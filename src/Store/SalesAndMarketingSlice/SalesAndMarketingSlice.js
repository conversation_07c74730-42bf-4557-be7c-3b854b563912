import { createSlice } from "@reduxjs/toolkit";

import {
  fetch_sales_add_form_enginee,
  fetch_sales_edit_form_enginee,
} from "../../Dashboard_API_calling_and_formatting/sales_API";
import { formEnginee_Formatee_Structure_and_State } from "../../Dashboard_API_calling_and_formatting/Form_Formate_and_API";
import { routes } from "../../Config/routesConfig";
import { salesAndMarketingTableConfig } from "../../Config/SalesAndMarketingTableConfig";

export const salesAndMarketingPageViewMode = {
  view: "View",
  add: "Addlead",
  edit: "Edit",
  delete: "Delete",
};

const initialState = {
  salesAndMarketingList: [],

  add: {
    structure: [],
    state: {},
  },

  edit: {
    structure: [],
    state: {},
    id: "",
  },

  filter: {
    state: {},
    date: {
      startDate: new Date(),
      endDate: new Date(),
      filter: {
        id: "Till now",
        content: "Till now",
      },
    },
  },

  pagination: {
    pageNo: 1,
    limit: 10,
  },

  headerData: {
    new: 0,
    inProgress: 0,
    quotationSent: 0,
    qualifies: 0,
    dropped: 0,
    total: 0,
    targetAchieved: 0,
  },

  headerDataSales: {
    Leads: 0,
    Closure: 0,
    Achieved: 0,
    Target: 0,
  },

  // Table Data
  table: {
    header: salesAndMarketingTableConfig,
  },

  // Analytics
  analytics: {
    startDate: new Date(),
    endDate: new Date(),
    filter: {
      id: "Till now",
      content: "Till now",
    },

    // -------------------
    salesPulse: {
      new: 0,
      potential: 0,
      dropped: 0,
      closed: 0,
    },

    leadSource: {
      "INDIVIDUAL RESIDENT": 0,
      RESIDENT: 0,
    },

    // lead Closue
    leadClosure: {
      leads: [],
      closure: [],
      date: [],
    },

    Walk_in_Status: {
      DNSP: 0,
      DBF: 0,
      DUC: 0,
      DC: 0,
    },

    top5: [],

    salesTrend: [],

    averageOrderValue: "",

    targetData: {
      targetAchieved: 0,
      pendingTargets: 0,
    },

    AOVvsDay: [],

    topSalesPerson: {
      name: [],
      totalQuantitySold: [],
      totalOrderValue: [],
    },

    topSellingProducts: {
      name: [],
      totalQuantity: [],
    },

    daysLeadClosure: 0,

    TargetsVsAchieved: {
      date: [],
      POTENTIAL: [],
      NEW: [],
    },

    // ------------------------
  },
};

const SalesAndMarkeingSlice = createSlice({
  name: "SALES_AND_MARKETING_SLICE_STATE_MANAGEMENT",
  initialState,
  reducers: {
    // table
    updateTableHeader(state, action) {
      return {
        ...state,
        table: {
          ...state.table,
          header: action.payload,
        },
      };
    },

    // Add
    updateStructureAndState(state, action) {
      return {
        ...state,
        add: {
          ...state.add,
          structure: action.payload?.structure,
          state: action.payload?.state,
        },
      };
    },

    updateState(state, action) {
      return {
        ...state,
        add: {
          ...state.add,
          state: action.payload,
        },
      };
    },

    //Edit
    updateEditStructureAndState(state, action) {
      return {
        ...state,
        edit: {
          ...state?.edit,
          structure: action.payload?.structure,
          state: action.payload?.state,
          id: action.payload?.id,
        },
      };
    },

    updateEditState(state, action) {
      return {
        ...state,
        edit: {
          ...state?.edit,
          state: action.payload,
        },
      };
    },

    // Filter
    updateFilterDate(state, action) {
      return {
        ...state,
        filter: {
          ...state.filter,
          date: {
            ...state.filter.date,
            ...action.payload,
          },
        },
      };
    },
    updateFilterState(state, action) {
      return { ...state, filter: { ...state.filter, state: action.payload } };
    },

    // Update Filter Array
    updateFilterArray(state, action) {
      return {
        ...state,
      };
    },

    // Update Header Data
    updateHeaderData(state, action) {
      return {
        ...state,
        headerData: {
          ...state.headerData,
          new: action.payload?.new,
          inProgress: action.payload.inProgress,
          quotationSent: action.payload?.quotationSent,
          qualifies: action.payload?.qualifies,
          dropped: action.payload?.dropped,
          total: action.payload?.total,
          targetAchieved: action.payload.targetAchieved,
        },
      };
    },

    // Update List
    updateSalesAndMarketingList(state, action) {
      return {
        ...state,
        salesAndMarketingList: action.payload,
      };
    },

    // Analytics
    updateAnalytics(state, action) {
      return {
        ...state,
        analytics: {
          ...state?.analytics,
          [action.payload?.key]: action?.payload?.value,
        },
      };
    },

    updateAnalyticsContent(state, action) {
      return {
        ...state,
        analytics: {
          ...state?.analytics,
          ...action.payload,
        },
      };
    },

    // Update HeaderDataSales
    updateHeaderDataSales(state, action) {
      return {
        ...state,
        headerDataSales: {
          ...action.payload,
        },
      };
    },
    //----------------
  },
});

export const SalesAndMarketingSliceActions = SalesAndMarkeingSlice.actions;
export default SalesAndMarkeingSlice;

// Add form
export const sales_load_addForm_thunk = () => {
  return async (dispatch) => {
    let response = await fetch_sales_add_form_enginee();
    let ss = formEnginee_Formatee_Structure_and_State(response);
    dispatch(SalesAndMarketingSliceActions.updateStructureAndState(ss));
  };
};

// edit form
export const sales_load_editform_thunk = (id, navigate = () => {}) => {
  return async (dispatch) => {
    let result = await fetch_sales_edit_form_enginee(id);
    let ss = formEnginee_Formatee_Structure_and_State(result);
    dispatch(
      SalesAndMarketingSliceActions.updateEditStructureAndState({
        ...ss,
        id: id,
      })
    );
    navigate(
      "?page=" + routes?.salesAndMarketingEditLead?.searchParams + "&id=" + id
    );
  };
};

export const dateFormatFunctionSalesAndMarketing = (dt) => {
  let date = new Date(dt);

  let fullyear = date.getFullYear();
  let month =
    date.getMonth() + 1 < 10
      ? "0" + (date.getMonth() + 1)
      : date.getMonth() + 1;

  let dateNO = date.getDate();

  let format = dateNO + "-" + month + "-" + fullyear;

  return format;
};

export const findTheListingData = (array, element = "name") => {
  let obj = {};

  for (let i = 0; i < array.length; i++) {
    let atp = array[i][element];
    if (obj[atp]) {
      obj[atp]++;
    } else {
      obj[atp] = 1;
    }
  }
  let returnObj = [];
  for (let item in obj) {
    returnObj.push({
      id: item,
      content: item,
    });
  }

  return returnObj;
};

export const salesAndMarketingHeaderDataFetch = (auth) => {
  return async (dispatch) => {
    return;
  };
};
