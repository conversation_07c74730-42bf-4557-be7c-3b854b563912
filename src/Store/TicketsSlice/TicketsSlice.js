import { createSlice } from "@reduxjs/toolkit";
import { API_ENDPOINTS, BaseURL, defaultHeaders } from "../../API/APIConfig";
import { GetAPI } from "../../API/GetAPI";

import { MasterSliceActions } from "../MasterSlice/MasterSlice";
import { TicketingTableConfig } from "../../Config/TicketingConfig";
import { UserMainSliceActions } from "../UserSlices/UserMainSlice";

const initialState = {
  userList: [],
  raisedByList: [],
  raisedToList: [],

  filter: {
    date: {
      startDate: new Date(),
      endDate: new Date(),
      filter: {
        id: "Till now",
        content: "Till now",
      },
    },
    state: {
      status: [],
    },
  },

  add: {
    structure: [],
    state: {},
  },

  edit: {
    structure: [],
    state: {},
    id: "",
  },

  table: {
    header: TicketingTableConfig,
  },

  headerCount: [
    { title: "Tickets", count: "0" },
    { title: "Open", count: "0" },
    { title: "In Progress", count: "0" },
    { title: "Closed", count: "0" },
  ],
};

const TicketsSlice = createSlice({
  name: "TICKETS_STATE_MANAGMENT",
  initialState,
  reducers: {
    //Filter Assignee
    updateFilterAssignee(state, action) {
      return {
        ...state,
        filter: {
          ...state.filter,
          assignee: action.payload,
        },
      };
    },
    //Filter Department
    updateFilterDepartment(state, action) {
      return {
        ...state,
        filter: {
          ...state.filter,
          department: action.payload,
        },
      };
    },

    // Filter
    updateFilterDate(state, action) {
      return {
        ...state,
        filter: {
          ...state.filter,
          date: {
            ...state.filter.date,
            ...action.payload,
          },
        },
      };
    },
    updateFilterState(state, action) {
      return { ...state, filter: { ...state.filter, state: action.payload } };
    },

    // raised by list
    updateRaiseByList(state, action) {
      return {
        ...state,
        raisedByList: action.payload,
      };
    },

    // raised to list
    updateRaisedToList(state, action) {
      return {
        ...state,
        raisedToList: action.payload,
      };
    },
    updateUserList(state, action) {
      return {
        ...state,
        userList: action.payload,
      };
    },

    // Update Add STructure
    updateAddStructure(state, action) {
      return {
        ...state,
        add: {
          ...state.add,
          structure: action.payload.structure,
          state: action.payload?.state,
        },
      };
    },

    //Edit
    updateEditStructureAndState(state, action) {
      return {
        ...state,
        edit: {
          ...state?.edit,
          structure: action.payload?.structure,
          state: action.payload?.state,
          id: action.payload?.id,
        },
      };
    },

    updateEditState(state, action) {
      return {
        ...state,
        edit: {
          ...state?.edit,
          state: action.payload,
        },
      };
    },

    // Header Count
    updateHeaderCount(state, action) {
      return { ...state, headerCount: action?.payload };
    },
  },
});

export default TicketsSlice;
export const TicketsSliceActions = TicketsSlice.actions;

export const userListFetchHelperFunction = async (page = 1, limit = 5000) => {
  let url = new URL(
    BaseURL.mainURl + API_ENDPOINTS.fetchuserlistWithoutAdminToken
  );

  url.searchParams.set("page", page);
  url.searchParams.set("limit", limit);

  const headers = {
    ...defaultHeaders,
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": ["POST", "GET", "OPTIONS", "DELETE", "PUT"],
    "Access-Control-Allow-Headers": [
      "append",
      "delete",
      "entries",
      "foreach",
      "get",
      "has",
      "keys",
      "set",
      "values",
      "Authorization",
    ],

    redirect: "follow",
  };

  const response = await GetAPI(url, headers);

  if (response?.ok) {
    let responsedata = await response?.json();
    let user = responsedata?.users;

    if (user) {
      let userarray = [];
      for (let i = 0; i < user?.length; i++) {
        let cont = user[i];

        let obj = {
          id: cont._id,
          username: cont.username,
          mobilephone: cont.mobile,
          email: cont.email,
        };
        userarray.push(obj);
      }
      let userList = [];
      for (let userObj of userarray) {
        userList.push({
          id: userObj.id,
          content: userObj.username,
          email: userObj?.email,
        });
      }

      return userList;
    }
  }

  return [];
};

export const userListFetch = (auth) => {
  return async (dispatch) => {
    let userList = await userListFetchHelperFunction();
    dispatch(TicketsSliceActions.updateUserList(userList));
    dispatch(MasterSliceActions.updateUserList(userList));
    // dispatch(UserMainSliceActions.updateUserList(userList));
  };
};
