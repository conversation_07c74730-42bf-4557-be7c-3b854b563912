import { createSlice } from "@reduxjs/toolkit";
import { fetch_serviceComplaint_add_form_enginee } from "../../Dashboard_API_calling_and_formatting/ServiceComplaints_API";
import { formEnginee_Formatee_Structure_and_State } from "../../Dashboard_API_calling_and_formatting/Form_Formate_and_API";
import { routes } from "../../Config/routesConfig";

const initialState = {
  serviceComplaintsList: [],

  headerCount: [
    { title: "Total", count: "0" },
    { title: "New", count: "0" },
    { title: "In Process", count: "0" },
    { title: "Resolved", count: "0" },
  ],

  add: {
    structure: [],
    state: {},
  },

  edit: {
    structure: [],
    state: {},
    id: "",
  },

  filter: {
    date: {
      startDate: new Date(),
      endDate: new Date(),
      filter: {
        id: "Till now",
        content: "Till now",
      },
    },
    state: {
      complaint_status: [],
    },
  },

  ui: {
    addComplaintButtonIsLoading: false,
  },
};

const ServiceComplaintsSlice = createSlice({
  name: "SERVICE_COMPLAINTS_SLICE_STATE_MANAGEMENT",
  initialState,
  reducers: {
    // Update List
    updateServiceComplaintsList(state, action) {
      return { ...state, serviceComplaintsList: action.payload };
    },

    // Add
    updateAddStructureAndState(state, action) {
      return {
        ...state,
        add: {
          ...state.add,
          structure: action.payload?.structure,
          state: action?.payload?.state,
        },
      };
    },

    updateState(state, action) {
      return {
        ...state,
        add: {
          ...state.add,
          state: action.payload,
        },
      };
    },

    // Filter
    updateFilterDate(state, action) {
      return {
        ...state,
        filter: {
          ...state.filter,
          date: {
            ...state.filter.date,
            ...action.payload,
          },
        },
      };
    },
    updateFilterState(state, action) {
      return { ...state, filter: { ...state.filter, state: action.payload } };
    },

    //Edit
    updateEditStructureAndState(state, action) {
      return {
        ...state,
        edit: {
          ...state?.edit,
          structure: action.payload?.structure,
          state: action.payload?.state,
          id: action.payload?.id,
        },
      };
    },

    updateEditState(state, action) {
      return {
        ...state,
        edit: {
          ...state?.edit,
          state: action.payload,
        },
      };
    },

    // Header Count Update
    updateHeaderCount(state, action) {
      return { ...state, headerCount: action.payload };
    },

    //--------------------------------------------------
  },
});

export const ServiceComplaintsSliceActions = ServiceComplaintsSlice.actions;
export default ServiceComplaintsSlice;

export const serviceComplaint_add_form = (navigate = () => {}) => {
  return async (dispatch) => {
    let structure = await fetch_serviceComplaint_add_form_enginee();
    let ss = formEnginee_Formatee_Structure_and_State(structure);

    dispatch(ServiceComplaintsSliceActions?.updateAddStructureAndState(ss));

    navigate(routes.servicecomplaintsAddNew.relativeLink);
  };
};
