import { createSlice } from "@reduxjs/toolkit";

import { formEnginee_Formatee_Structure_and_State } from "../../Dashboard_API_calling_and_formatting/Form_Formate_and_API";
import { routes } from "../../Config/routesConfig";
import { API_ENDPOINTS, api_headers, BaseURL } from "../../API/APIConfig";
import { GetAPI } from "../../API/GetAPI";

const initialState = {
  list: [],

  add: {
    structure: [],
    state: {},
  },

  filter: {
    state: {},
    date: {
      startDate: new Date(),
      endDate: new Date(),
      filter: {
        id: "Till now",
        content: "Till now",
      },
    },
  },
};

const NetworkingSlice = createSlice({
  name: "NETWORKING_SLICE_STATE_MANAGEMENT",
  initialState,
  reducers: {
    updateAdd(state, action) {
      return {
        ...state,
        add: {
          ...state.add,
          structure: action?.payload?.structure,
          state: action.payload?.state,
        },
      };
    },

    updateAddState(state, action) {
      return {
        ...state,
        add: {
          ...state.add,
          state: action.payload,
        },
      };
    },

    updateList(state, action) {
      return {
        ...state,
        list: action.payload,
      };
    },

    // Update Filter  State
    updateFilterDate(state, action) {
      return {
        ...state,
        filter: {
          ...state.filter,
          date: {
            ...state.filter.date,
            ...action.payload,
          },
        },
      };
    },
    updateFilterState(state, action) {
      return {
        ...state,
        filter: {
          ...state.filter,
          state: action.payload,
        },
      };
    },
    // -------------------------------
  },
});

export default NetworkingSlice;
export const NetworkingSliceActions = NetworkingSlice?.actions;

export const networking_Load_addForm_datat = (navigate = () => {}) => {
  return async (dispatch) => {
    let formEngineeData = await fetch_Networking_add_form_enginee();
    let ss = formEnginee_Formatee_Structure_and_State(formEngineeData);
    dispatch(NetworkingSliceActions.updateAdd(ss));
    navigate(routes?.networkingadd.directLink);
  };
};

export const fetch_Networking_add_form_enginee = async () => {
  let url = new URL(BaseURL?.mainURl + API_ENDPOINTS?.networkingAddFormBuilder);

  let headers = { ...api_headers };

  let response = await GetAPI(url, headers);

  if (response?.ok) {
    let responseData = await response?.json();

    return responseData?.structure;
  }
  return [
    {
      type: "text",
      required: true,
      key: "Name",
      defaultValue: "",
      placeholder: "Enter a name",
    },
    {
      type: "email",
      required: true,
      key: "Email",
      defaultValue: "",
      placeholder: "Enter a email",
    },
    {
      type: "number",
      required: true,
      key: "Phone_number",
      defaultValue: "",
      placeholder: "Enter a phone number ",
    },
    {
      type: "text",
      required: true,
      key: "Location",
      defaultValue: "",
      placeholder: "Enter a location ",
    },
    {
      type: "date",
      required: true,
      key: "Date_Of_Birth",
      title: "Date Of Birth",
      defaultValue: new Date(),
      format: "format",
    },
    {
      type: "text",
      required: true,
      key: "Relationship_Status",
      defaultValue: "",
      placeholder: "Enter a Relationship Status ",
    },
    {
      type: "date",
      required: true,
      key: "Anvisory_Date",
      title: "Anvisory Date",
      defaultValue: new Date(),
      format: "format",
    },
    {
      type: "singleselect",
      required: true,
      key: "Occupation",
      options: ["Engineer", "Architect", "Plumber", "Layer"],
      placeholder: "Occupation ",
      defaultValue: "Occupation ",
    },
    {
      type: "text",
      required: true,
      key: "form_name",
      defaultValue: "",
      placeholder: "Enter a Firm Name",
    },
    // {
    //   type: "text",
    //   required: true,
    //   key: "Employee_id",
    //   defaultValue: "",
    //   placeholder: "Enter a Employee Id ",
    // },
    // {
    //   type: "text",
    //   required: true,
    //   key: "Qualification",
    //   defaultValue: "",
    //   placeholder: "Enter a Qualification ",
    // },
  ];
};
