import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  pageNo: 1,
  userList: [],

  delete: {
    id: "",
    deleteButtonIsLoading: false,
  },
};

const UserMainSlice = createSlice({
  name: "USER_MAIN_SLICE_STATE_MANAGEMENT",
  initialState,
  reducers: {
    updatePageNo(state, action) {
      return {
        ...state,
        pageNo: action.payload,
      };
    },
    updateUserList(state, action) {
      return {
        ...state,
        userList: action.payload,
      };
    },

    updateDeleteId(state, action) {
      return {
        ...state,
        delete: {
          ...state.delete,
          id: action.payload,
        },
      };
    },
    updateDeleteButtonIsLoading(state, action) {
      return {
        ...state,
        delete: {
          ...state.delete,
          deleteButtonIsLoading: action.payload,
        },
      };
    },
  },
});

export const UserMainSliceActions = UserMainSlice.actions;
export default UserMainSlice;

export const PaginationOfUserList = (page, token) => {
  return async (dispatch) => {
    dispatch(UserMainSliceActions.updatePageNo(page));
  };
};

export const modifyingUserAccessForUserList = (array) => {};

export const userDeleteThunk = (id, token) => {};
