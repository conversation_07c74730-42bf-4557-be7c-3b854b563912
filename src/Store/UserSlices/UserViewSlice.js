import { createSlice } from "@reduxjs/toolkit";

export const userViewPageMode = {
     userDetails: "UserDetails",
     permission: "permission",
};

const initialState = {
     permissionValues: {
          leads: [true, true, true, false],
          customers: [false, false, false, true],
          orderDetails: [false, true, true, false],
          products: [true, true, true, true],
          reports: [false, false, false, false],
     },

     content: {
          username: "",
          email: "",
          mobilephone: "",
     },
     ui: {
          modePage: userViewPageMode.permission,
     },
};

const userViewSlice = createSlice({
     name: "User_View_state_Management",
     initialState,
     reducers: {
          modifyUserViewModePage(state, action) {
               return {
                    ...state,
                    ui: {
                         ...state.ui,
                         modePage: action.payload,
                    },
               };
          },

          //updation
          updateTheContent(state, action) {
               return {
                    ...state,
                    content: {
                         ...state.content,
                         username: action.payload.username,
                         email: action.payload.email,
                         mobilephone: action.payload.mobilephone,
                    },
               };
          },

          updateThePermissionValues(state, action) {
               return {
                    ...state,
                    permissionValues: action.payload,
               };
          },

          resetContentPermissionValues(state, action) {
               return {
                    ...state,
                    content: initialState.content,
                    permissionValues: initialState.permissionValues,
               };
          },
     },
});

export const userViewSliceActions = userViewSlice.actions;
export default userViewSlice;
