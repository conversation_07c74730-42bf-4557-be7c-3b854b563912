import { createSlice, current } from "@reduxjs/toolkit";
import { UserPermissionTableConfig } from "../../Config/UserConfig";

const initialState = {
     permissionValues: {
          leads: [false, false, false, false],
          customers: [false, false, false, false],
          orderDetails: [false, false, false, false],
          products: [false, false, false, false],
          reports: [false, false, false, false],
     },
};

const UserInviteUserSlice = createSlice({
     name: "UserInviteUser_state_management",
     initialState,
     reducers: {
          permissionValuesChange(state, action) {
               let index = action.payload.index;
               let title = action.payload.title;
               let currentState = { ...current(state) };

               if (title === UserPermissionTableConfig.leads) {
                    let array = [...currentState.permissionValues.leads];
                    array[index] = !array[index];
                    return {
                         ...state,
                         permissionValues: {
                              ...state.permissionValues,
                              leads: array,
                         },
                    };
               } else if (title === UserPermissionTableConfig.customers) {
                    let array = [...currentState.permissionValues.customers];
                    array[index] = !array[index];
                    return {
                         ...state,
                         permissionValues: {
                              ...state.permissionValues,
                              customers: array,
                         },
                    };
               } else if (title === UserPermissionTableConfig.orderDetails) {
                    let array = [...currentState.permissionValues.orderDetails];
                    array[index] = !array[index];
                    return {
                         ...state,
                         permissionValues: {
                              ...state.permissionValues,
                              orderDetails: array,
                         },
                    };
               } else if (title === UserPermissionTableConfig.products) {
                    let array = [...currentState.permissionValues.products];
                    array[index] = !array[index];
                    return {
                         ...state,
                         permissionValues: {
                              ...state.permissionValues,
                              products: array,
                         },
                    };
               } else if (title === UserPermissionTableConfig.reports) {
                    let array = [...currentState.permissionValues.reports];
                    array[index] = !array[index];
                    return {
                         ...state,
                         permissionValues: {
                              ...state.permissionValues,
                              reports: array,
                         },
                    };
               }
               return {
                    ...state,
               };
          },
     },
});

export const UserInviteUserSliceActions = UserInviteUserSlice.actions;
export default UserInviteUserSlice;
