import { createSlice } from "@reduxjs/toolkit";

import { PostAPI } from "../../API/PostAPI";
import { API_ENDPOINTS, BaseURL, defaultHeaders } from "../../API/APIConfig";

export const userEditPageModes = {
  permission: "Permission",
  userDetails: "UserDetails",
};

const initialState = {
  userDetails: {
    username: "",
    id: "",
  },

  permissionValues: {
    users: [false, false, false, false, false],
    purchase: [false, false, false, false, false],
    sales: [false, false, false, false, false],
    marketing: [false, false, false, false, false],
    orders: [false, false, false, false, false],
    customers: [false, false, false, false, false],
    networking: [false, false, false, false, false],
    serviceComplaints: [false, false, false, false, false],
    tickets: [false, false, false, false, false],
    taskManagement: [false, false, false, false, false],
  },
};

const userEditSlice = createSlice({
  name: "User_Edit_State_Management",
  initialState,
  reducers: {
    // initialize Permisson
    updatePermissionValue(state, action) {
      return {
        ...state,
        permissionValues: action.payload,
      };
    },

    // Update User Details
    updateUserDetails(state, action) {
      return {
        ...state,
        userDetails: action.payload,
      };
    },

    // Reset Content
    resetContentPermissionValues(state, action) {
      return {
        ...state,
        content: initialState.content,
        permissionValues: initialState.permissionValues,
      };
    },
  },
});

export const userEditSliceActions = userEditSlice.actions;
export default userEditSlice;

export const userUpdateAccessThunk = () => {
  return async (dispatch) => {};
};

export const updateAccess = async (token, id, title, array) => {
  let url = new URL(
    BaseURL?.mainURl + API_ENDPOINTS?.updateadminaccess + "/" + id
  );

  let bodyObj = {
    product: title,
    access_data: {
      view: array[0],
      add: array[1],
      update: array[2],
      deletes: array[3],
    },
  };

  let headers = {
    ...defaultHeaders,
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": ["POST", "GET", "OPTIONS", "DELETE", "PUT"],
    "Access-Control-Allow-Headers": [
      "append",
      "delete",
      "entries",
      "foreach",
      "get",
      "has",
      "keys",
      "set",
      "values",
      "Authorization",
    ],

    redirect: "follow",

    Authorization: token,
  };

  const response = await PostAPI(url, bodyObj, headers);

  return response;
};
