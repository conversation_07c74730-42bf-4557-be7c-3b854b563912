import { createSlice } from "@reduxjs/toolkit";
import { API_ENDPOINTS, BaseURL, defaultHeaders } from "../../API/APIConfig";
import { GetAPI } from "../../API/GetAPI";
import { PostAPI } from "../../API/PostAPI";

import { fetch_order_headercount_and_list } from "../../Dashboard_API_calling_and_formatting/order_apis";

const initialState = {
  headerData: [
    { title: "Total", count: "0" },
    { title: "Full Orders", count: "0" },
    { title: "Partial Orders", count: "0" },
    {
      title: "No of line item",
      count: "0",
    },
    {
      title: "Value of the line item",
      count: "0",
    },
    { title: "Available", ccount: "0" },
    { title: "Unavailable", count: "0" },
    { title: "A", count: "0" },
    { title: "AO", count: "0" },
  ],

  orderListing: [],

  filter: {
    date: {
      startDate: new Date(),
      endDate: new Date(),
      filter: {
        id: "Till now",
        content: "Till now",
      },
    },
    state: {},
  },
  filterArray: {
    orderTotalArray: [],
    salesPersonArray: [],
    locationArray: [],
    orderStatusArray: [],
  },

  // Analytics
  analytics: {
    startDate: new Date(),
    endDate: new Date(),
    filter: {
      id: "Till now",
      content: "Till now",
    },

    //--------------
    orderOverview: {
      Total_orders: 0,
      delivered: 0,
      readyForDelivery: 0,
      pendingForStock: 0,
      damaged: 0,
    },

    totalOrderValueVsPaymentReceived: {
      totalPaymentReceived: 0,
      totalOrderValue: 0,
      paymentOutstanding: 0,
    },

    averageDeliveryTime: 0,

    commitment: {
      commitmentsHonored: 0,
      commitmentsDelayed: 0,
    },

    Orderavailableoutofstock: {
      date: [],
      orderCount: [],
      Available: [],
      out_of_stock: [],
    },

    orderByLocation: {
      location: [],
      count: [],
    },

    productVsQuantity: {
      id: [],
      totalQuantity: [],
    },

    OrderSalespersonProductSoldTotalOrder: {
      id: [],
      totalQuantity: [],
      totalOrder: [],
    },

    SalespersonProductSoldTotalPaymentReceived: {
      id: [],
      totalQuantity: [],
      Total_Payment_Received: [],
    },

    brandsValue: {
      brand: [],
      value: [],
    },
  },
};

const OrderManagementSlice = createSlice({
  name: "ORDER_MANAGMENT_SLICE_STATE_MANAGMENT",
  initialState,
  reducers: {
    updateHeaderData(state, action) {
      return {
        ...state,
        headerData: action.payload,
      };
    },

    updateOrderListing(state, action) {
      return {
        ...state,
        orderListing: action.payload,
      };
    },

    //--------------------------------------
    // Analytics
    updateAnalytics(state, action) {
      return {
        ...state,
        analytics: {
          ...state?.analytics,
          [action.payload?.key]: action?.payload?.value,
        },
      };
    },

    updateAnalyticsContent(state, action) {
      return {
        ...state,
        analytics: {
          ...state?.analytics,
          ...action.payload,
        },
      };
    },
    //--------------------------------------

    // Update Filter  State
    updateFilterDate(state, action) {
      return {
        ...state,
        filter: {
          ...state.filter,
          date: {
            ...state.filter.date,
            ...action.payload,
          },
        },
      };
    },
    updateFilterState(state, action) {
      return {
        ...state,
        filter: {
          ...state.filter,
          state: action.payload,
        },
      };
    },
  },
});

export const OrderManagementSliceActions = OrderManagementSlice.actions;
export default OrderManagementSlice;

export const order_list_headerCount_thunk = () => {
  return async (dispatch) => {
    let response = await fetch_order_headercount_and_list();

    if (response) {
      let headerData = response?.headerData;
      dispatch(OrderManagementSliceActions.updateHeaderData(headerData));
    }
  };
};

const orderListDataFormatHelper = (data) => {
  let obj = {
    id: data?._id,
    orderId: data?.order_id,
    name: data?.Name,
    customerId: data?.customer_id,
    orderDate: data?.order_date,
    productDetails: data?.product_details,
    batchNo: data?.batch_no,
    quantity: data?.quantity,
    stockAvailablity: data?.stock_availability,
    unavailableQuantity: data?.unavailable_quantity,
    damagedProducts: data?.eta_damaged_stock,
    ETA: data?.eta_unavailable_stock,
    orderTotal: data?.order_total,
    salesPerson: data?.sales_person,
    deliveryDate: data?.delivery_date,
    orderStatus: data?.order_status,
    location: data?.location,
    paymentReceived: data?.payment_received,
    brand: data?.Brand,
  };

  return obj;
};

const orderListFormatHelper = (dataArray) => {
  let array = [];

  if (dataArray?.length === 0) return [];

  for (let i = 0; i < dataArray.length; i++) {
    let currentindex = dataArray[i];
    let obj = {
      id: currentindex?._id,
      orderId: currentindex?.order_id,
      name: currentindex?.Name,
      customerId: currentindex?.customer_id,
      orderDate: currentindex?.order_date,
      productDetails: currentindex?.product_details,
      batchNo: currentindex?.batch_no,
      quantity: currentindex?.quantity,
      stockAvailablity: currentindex?.stock_availability,
      unavailableQuantity: currentindex?.unavailable_quantity,
      damagedProducts: currentindex?.damaged_product,
      ETA: currentindex?.eta_unavailable_stock,
      orderTotal: currentindex?.order_total,
      salesPerson: currentindex?.sales_person,
      deliveryDate: currentindex?.delivery_date,
      orderStatus: currentindex?.order_status,
      location: currentindex?.location,
      paymentReceived: currentindex?.payment_received,
      brand: currentindex?.Brand,
    };
    array.push(obj);
  }

  return array;
};

const orderListFormat = async (responseData) => {
  let data = responseData.data;
  let orders = data.orders;

  let array = [];

  if (orders?.length === 0) return [];

  for (let i = 0; i < orders?.length; i++) {
    let currentindex = orders[i];
    let data = currentindex.data;
    let ordersList = data?.orders;

    ordersList = ordersList?.length === 0 ? ordersList : [];

    let dataFormat = orderListDataFormatHelper(data);
    let subArray = orderListFormatHelper(data?.orders);

    dataFormat = {
      ...dataFormat,
      subArray,
    };

    array.push(dataFormat);
  }
  return array;
};

const fetchOrderList = async () => {
  let url = new URL(BaseURL.mainURl + API_ENDPOINTS.orderListing);

  let header = {
    ...defaultHeaders,
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": ["POST", "GET", "OPTIONS", "DELETE", "PUT"],
    "Access-Control-Allow-Headers": [
      "append",
      "delete",
      "entries",
      "foreach",
      "get",
      "has",
      "keys",
      "set",
      "values",
      "Authorization",
    ],

    redirect: "follow",
  };

  let response = await GetAPI(url, header);

  if (response?.ok) {
    let responseData = await response.json();

    let array = await orderListFormat(responseData);

    let obj = {
      total: responseData?.Total,
      full_orders: responseData?.full_orders,
      partial_orders: responseData?.partial_orders,
      no_of_line_item: responseData?.No_of_line_item,
      value_of_the_line_item: responseData?.value_of_the_line_item,
      available: responseData?.Available,
      unavailable: responseData?.unavailable,
      a: responseData.A,
      aO: responseData?.AO,
    };

    return { array: array, headerData: obj };
  }

  let obj = {
    total: 0,
    full_orders: 0,
    partial_orders: 0,
    no_of_line_item: 0,
    value_of_the_line_item: 0,
    available: 0,
    unavailable: 0,
    a: 0,
    aO: 0,
  };

  return { array: [], headerData: obj };
};

export const orderListingFetchThunk = () => {
  return async (dispatch) => {
    let response = await fetchOrderList();

    dispatch(OrderManagementSliceActions.updateOrderListing(response.array));
  };
};

export const orderFilterListFetchThunk = () => {
  return async (dispatch) => {};
};

export const arrayFormat = (array) => {
  let returnarray = [];

  for (let i = 0; i < array.length; i++) {
    returnarray.push(array[i].content);
  }
  return returnarray;
};

export const orderListFilterFetchThunk = (filter) => {
  return async (dispatch) => {
    let url = new URL(BaseURL.mainURl + API_ENDPOINTS.orderFilterList);

    let header = {
      ...defaultHeaders,
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": [
        "POST",
        "GET",
        "OPTIONS",
        "DELETE",
        "PUT",
      ],
      "Access-Control-Allow-Headers": [
        "append",
        "delete",
        "entries",
        "foreach",
        "get",
        "has",
        "keys",
        "set",
        "values",
        "Authorization",
      ],

      redirect: "follow",
    };

    // let salesPerson = filter?.salesPerson;
    // let orderStatus = filter?.orderStatus;
    // let location = filter?.location;
    // let orderTotal = filter.orderTotal;
    // let damagedProduct = filter?.damagedProduct;
    // let stockAvailablity = filter.stockAvailablity;
    // damagedProduct = damagedProduct.map((item) => {
    //   if (damagedProduct_DamagedOption.content === item.content) return true;
    //   if (damagedProduct_NotDamagedOption.content === item.content)
    //     return false;
    //   return false;
    // });
    // let obj = {};

    // if (salesPerson.length) {
    //   obj = { ...obj, sales_person: arrayFormat(salesPerson) };
    // }
    // if (orderStatus.length) {
    //   obj = { ...obj, order_status: arrayFormat(orderStatus) };
    // }
    // if (location.length) {
    //   obj = { ...obj, location: arrayFormat(location) };
    // }
    // if (orderTotal.length) {
    //   obj = { ...obj, order_total: arrayFormat(orderTotal) };
    // }
    // if (damagedProduct.length) {
    //   obj = {
    //     ...obj,
    //     damaged_product: damagedProduct,
    //   };
    // }
    // if (stockAvailablity.length) {
    //   obj = {
    //     ...obj,
    //     stock_availability: arrayFormat(stockAvailablity),
    //   };
    // }

    let body = filter;

    let response = await PostAPI(url, body, header, "POST");

    if (response?.ok) {
      let responseData = await response.json();

      let data = responseData?.filteredOrders;

      let array = [];

      for (let i = 0; i < data.length; i++) {
        let currentindex = data[i];
        let obj = {
          id: currentindex?._id,
          orderId: currentindex?.order_id,
          customerId: currentindex?.customer_id,
          orderDate: currentindex?.order_date,
          productDetails: currentindex?.product_details,
          batchNo: currentindex?.batch_no,
          quantity: currentindex?.quantity,
          stockAvailablity: currentindex?.stock_availability,
          unavailableQuantity: currentindex?.unavailable_quantity,
          damagedProducts: currentindex?.damaged_product,
          ETA: currentindex?.eta_unavailable_stock,
          orderTotal: currentindex?.order_total,
          salesPerson: currentindex?.sales_person,
          deliveryDate: currentindex?.delivery_date,
          orderStatus: currentindex?.order_status,
          location: currentindex?.location,
        };

        array.push(obj);
      }

      dispatch(OrderManagementSliceActions.updateOrderListing(array));
    }
  };
};
