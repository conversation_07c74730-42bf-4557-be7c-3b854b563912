import { createSlice } from "@reduxjs/toolkit";
import { API_ENDPOINTS, BaseURL, defaultHeaders } from "../../API/APIConfig";
import { GetAPI } from "../../API/GetAPI";
import { PostAPI } from "../../API/PostAPI";
import { fetch_taskManagement_add_form } from "../../Dashboard_API_calling_and_formatting/taskmanagement_API";
import { formEnginee_Formatee_Structure_and_State } from "../../Dashboard_API_calling_and_formatting/Form_Formate_and_API";
import { routes } from "../../Config/routesConfig";

// Modes in the taskmanagement dashboard
// List view and grid view
export const mode_taskManagement = {
  list: "List",
  grid: "grid",
};



const initialState = {
  list: [],

  add: {
    structure: [],
    state: {},
  },

  filter: {
    status: [],
    department: [],
    project: [],
    assingee: [],
  },

  ui: {
    mode: mode_taskManagement.list,
  },
};

const taskManagementSlice = createSlice({
  name: "TASK_MANAGEMENT_SLICE",
  initialState,
  reducers: {
    // Updating List
    updateList(state, action) {
      return {
        ...state,
        list: action.payload,
      };
    },

    // add
    updateAddStructureAndState(state, action) {
      return {
        ...state,
        add: {
          ...state.add,
          structure: action?.payload?.structure,
          state: action?.payload?.state,
        },
      };
    },

    updateAddState(state, action) {
      return {
        ...state,
        add: {
          ...state.add,
          state: action?.payload,
        },
      };
    },

    // Updating UI -> Mode
    updateUIMode(state, action) {
      return {
        ...state,
        ui: {
          ...state.ui,
          mode: action.payload,
        },
      };
    },

    // Update Filter
    updateFilterStatus(state, action) {
      return {
        ...state,
        filter: {
          ...state.filter,
          status: action.payload,
        },
      };
    },
    updateFilterDepartment(state, action) {
      return {
        ...state,
        filter: {
          ...state.filter,
          department: action.payload,
        },
      };
    },
    updateFilterProject(state, action) {
      return {
        ...state,
        filter: {
          ...state.filter,
          project: action.payload,
        },
      };
    },
    updateFilterAssingee(state, action) {
      return {
        ...state,
        filter: {
          ...state.filter,
          assingee: action.payload,
        },
      };
    },
  },
});

export const taskManagementSliceActions = taskManagementSlice.actions;
export default taskManagementSlice;

export const taskManagement_add_form = (navigate = () => {}) => {
  return async (dispatch) => {
    let structure = await fetch_taskManagement_add_form();
    let ss = formEnginee_Formatee_Structure_and_State(structure);

    dispatch(taskManagementSliceActions?.updateAddStructureAndState(ss));

    navigate(routes.taskManagementAdd.directLink);
  };
};

export let taskManagementList_format = (list = []) => {
  let array = [];

  for (let i = 0; i < list?.length; i++) {
    let currentObj = list[i];
    let obj = {
      id: currentObj?._id,
      taskDescription: currentObj?.Task_Description,
      department: currentObj?.Department,
      project: currentObj?.Project,
      assignee: currentObj?.Assignee,
      assignedBy: currentObj?.Assigned_By,
      dueDate: currentObj?.Dead_line_date,
      status: currentObj?.Status,
      completedDate: currentObj?.Completed_Date,
      createdAt: currentObj?.createdAt,
    };
    array.push(obj);
  }

  return array;
};

// fetch list
export const fetchListTaskmanagement = async () => {
  let url = new URL(BaseURL.mainURl + API_ENDPOINTS?.taskManagementList);

  const headers = {
    ...defaultHeaders,
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": ["POST", "GET", "OPTIONS", "DELETE", "PUT"],
    "Access-Control-Allow-Headers": [
      "append",
      "delete",
      "entries",
      "foreach",
      "get",
      "has",
      "keys",
      "set",
      "values",
      "Authorization",
    ],

    redirect: "follow",

    // Authorization: auth.token,
  };

  let response = await GetAPI(url, headers);

  if (response?.ok) {
    let responseDate = await response.json();
    let list = responseDate?.list;
    let array_formatted = taskManagementList_format(list);
    return array_formatted;
  }

  return [];
};

export const fetch_list_taskmanagement = () => {
  return async (dispatch) => {
    let array_formatted = await fetchListTaskmanagement();
    dispatch(taskManagementSliceActions.updateList(array_formatted));
  };
};

// Fetch list with filter
export const fetchListWithFilter_taskmanagement = async (obj) => {
  const url = new URL(BaseURL.mainURl + API_ENDPOINTS?.taskManagementFilter);

  const headers = {
    ...defaultHeaders,
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": ["POST", "GET", "OPTIONS", "DELETE", "PUT"],
    "Access-Control-Allow-Headers": [
      "append",
      "delete",
      "entries",
      "foreach",
      "get",
      "has",
      "keys",
      "set",
      "values",
      "Authorization",
    ],

    redirect: "follow",

    // Authorization: auth.token,
  };

  const response = await PostAPI(url, obj, headers);

  if (response?.ok) {
    let responseDate = await response.json();
    let filteredOrders = responseDate?.filteredOrders;

    let array_formatted = taskManagementList_format(filteredOrders);
    return array_formatted;
  }

  return [];
};

// Middle ware of filter
export const taskmanagement_filter_middleware = (filter) => {
  return (dispatch) => {
    let obj = {};

    if (filter?.status?.length !== 0) {
      obj = {
        ...obj,
        Status: filter.status?.map((item) => item.id),
      };
    }

    if (filter.department?.length !== 0) {
      obj = {
        ...obj,
        Department: filter.department?.map((item) => item.id),
      };
    }

    if (filter?.project?.length !== 0) {
      obj = {
        ...obj,
        Project: filter?.project?.map((item) => item.id),
      };
    }

    if (filter?.assingee?.length !== 0) {
      obj = {
        ...obj,
        Assignee: filter?.assingee?.map((item) => item?.id),
      };
    }


    if (obj) {
      dispatch(taskmanagement_filter_Thunk(obj));
    } else {
      dispatch(fetch_list_taskmanagement());
    }
  };
};

export const taskmanagement_filter_Thunk = (obj) => {
  return async (dispatch) => {
    let array_formatted = await fetchListWithFilter_taskmanagement(obj);
    dispatch(taskManagementSliceActions.updateList(array_formatted));
  };
};
