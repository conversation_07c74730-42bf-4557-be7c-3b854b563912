import { createSlice, current } from "@reduxjs/toolkit";

import {
  fetchListTaskmanagement,
  fetchListWithFilter_taskmanagement,
} from "./taskManagementSlice";

function getStartOfWeek(date) {
  // Clone the date object to avoid modifying the original one
  const startDate = new Date(date);

  // Get the current day of the week (0 = Sunday, 1 = Monday, ..., 6 = Saturday)
  const dayOfWeek = startDate.getDay();

  // Calculate the difference between the current day and the start of the week (Sunday)
  const diff = startDate.getDate() - dayOfWeek;

  // Set the date to the start of the week
  startDate.setDate(diff);

  // Reset the time to the beginning of the day
  startDate.setHours(0, 0, 0, 0);

  return startDate;
}

function getAdjacentDay(date, direction) {
  // Clone the date object to avoid modifying the original one
  const newDate = new Date(date);

  // Determine the adjustment based on the direction
  const adjustment = direction?.toLowerCase() === "next" ? 1 : -1;

  // Set the date to the adjacent day
  newDate.setDate(newDate.getDate() + adjustment);

  return newDate;
}

const inital_week_setup = (dat = new Date()) => {
  let currentDate = new Date(dat);

  let startDay_of_currentweek = getStartOfWeek(currentDate);

  let array = [
    {
      date: startDay_of_currentweek,
      list: [],
    },
  ];

  let temp = new Date(startDay_of_currentweek);
  for (let i = 1; i <= 6; i++) {
    let next_day = getAdjacentDay(temp, "next");
    temp = next_day;
    array.push({
      date: temp,
      list: [],
    });
  }

  return array;
};

const initialState = {
  list: [],
  gridList: inital_week_setup(),

  filter: {
    status: [],
  },
};

// Updating the grid list with task list ;
function isSameDay(date1, date2) {
  return (
    date1.getFullYear() === date2.getFullYear() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getDate() === date2.getDate()
  );
}

const updateGridList = (gridList, taskList) => {
  let array = [];
  for (let i = 0; i < gridList.length; i++) {
    let current_gridList = gridList[i];
    let list = [];

    for (let j = 0; j < taskList.length; j++) {
      let current_TaskList = taskList[j];
      if (
        isSameDay(
          new Date(current_gridList?.date),
          new Date(current_TaskList?.createdAt)
        )
      ) {
        list.push(current_TaskList);
      }
    }
    array.push({
      date: current_gridList?.date,
      list,
    });
  }
  return array;
};

const taskMangementGridSlice = createSlice({
  name: "TASKMANAGMENT_GRID_SLICE_STATE_MANAGMENT",
  initialState,
  reducers: {
    // Update List
    updateList(state, action) {
      let current_state = current(state);
      current_state = { ...current_state };

      let updatedList = updateGridList(current_state?.gridList, action.payload);
      return {
        ...state,
        list: action.payload,
        gridList: updatedList,
      };
    },

    // Previous Week
    updateWeek(state, action) {
      let current_state = current(state);
      current_state = { ...current_state };

      let oldGridList = current_state.gridList;

      let adjusted_Date = getAdjacentDay(
        new Date(oldGridList[0].date),
        action.payload
      );
      if (action.payload === "next") {
        adjusted_Date = getAdjacentDay(
          new Date(oldGridList[oldGridList.length - 1].date),
          action.payload
        );
      }

      let updated_GridList = inital_week_setup(adjusted_Date);

      let updateList = updateGridList(updated_GridList, current_state?.list);

      return {
        ...state,
        gridList: updateList,
      };
    },

    /// Update filter
    updateFilterStatus(state, action) {
      return {
        ...state,
        filter: {
          ...state.filter,
          status: action.payload,
        },
      };
    },
  },
});

export default taskMangementGridSlice;
export const taskManagmentGridSliceActions = taskMangementGridSlice.actions;

export const fetch_Grid_list_taskmanagement = () => {
  return async (dispatch) => {
    let array_formatted = await fetchListTaskmanagement();
    dispatch(taskManagmentGridSliceActions.updateList(array_formatted));
  };
};

// Middle ware of filter
export const taskmanagement_grid_filter_middleware = (filter) => {
  return (dispatch) => {
    let obj = {};

    if (filter?.status?.length !== 0) {
      obj = {
        ...obj,
        Status: filter.status?.map((item) => item.id),
      };
    }

    if (obj) {
      dispatch(taskmanagement_grid_filter_Thunk(obj));
    } else {
      dispatch(fetch_Grid_list_taskmanagement());
    }
  };
};

export const taskmanagement_grid_filter_Thunk = (obj) => {
  return async (dispatch) => {
    let array_formatted = await fetchListWithFilter_taskmanagement(obj);
    dispatch(taskManagmentGridSliceActions.updateList(array_formatted));
  };
};
