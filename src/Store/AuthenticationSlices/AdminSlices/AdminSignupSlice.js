import { createSlice } from "@reduxjs/toolkit";
import { isUsernameValid, passwordValid } from "../../../Config/FormConfig";
import { API_ENDPOINTS, BaseURL, defaultHeaders } from "../../../API/APIConfig";
import { PostAPI } from "../../../API/PostAPI";

const initialState = {
  content: {
    username: {
      value: "",
      isValid: false,
      isTouched: false,
      errorMessage: "Username should be atleast 3 characters",
    },
    mobilephone: {
      value: "",
      isValid: false,
      isTouched: false,
      errorMessage: "Please enter a mobile number",
    },
    password: {
      value: "",
      isValid: false,
      isTouched: false,
      errorMessage: "password should be atleast 6 characters",
    },
  },

  ui: {
    signupButtonIsLoading: false,
  },
};

const AdminSignupSlice = createSlice({
  name: "ADMIN_SIGNUP_SLICE_STATE_MAANAGEMENT",
  initialState,
  reducers: {
    // USER NAME
    usernameUpdateValue(state, action) {
      return {
        ...state,
        content: {
          ...state.content,
          username: {
            ...state.content.username,
            value: action.payload,
            isValid: isUsernameValid(action.payload),
          },
        },
      };
    },

    usernameUpdateIsTouched(state, action) {
      return {
        ...state,
        content: {
          ...state.content,
          username: {
            ...state.content.username,
            isTouched: true,
          },
        },
      };
    },

    //Mobile Number

    mobilePhoneValueUpdate(state, action) {
      return {
        ...state,
        content: {
          ...state.content,
          mobilephone: {
            ...state.content.mobilephone,
            value: action.payload,
            isValid: true,
          },
        },
      };
    },

    mobilePhoneUpdateIsTouched(state, action) {
      return {
        ...state,
        content: {
          ...state.content,
          mobilephone: {
            ...state.content.mobilephone,
            isTouched: true,
          },
        },
      };
    },

    // Password
    passwordUpdateValue(state, action) {
      return {
        ...state,
        content: {
          ...state.content,
          password: {
            ...state.content.password,
            value: action.payload,
            isValid: passwordValid(action.payload),
          },
        },
      };
    },

    passwordUpdateIsTouched(state, action) {
      return {
        ...state,
        content: {
          ...state.content,
          password: {
            ...state.content.password,
            isTouched: true,
          },
        },
      };
    },

    modifySignupButtonIsLoading(state, action) {
      return {
        ...state,
        ui: {
          ...state.ui,
          signupButtonIsLoading: action.payload,
        },
      };
    },

    resetContent(state, content) {
      return {
        ...state,
        content: initialState.content,
      };
    },
  },
});

export const AdminSignupSliceActions = AdminSignupSlice.actions;
export default AdminSignupSlice;

export const AuthenticationAdminSignUpThunk = (content, navigate) => {
  return async (dispatch) => {
    const usernameObj = content.username;
    const mobilephoneObj = content.mobilephone;
    const passwordObj = content.password;

    if (
      !usernameObj.isValid ||
      !mobilephoneObj.isValid ||
      !passwordObj.isValid
    ) {
      return;
    }

    dispatch(AdminSignupSliceActions.modifySignupButtonIsLoading(true));

    const url = new URL(BaseURL.mainURl + API_ENDPOINTS.adminSignup);
    const bodyObj = {
      username: usernameObj.value,
      mobile: mobilephoneObj.value,
      password: passwordObj.value,
    };
    const headers = {
      ...defaultHeaders,
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": [
        "POST",
        "GET",
        "OPTIONS",
        "DELETE",
        "PUT",
      ],
      "Access-Control-Allow-Headers": [
        "append",
        "delete",
        "entries",
        "foreach",
        "get",
        "has",
        "keys",
        "set",
        "values",
        "Authorization",
      ],

      redirect: "follow",
    };

    const response = await PostAPI(url, bodyObj, headers);

    if (response.ok) {
      const responsedate = await response.json();
      let data = responsedate?.data;
      //let user = data?.admin;
      let token = data?.token;

      if (token) {
        navigate("/admin/signin");
      }
    } else {
    }
    dispatch(AdminSignupSliceActions.resetContent());
    dispatch(AdminSignupSliceActions.modifySignupButtonIsLoading(false));
  };
};
