import { createSlice } from "@reduxjs/toolkit";
import { isUsernameValid, passwordValid } from "../../../Config/FormConfig";
import { API_ENDPOINTS, BaseURL, defaultHeaders } from "../../../API/APIConfig";
import { PostAPI } from "../../../API/PostAPI";
import {
  Master_Roles,
  MasterSliceActions,
  setLoginAuth_LocalStorage,
  update_auth_obj_master_slice_thunk,
} from "../../MasterSlice/MasterSlice";
import { routes } from "../../../Config/routesConfig";

const initialState = {
  content: {
    username: {
      value: "",
      isValid: false,
      isTouched: false,
      errorMessage: "Username should be atleast 3 characters",
    },
    password: {
      value: "",
      isValid: false,
      isTouched: false,
      errorMessage: "password should be atleast 6 characters",
    },
  },

  ui: {
    signinButtonIsLoading: false,
  },
};

const AdminSigninSlice = createSlice({
  name: "ADMIN_SIGIN_STATE_MANAGEMENT",
  initialState,
  reducers: {
    // Email
    usernameUpdateValue(state, action) {
      return {
        ...state,
        content: {
          ...state.content,
          username: {
            ...state.content.username,
            value: action.payload,
            isValid: isUsernameValid(action.payload),
          },
        },
      };
    },

    usernameUpdateIsTouched(state, action) {
      return {
        ...state,
        content: {
          ...state.content,
          username: {
            ...state.content.username,
            isTouched: true,
          },
        },
      };
    },

    //password
    passwordUpdateValue(state, action) {
      return {
        ...state,
        content: {
          ...state.content,
          password: {
            ...state.content.password,
            value: action.payload,
            isValid: passwordValid(action.payload),
          },
        },
      };
    },

    passwordUpdateIsTouched(state, action) {
      return {
        ...state,
        content: {
          ...state.content,
          password: {
            ...state.content.password,
            isTouched: true,
          },
        },
      };
    },

    modifySigninButtonIsLoading(state, action) {
      return {
        ...state,
        ui: {
          ...state.ui,
          signinButtonIsLoading: action.payload,
        },
      };
    },

    //ResetContent
    resetContent(state, action) {
      return {
        ...state,
        content: initialState.content,
      };
    },
  },
});

export const AdminSigninSliceActions = AdminSigninSlice.actions;
export default AdminSigninSlice;

export const AuthenticationAdminSignInThunk = (content, navigate) => {
  return async (dispatch) => {
    let usernameObj = content.username;
    let passwordObj = content.password;

    if (!usernameObj.isValid || !passwordObj.isValid) {
      return;
    }

    dispatch(AdminSigninSliceActions.modifySigninButtonIsLoading(true));

    const url = new URL(BaseURL.mainURl + API_ENDPOINTS.adminSignin);
    const bodyObj = {
      username: usernameObj.value,
      password: passwordObj.value,
    };
    const headers = {
      ...defaultHeaders,
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": [
        "POST",
        "GET",
        "OPTIONS",
        "DELETE",
        "PUT",
      ],
      "Access-Control-Allow-Headers": [
        "append",
        "delete",
        "entries",
        "foreach",
        "get",
        "has",
        "keys",
        "set",
        "values",
        "Authorization",
      ],

      redirect: "follow",
    };

    const response = await PostAPI(url, bodyObj, headers);

    if (response.ok) {
      const responsedate = await response.json();
      let data = responsedate?.data;
      let user = data?.user;
      let token = data?.token;

      let obj = {
        token: token,
        id: user?._id,
        email: user?.email,
        username: user?.username,
        mobilephone: user?.mobile,
        role: Master_Roles.admin,
      };

      if (obj?.role?.toLocaleLowerCase() !== "Admin"?.toLocaleLowerCase()) {
        navigate("/");
        window.alert("User is not an admin");
        return;
      }

      let access = {
        user: {
          read: true,
          write: true,
          insights: true,
          edit: true,
          delete: true,
        },
        purchase: {
          read: true,
          write: true,
          insights: true,
          edit: true,
          delete: true,
        },
        sales: {
          read: true,
          write: true,
          insights: true,
          edit: true,
          delete: true,
        },
        marketing: {
          read: true,
          write: true,
          insights: true,
          edit: true,
          delete: true,
        },
        orders: {
          read: true,
          write: true,
          insights: true,
          edit: true,
          delete: true,
        },
        customers: {
          read: true,
          write: true,
          insights: true,
          edit: true,
          delete: true,
        },
        networking: {
          read: true,
          write: true,
          insights: true,
          edit: true,
          delete: true,
        },
        serviceComplaints: {
          read: true,
          write: true,
          insights: true,
          edit: true,
          delete: true,
        },
        tickets: {
          read: true,
          write: true,
          insights: true,
          edit: true,
          delete: true,
        },
        taskManagement: {
          read: true,
          write: true,
          insights: true,
          edit: true,
          delete: true,
        },
      };

      // If mustChangePassword is true
      if (data?.user?.mustChangePassword) {
        navigate(
          routes?.resetPassword?.directLink +
            "?" +
            routes?.resetPassword?.userIdSearchParam +
            "=" +
            obj?.id +
            "&" +
            routes?.resetPassword?.tokenSeachParam +
            "=" +
            obj?.token +
            "&" +
            routes?.resetPassword?.signFormSearchParam +
            "=admin"
        );
      } else {
        dispatch(MasterSliceActions.updateAuthObj(obj));
        setLoginAuth_LocalStorage(obj, access);
        dispatch(MasterSliceActions?.updateAccess(access));
        dispatch(update_auth_obj_master_slice_thunk(obj));
        navigate(routes.userManagement.directLink);
      }
    } else {
      const responsedate = await response.json();
      console.log(responsedate);
    }
    dispatch(AdminSigninSliceActions.resetContent());
    dispatch(AdminSigninSliceActions.modifySigninButtonIsLoading(false));
  };
};
