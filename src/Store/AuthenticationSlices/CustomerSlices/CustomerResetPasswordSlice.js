import { createSlice } from "@reduxjs/toolkit";
import {
  isUsernameValid,
  mobileNumberValid,
  passwordValid,
} from "../../../Config/FormConfig";
import { API_ENDPOINTS, BaseURL, defaultHeaders } from "../../../API/APIConfig";
import { PostAPI } from "../../../API/PostAPI";

const initialState = {
  content: {
    mobilePhone: {
      value: "",
      isValid: false,
      isTouched: false,
      message: "Enter Valid Mobile Number",
    },
    oldPassword: {
      value: "",
      isValid: false,
      isTouched: false,
      message: "Old Password should not be empty",
    },
    password: {
      value: "",
      isValid: false,
      isTouched: false,
      message: "Password should be atleast 6 characters",
    },
    confirmPassword: {
      value: "",
      isValid: false,
      isTouched: false,
      message: "Not same",
    },
  },
};

const CustomerResetPasswordSlice = createSlice({
  name: "CUSTOMER_RESET_PASSWORD_STATE_MANAGEMENT",
  initialState,
  reducers: {
    //content
    // mobile phone
    updateContentMobilePhone(state, action) {
      return {
        ...state,
        content: {
          ...state.content,
          mobilePhone: {
            ...state.content.mobilePhone,
            value: action.payload,
            isValid: mobileNumberValid(action.payload),
          },
        },
      };
    },

    updateContentBlurMobilePhone(state, action) {
      return {
        ...state,
        content: {
          ...state.content,
          mobilePhone: {
            ...state.content.mobilePhone,
            isTouched: true,
          },
        },
      };
    },

    // Old Password
    updateContentOldPassword(state, action) {
      return {
        ...state,
        content: {
          ...state.content,
          oldPassword: {
            ...state.content.oldPassword,
            value: action.payload,
            isValid: isUsernameValid(action.payload),
          },
        },
      };
    },

    updateContentBlurOldPassword(state, action) {
      return {
        ...state,
        content: {
          ...state.content,
          oldPassword: {
            ...state.content.oldPassword,
            isTouched: true,
          },
        },
      };
    },

    // Password
    updateContentPassword(state, action) {
      return {
        ...state,
        content: {
          ...state.content,
          password: {
            ...state.content.password,
            value: action.payload,
            isValid: passwordValid(action.payload),
          },
        },
      };
    },

    updateContentBlurPassword(state, action) {
      return {
        ...state,
        content: {
          ...state.content,
          password: {
            ...state.content.password,
            isTouched: true,
          },
        },
      };
    },

    // Confirm Password
    updateContentConfirmPassword(state, action) {
      return {
        ...state,
        content: {
          ...state.content,
          confirmPassword: {
            ...state.content.confirmPassword,
            value: action.payload,
            isValid: state.content.password.value === action.payload,
          },
        },
      };
    },

    updateContentBlurConfirmPassword(state, action) {
      return {
        ...state,
        content: {
          ...state.content,
          confirmPassword: {
            ...state.content.confirmPassword,
            isTouched: true,
          },
        },
      };
    },
  },
});

export const CustomerResetPasswordSliceActions =
  CustomerResetPasswordSlice.actions;
export default CustomerResetPasswordSlice;

export const customerResetPasswordThunk = (content) => {
  return async (dispatch) => {
    let mobilePhone = content.mobilePhone;
    let oldPassword = content.oldPassword;
    let password = content.password;
    let confirmPassword = content.confirmPassword;

    if (
      !mobilePhone.isValid &&
      !oldPassword.isValid &&
      !password.isValid &&
      !confirmPassword.isValid
    ) {
      return;
    }

    let obj = {
      mobileNumber: mobilePhone.value,
      oldPassword: oldPassword.value,
      newPassword: password.value,
      confirmNewPassword: confirmPassword.value,
    };

    let api = new URL(BaseURL.mainURl + API_ENDPOINTS.customerResetPassword);

    const headers = {
      ...defaultHeaders,
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": [
        "POST",
        "GET",
        "OPTIONS",
        "DELETE",
        "PUT",
      ],
      "Access-Control-Allow-Headers": [
        "append",
        "delete",
        "entries",
        "foreach",
        "get",
        "has",
        "keys",
        "set",
        "values",
        "Authorization",
      ],

      redirect: "follow",
    };

    let response = await PostAPI(api, obj, headers);

    if (response.ok) {
      let responseData = await response.json();
    }
  };
};
