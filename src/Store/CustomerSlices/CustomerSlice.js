import { createSlice } from "@reduxjs/toolkit";
import { API_ENDPOINTS, BaseURL, defaultHeaders } from "../../API/APIConfig";
import { GetAPI } from "../../API/GetAPI";
import { updateCustomerListThunkMaster } from "../MasterSlice/MasterSlice";

export const customerPageViewMode = {
     view: "view",
     edit: "edit ",
};

const initialState = {
     searchedCustomerList: [],
     customerList: [],

     ui: {
          pageMode: customerPageViewMode.view,
     },
};

const customerSlice = createSlice({
     name: "CUSTOMER_SLICE_STATE_MANAGMENET",
     initialState,
     reducers: {
          updateSearchedCustomerList(state, action) {
               return {
                    ...state,
                    searchedCustomerList: action.payload,
               };
          },
          updateCustomerList(state, action) {
               return {
                    ...state,
                    customerList: action.payload,
                    searchedCustomerList: action.payload,
               };
          },

          // update page mode
          updateUIPageMode(state, action) {
               return {
                    ...state,
                    ui: {
                         ...state.ui,
                         pageMode: action.payload,
                    },
               };
          },
     },
});

export const customerSliceActions = customerSlice.actions;
export default customerSlice;

export const customerListFetchHelperFunction = async () => {
     let api = new URL(BaseURL.mainURl + API_ENDPOINTS.customerList);
     const headers = {
          ...defaultHeaders,
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": [
               "POST",
               "GET",
               "OPTIONS",
               "DELETE",
               "PUT",
          ],
          "Access-Control-Allow-Headers": [
               "append",
               "delete",
               "entries",
               "foreach",
               "get",
               "has",
               "keys",
               "set",
               "values",
               "Authorization",
          ],

          redirect: "follow",
     };

     let response = await GetAPI(api, headers);

     if (response.ok) {
          let responseDate = await response.json();
          let data = responseDate?.data?.customers;
          let array = [];
          for (const item of data) {
               let obj = {
                    id: item._id,
                    customerName: item.customer_name,
                    customerSince: item?.customer_since,
                    email: item?.email,
                    lastPurchase: item?.last_purchase,
                    orderCount: item?.order_count,
                    phoneNumber: item?.phone_number,
                    totalOrder: item?.total_order_total,
                    ifReferred: item?.If_Referred,
                    referredCustomerName: item?.Referred_Customer_Name,
                    referredCustomerId: item?.Referred_Customer_ID,
                    finalJobLink: item?.Final_Job_Link,
               };

               array.push(obj);
          }
          return array;
     }

     return [];
};

export const customerListFetchThunk = () => {
     return async (dispatch) => {
          let response = await customerListFetchHelperFunction();
          dispatch(customerSliceActions.updateCustomerList(response));
          dispatch(updateCustomerListThunkMaster());
          // let api = new URL(BaseURL.mainURl + API_ENDPOINTS.customerList);
          // const headers = {
          //      ...defaultHeaders,
          //      "Access-Control-Allow-Origin": "*",
          //      "Access-Control-Allow-Methods": [
          //           "POST",
          //           "GET",
          //           "OPTIONS",
          //           "DELETE",
          //           "PUT",
          //      ],
          //      "Access-Control-Allow-Headers": [
          //           "append",
          //           "delete",
          //           "entries",
          //           "foreach",
          //           "get",
          //           "has",
          //           "keys",
          //           "set",
          //           "values",
          //           "Authorization",
          //      ],

          //      redirect: "follow",
          // };

          // let response = await GetAPI(api, headers);

          // if (response.ok) {
          //      let responseDate = await response.json();
          //      let data = responseDate?.data?.customers;
          //      let array = [];
          //      for (const item of data) {
          //           let obj = {
          //                id: item._id,
          //                customerName: item.customer_name,
          //                customerSince: item?.customer_since,
          //                email: item?.email,
          //                lastPurchase: item?.last_purchase,
          //                orderCount: item?.order_count,
          //                phoneNumber: item?.phone_number,
          //                totalOrder: item?.total_order_total,
          //           };
          //           array.push(obj);
          //      }
          //      dispatch(customerSliceActions.updateCustomerList(array));
          // }
     };
};
