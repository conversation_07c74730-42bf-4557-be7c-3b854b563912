import { createSlice } from "@reduxjs/toolkit";
import { API_ENDPOINTS, BaseURL, defaultHeaders } from "../../API/APIConfig";
import { GetAPI } from "../../API/GetAPI";

const initialState = {
  selectedCustomer: {
    id: "",
  },

  orderList: [],

  headerData: {
    allOrders: 0,
    completed: 0,
    ordersTotal: 0,
    outOfStock: 0,
    pending: 0,
  },
};

const singleCustomerSlice = createSlice({
  name: "SINGLE_CUSTOMER_STATA_MANAGMENT",
  initialState,
  reducers: {
    updateSelectedCustomer(state, action) {
      return {
        ...state,
        selectedCustomer: {
          ...state.selectedCustomer,
          id: action.payload.id,
        },
      };
    },

    // order list updation

    updateOrderList(state, action) {
      return {
        ...state,
        orderList: action.payload,
      };
    },

    // header data

    updateHeaderData(state, action) {
      return {
        ...state,
        headerData: {
          ...state.headerData,
          allOrders: action.payload.allOrders,
          completed: action.payload.completed,
          ordersTotal: action.payload.ordersTotal,
          outOfStock: action.payload.outOfStock,
          pending: action.payload.pending,
        },
      };
    },
  },
});

export const singleCustomerSliceActions = singleCustomerSlice.actions;
export default singleCustomerSlice;

export const singleCustomerDataFetch = (id) => {
  return async (dispatch) => {
    let api = new URL(
      BaseURL.mainURl + API_ENDPOINTS?.singleCustomerData + "/" + id
    );
    const headers = {
      ...defaultHeaders,
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": [
        "POST",
        "GET",
        "OPTIONS",
        "DELETE",
        "PUT",
      ],
      "Access-Control-Allow-Headers": [
        "append",
        "delete",
        "entries",
        "foreach",
        "get",
        "has",
        "keys",
        "set",
        "values",
        "Authorization",
      ],

      redirect: "follow",
    };

    let response = await GetAPI(api, headers);

    if (response.ok) {
      let responseData = await response.json();

      let data = responseData.data;
      let orderCount = data?.order_counts;
      let orders = data.orders;

      let array = [];

      for (const item of orders) {
        let billingsummary = item?.billingsummary;
        let billedorder = item?.billedorder;

        let obj = {
          billId: billingsummary?._id,
          orderId: billingsummary?.order_id,
          orderDate: billingsummary?.orderdate,
          productDetails: billingsummary?.product,
          quantity: billingsummary?.quantity,
          stockAvailablity: billingsummary?.stock_availability,
          orderTotal: billingsummary?.total,
          salesPerson: billingsummary?.sales_person,
          deliveryDate: billingsummary?.delivery_date,
          status: billingsummary?.status,
          subArray: [],
        };

        let subArray = [];

        for (const indiv of billedorder) {
          let subObj = {
            billId: indiv.billing_id,
            orderId: indiv?.order_id,
            orderDate: indiv?.order_date,
            productDetails: indiv?.product_details,
            quantity: indiv?.quantity,
            stockAvailablity: indiv?.stock_availability,
            orderTotal: indiv?.order_total,
            status: indiv?.order_status,
            salesPerson: indiv?.sales_person,
            deliveryDate: indiv?.delivery_date,
            status: indiv?.status,
          };

          subArray.push(subObj);
        }
        obj.subArray = subArray;
        array.push(obj);
      }

      dispatch(singleCustomerSliceActions.updateOrderList(array));

      let headerData = {
        allOrders: orderCount.All_orders,
        completed: orderCount.completed,
        ordersTotal: orderCount?.orders_total,
        outOfStock: orderCount?.out_of_stock,
        pending: orderCount?.pending,
      };

      dispatch(singleCustomerSliceActions.updateHeaderData(headerData));
    }
  };
};
