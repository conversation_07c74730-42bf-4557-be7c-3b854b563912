import { createSlice } from "@reduxjs/toolkit";
import { API_ENDPOINTS, BaseURL, defaultHeaders } from "../../API/APIConfig";
import { PostAPI } from "../../API/PostAPI";
import {
     customerListFetchThunk,
     customerPageViewMode,
     customerSliceActions,
} from "./CustomerSlice";

const initialState = {
     content: {
          id: "",
          ifReferred: {
               id: "",
               content: "",
          },
          referredCustomerName: "",
          referredCustomerId: {
               id: "",
               content: "",
          },
          finalJobLink: "",
     },

     ui: {
          updateButtonIsLoading: false,
     },
};

const CustomerEditSlice = createSlice({
     name: "CUSTOMER_SLICE_EDIT",
     initialState,
     reducers: {
          updateContent(state, action) {
               return {
                    ...state,
                    content: {
                         ...state.content,
                         id: action.payload.id,
                         ifReferred: action.payload.ifReferred,
                         referredCustomerId: action.payload.referredCustomerId,
                         referredCustomerName:
                              action.payload.referredCustomerName,
                         finalJobLink: action.payload.finalJobLink,
                    },
               };
          },

          // reset Content

          resetContent(state, action) {
               return {
                    ...state,
                    content: initialState.content,
               };
          },

          // Key updation

          updateContentIfReferred(state, action) {
               return {
                    ...state,
                    content: {
                         ...state.content,
                         ifReferred: action.payload,
                    },
               };
          },

          updateContentReferredCustomerName(state, action) {
               return {
                    ...state,
                    content: {
                         ...state.content,
                         referredCustomerName: action.payload,
                    },
               };
          },

          updateContentReferredCustomerId(state, action) {
               return {
                    ...state,
                    content: {
                         ...state.content,
                         referredCustomerId: action.payload,
                    },
               };
          },

          updateContentFinalJobLink(state, action) {
               return {
                    ...state,
                    content: {
                         ...state.content,
                         finalJobLink: action.payload,
                    },
               };
          },

          //  Update updateButtonIsLoading
          updateUiUpdateButtonIsLoading(state, action) {
               return {
                    ...state,
                    ui: {
                         ...state.ui,
                         updateButtonIsLoading: action.payload,
                    },
               };
          },
     },
});

export default CustomerEditSlice;
export const customerEditSliceActions = CustomerEditSlice.actions;

export const customerEditThunk = (content) => {
     return async (dispatch) => {
          const body = {
               If_Referred: content?.ifReferred.content,
               Referred_Customer_Name: content?.referredCustomerName,
               Referred_Customer_ID: content?.referredCustomerId.content,
               Final_Job_Link: content?.finalJobLink,
          };

          let api = new URL(
               BaseURL.mainURl + API_ENDPOINTS.customerEdit + "/" + content.id
          );

          const headers = {
               ...defaultHeaders,
               "Access-Control-Allow-Origin": "*",
               "Access-Control-Allow-Methods": [
                    "POST",
                    "GET",
                    "OPTIONS",
                    "DELETE",
                    "PUT",
               ],
               "Access-Control-Allow-Headers": [
                    "append",
                    "delete",
                    "entries",
                    "foreach",
                    "get",
                    "has",
                    "keys",
                    "set",
                    "values",
                    "Authorization",
               ],
          };

          dispatch(
               customerEditSliceActions.updateUiUpdateButtonIsLoading(true)
          );

          const response = await PostAPI(api, body, headers, "PUT");

          if (response.ok) {
               let responseData = await response.json();
               dispatch(customerListFetchThunk());

               dispatch(
                    customerSliceActions.updateUIPageMode(
                         customerPageViewMode.view
                    )
               );
               dispatch(customerEditSliceActions.resetContent());
          }
          dispatch(
               customerEditSliceActions.updateUiUpdateButtonIsLoading(false)
          );
     };
};
