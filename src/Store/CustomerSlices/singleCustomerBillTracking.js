import { createSlice } from "@reduxjs/toolkit";
import { API_ENDPOINTS, BaseURL, defaultHeaders } from "../../API/APIConfig";
import { GetAPI } from "../../API/GetAPI";

const initialState = {
  billId: "Not Found",
  customerId: "Not Found",

  orders: [],

  totalList: [],

  total: {
    title: "Total",
    amount: 0,
  },
};

const singleCustomerBillTrackingSlice = createSlice({
  name: "SINGLE_CUSTOMER_BILL_TRACKING_SLICE",
  initialState,
  reducers: {
    updateBillIdCustomerId(state, action) {
      return {
        ...state,
        billId: action.payload.billId,
        customerId: action.payload?.customerId,
      };
    },

    updateDetails(state, action) {
      return {
        ...state,
        orders: action.payload?.orders,
        total: { title: "Total", amount: action.payload?.total },
        totalList: action.payload?.totalList,
      };
    },

    updateReset(state, action) {
      return {
        ...state,
        ...initialState,
      };
    },
  },
});

export const singleCustomerBillTrackingSliceActions =
  singleCustomerBillTrackingSlice?.actions;
export default singleCustomerBillTrackingSlice;

export const fetchCustomerBill = (cusId, billId) => {
  return async (dispatch) => {
    let url = new URL(
      BaseURL.mainURl + API_ENDPOINTS?.tracking + "/" + cusId + "/" + billId
    );

    const headers = {
      ...defaultHeaders,
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": [
        "POST",
        "GET",
        "OPTIONS",
        "DELETE",
        "PUT",
      ],
      "Access-Control-Allow-Headers": [
        "append",
        "delete",
        "entries",
        "foreach",
        "get",
        "has",
        "keys",
        "set",
        "values",
        "Authorization",
      ],

      redirect: "follow",
    };

    let response = await GetAPI(url, headers);

    if (response?.ok) {
      let responseData = await response.json();

      let total = responseData?.total;
      let orders = responseData?.customer_tracking?.orders;

      let array = [];
      let totalList = [];

      for (const item of orders) {
        let obj = {
          product: item?.product_details,
          quantity: item?.quantity,
          total: item?.order_total,
          orderDate: item?.order_date,
          status: item?.status,
        };

        array.push(obj);

        let tot = {
          title: item?.product_details,
          amount: item?.order_total,
          quantity: item?.quantity,
        };

        totalList.push(tot);
      }

      dispatch(
        singleCustomerBillTrackingSliceActions.updateDetails({
          orders: array,
          total: total,
          totalList: totalList,
        })
      );
    }
  };
};
