import { createSlice } from "@reduxjs/toolkit";
import { marketingWalkinTableConfig } from "../../Config/MarketingWalkinConfig";

const initialState = {
  list: [],

  headerList: [
    { title: "TOTAL", count: 0 },
    { title: "NEW LEAD", count: 0 },
    { title: "QUOTE SENT", count: 0 },
    { title: "IN FOLLOW UP", count: 0 },
    { title: "DROPPED", count: 0 },
    { title: "ORDER CLOSED", count: 0 },
  ],

  filter: {
    state: {},
    date: {
      startDate: new Date(),
      endDate: new Date(),
      filter: {
        id: "Till now",
        content: "Till now",
      },
    },
  },

  // add
  add: {
    structure: [],
    state: {},
  },

  // edit
  edit: {
    structure: [],
    state: {},
    id: "",
  },

  // Table Data
  table: {
    header: marketingWalkinTableConfig,
  },
};

const marketingWalkinSlice = createSlice({
  name: "marketing_Walkin_State_management",
  initialState,
  reducers: {
    // table
    updateTableHeader(state, action) {
      return {
        ...state,
        table: {
          ...state.table,
          header: action.payload,
        },
      };
    },

    // Add
    updateStructureAndState(state, action) {
      return {
        ...state,
        add: {
          ...state.add,
          structure: action.payload?.structure,
          state: action.payload?.state,
        },
      };
    },

    updateState(state, action) {
      return {
        ...state,
        add: {
          ...state.add,
          state: action.payload,
        },
      };
    },

    //Edit
    updateEditStructureAndState(state, action) {
      return {
        ...state,
        edit: {
          ...state?.edit,
          structure: action.payload?.structure,
          state: action.payload?.state,
          id: action.payload?.id,
        },
      };
    },

    updateEditState(state, action) {
      return {
        ...state,
        edit: {
          ...state?.edit,
          state: action.payload,
        },
      };
    },

    // -----------------------------
    // List
    updateList(state, action) {
      return {
        ...state,
        list: action.payload,
      };
    },
    // headerdata
    updateHeaderList(state, action) {
      return {
        ...state,
        headerList: action.payload,
      };
    },

    // Filter
    updateFilterDate(state, action) {
      return {
        ...state,
        filter: {
          ...state.filter,
          date: {
            ...state.filter.date,
            ...action.payload,
          },
        },
      };
    },
    updateFilterState(state, action) {
      return { ...state, filter: { ...state.filter, state: action.payload } };
    },
  },
});

export default marketingWalkinSlice;
export const marketingWalkinSliceAction = marketingWalkinSlice.actions;
