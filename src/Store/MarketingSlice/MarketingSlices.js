import { createSlice } from "@reduxjs/toolkit";
import { fetch_marketing_header_count } from "../../Dashboard_API_calling_and_formatting/sales_API";
import { salesAndMarketingTableConfig } from "../../Config/SalesAndMarketingTableConfig";

const initialState = {
  list: [],
  headerList: [
    { title: "Total", count: 0 },
    { title: "New Lead", count: 0 },
    { title: "Quote Sent", count: 0 },
    { title: "Follow In", count: 0 },
    { title: "Dropped", count: 0 },
    { title: "Order Confirm", count: 0 },
    // { title: "Walk In", count: 0 },
    // { title: "Field Visit", count: 0 },
  ],

  filter: {
    state: {},
    date: {
      startDate: new Date(),
      endDate: new Date(),
      filter: {
        id: "Till now",
        content: "Till now",
      },
    },
  },

  // add
  add: {
    structure: [],
    state: {},
  },

  // edit
  edit: {
    structure: [],
    state: {},
    id: "",
  },

  // Table Data
  table: {
    header: salesAndMarketingTableConfig,
  },

  // Analytics
  analytics: {
    startDate: new Date(),
    endDate: new Date(),
    filter: {
      id: "Till now",
      content: "Till now",
    },

    // -------------------
    salesPulse: {
      new_lead: 0,
      QUOTE_SENT: 0,
      IN_FOLLOW: 0,
      dropped: 0,
      WALK_IN: 0,
      FIELD_VISIT: 0,
    },

    leadSource: {
      "INDIVIDUAL RESIDENT": 0,
      RESIDENT: 0,
    },

    // lead Closue
    leadClosure: {
      leads: [],
      closure: [],
      date: [],
    },

    Walk_in_Status: {
      DNSP: 0,
      DBF: 0,
      DUC: 0,
      DC: 0,
    },

    top5: [],

    salesTrend: [],

    averageOrderValue: "",

    targetData: {
      targetAchieved: 0,
      pendingTargets: 0,
    },

    AOVvsDay: [],

    topSalesPerson: {
      name: [],
      totalQuantitySold: [],
      totalOrderValue: [],
    },

    topSellingProducts: {
      name: [],
      totalQuantity: [],
    },

    daysLeadClosure: 0,

    TargetsVsAchieved: {
      date: [],
      IN_FOLLOW: [],
      QUOTE_SENT: [],
    },

    ShowLeadsquoteengineers: {
      newLeads: [],
      quoteSent: [],
      engineer: [],
    },

    ShowLeadsquoteARCH_NAME: {
      newLeads: [],
      quoteSent: [],
      architect: [],
    },

    ShowLeadsquoteBrand: {
      brand: [],
      newLeads: [],
      quoteSent: [],
    },

    ShowLeadsquoteARCH_NAME: {
      newLeads: [],
      quoteSent: [],
      architect: [],
    },

    Leadsquoteresidentindividual: {
      INDIVIDUALRESIDENT: {
        newLead: 0,
        quoteSent: 0,
      },
      RESIDENT: {
        newLead: 0,
        quoteSent: 0,
      },

      BUNGALOW: {
        newLead: 0,
        quoteSent: 0,
      },

      PROJECT: {
        newLead: 0,
        quoteSent: 0,
      },

      COMMERCIAL: {
        newLead: 0,
        quoteSent: 0,
      },

      INDIVIDUAL: {
        newLead: 0,
        quoteSent: 0,
      },

      APARTMENT: {
        newLead: 0,
        quoteSent: 0,
      },
    },
  },
};

const marketingSlice = createSlice({
  name: "MARKETING_SLICE_STATE_MANAGMENT",
  initialState,
  reducers: {
    // table
    updateTableHeader(state, action) {
      return {
        ...state,
        table: {
          ...state.table,
          header: action.payload,
        },
      };
    },

    // Add
    updateStructureAndState(state, action) {
      return {
        ...state,
        add: {
          ...state.add,
          structure: action.payload?.structure,
          state: action.payload?.state,
        },
      };
    },

    updateState(state, action) {
      return {
        ...state,
        add: {
          ...state.add,
          state: action.payload,
        },
      };
    },

    //Edit
    updateEditStructureAndState(state, action) {
      return {
        ...state,
        edit: {
          ...state?.edit,
          structure: action.payload?.structure,
          state: action.payload?.state,
          id: action.payload?.id,
        },
      };
    },

    updateEditState(state, action) {
      return {
        ...state,
        edit: {
          ...state?.edit,
          state: action.payload,
        },
      };
    },

    // -----------------------------

    updateList(state, action) {
      return {
        ...state,
        list: action.payload,
      };
    },

    updateHeaderList(state, action) {
      return {
        ...state,
        headerList: action.payload,
      };
    },

    // Filter
    updateFilterDate(state, action) {
      return {
        ...state,
        filter: {
          ...state.filter,
          date: {
            ...state.filter.date,
            ...action.payload,
          },
        },
      };
    },
    updateFilterState(state, action) {
      return { ...state, filter: { ...state.filter, state: action.payload } };
    },

    // Analytics
    updateAnalytics(state, action) {
      return {
        ...state,
        analytics: {
          ...state?.analytics,
          [action.payload?.key]: action?.payload?.value,
        },
      };
    },

    updateAnalyticsContent(state, action) {
      return {
        ...state,
        analytics: {
          ...state?.analytics,
          ...action.payload,
        },
      };
    },
    //----------------
  },
});

export default marketingSlice;
export const marketingSliceActions = marketingSlice?.actions;

export const marketing_headerdata_thunk = (auth) => {
  return async (dispatch) => {
    let response = await fetch_marketing_header_count(auth);

    if (response) {
      let leadHeader = response?.data;

      let array = [
        { title: "Total", count: leadHeader?.TOTAL_LEADS },
        { title: "New Lead", count: leadHeader?.NEW_LEAD },
        { title: "Quote Sent", count: leadHeader?.QUOTE_SENT },
        { title: "Follow In", count: leadHeader?.IN_FOLLOW },
        { title: "Dropped", count: leadHeader?.DROPPED },
        { title: "Order Confirm", count: leadHeader?.ORDER_CONFIRM },
        // { title: "Walk In", count: leadHeader?.WALK_IN },
        // { title: "Field Visit", count: leadHeader?.FIELD_VISIT },
      ];

      dispatch(marketingSliceActions.updateHeaderList(array));
    }
  };
};
