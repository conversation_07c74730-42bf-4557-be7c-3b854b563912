import { createSlice } from "@reduxjs/toolkit";
import { API_ENDPOINTS, api_headers, BaseURL } from "../../API/APIConfig";
import { GetAPI } from "../../API/GetAPI";

const initialState = {
  headerData: {
    orders_total: 0,
    All_orders: 0,
    pending: 0,
    completed: 0,
    out_of_stock: 0,
  },

  list: [],
};

const CustomerDashboardOrderSlice = createSlice({
  name: "Customerdashboardslice_state_managmenet",
  initialState,
  reducers: {
    // Update Header Data
    updateHeaderData(state, action) {
      return { state, headerData: action.payload };
    },

    // update List
    updateList(state, action) {
      return { ...state, list: action.payload };
    },
  },
});

export const CustomerDashboardOrderSliceActions =
  CustomerDashboardOrderSlice?.actions;
export default CustomerDashboardOrderSlice;

export const fetch_customer_dashboard_order_data_thunk = (id) => {
  return async (dispatch) => {
    let api = new URL(
      BaseURL.mainURl + API_ENDPOINTS?.singleCustomerData + "/" + id
    );

    let headers = { ...api_headers };

    let response = await GetAPI(api, headers);

    if (response.ok) {
      let responseData = await response.json();

      let data = responseData.data;
      let orderCount = data?.order_counts;
      let orders = data.orders;

      dispatch(
        CustomerDashboardOrderSliceActions?.updateHeaderData(orderCount)
      );

      let array = [];

      for (const item of orders) {
        let billingsummary = item?.billingsummary;
        let billedorder = item?.billedorder;

        let obj = {
          billId: billingsummary?._id,
          orderId: billingsummary?.order_id,
          orderDate: billingsummary?.orderdate,
          productDetails: billingsummary?.product,
          quantity: billingsummary?.quantity,
          stockAvailablity: billingsummary?.stock_availability,
          orderTotal: billingsummary?.total,
          salesPerson: billingsummary?.sales_person,
          deliveryDate: billingsummary?.delivery_date,
          status: billingsummary?.status,
          subArray: [],
        };

        let subArray = [];

        for (const indiv of billedorder) {
          let subObj = {
            billId: indiv.billing_id,
            orderId: indiv?.order_id,
            orderDate: indiv?.order_date,
            productDetails: indiv?.product_details,
            quantity: indiv?.quantity,
            stockAvailablity: indiv?.stock_availability,
            orderTotal: indiv?.order_total,
            status: indiv?.order_status,
            salesPerson: indiv?.sales_person,
            deliveryDate: indiv?.delivery_date,
          };

          subArray.push(subObj);
        }
        obj.subArray = subArray;
        array.push(obj);
      }

      dispatch(CustomerDashboardOrderSliceActions?.updateList(array));
    }
  };
};
