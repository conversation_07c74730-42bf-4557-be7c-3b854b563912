import { createSlice } from "@reduxjs/toolkit";

import { api_purchase_headercount } from "../../Dashboard_API_calling_and_formatting/purchase_API";

const initialState = {
  headerData: [
    { title: "All Products", count: "0" },
    { title: "Available", count: "0" },
    { title: "Against Order", count: "0" },
    { title: "B", count: "0" },
    { title: "C", count: "0" },
    {
      title: "Purchase Order",
      count: "0",
    },
  ],

  purchaseList: [],

  filter: {
    date: {
      startDate: new Date(),
      endDate: new Date(),
      filter: {
        id: "Till now",
        content: "Till now",
      },
    },
    state: {},
  },

  add: {
    structure: [],
    state: {},
  },

  edit: {
    structure: [],
    state: {},
    id: "",
  },

  // Analytics
  analytics: {
    startDate: new Date(),
    endDate: new Date(),
    filter: {
      id: "Till now",
      content: "Till now",
    },

    brandVsSizeOptions: { id: "Category 1", content: "Category 1" },

    OverallStockPurchased: 0,

    OverallStockSold: 0,

    DelayedCommitments: 0,

    AverageStockDeliveryTime: 0,

    OnTimeDeliveryDelayedCommitments: {
      OnTimeDelivery: 0,
      DelayedCommitments: 0,
    },

    NumberofTrucks: 0,

    MaterialsUnloaded: 0,

    PurchaseEntry: 0,

    PurchaseAgainstOrder: 0,

    StockPurchasedVsStockSold: {
      StockPurchased: [],
      StockSold: [],
      unloadingDate: [],
    },

    MSLVsOutofStockVsDamaged: {
      Damage: [],
      MSL: [],
      OutofStock: [],
      unloadingDate: [],
      B: [],
      c: [],
    },

    topProducts: {
      id: [],
      totalQuantity: [],
    },

    topBatches: {
      id: [],
      totalQuantity: [],
    },

    topMaterials: {
      id: [],
      totalQuantity: [],
    },

    topDivisions: {
      id: [],
      totalQuantity: [],
    },

    topSizes: {
      id: [],
      totalQuantity: [],
    },

    topCategories: {
      id: [],
      totalQuantity: [],
    },

    topSubCategories: {
      id: [],
      totalQuantity: [],
    },

    topBrands: {
      id: [],
      totalQuantity: [],
    },

    topGrades: {
      id: [],
      totalQuantity: [],
    },

    topWarehouseStatus: {
      id: [],
      totalQuantity: [],
    },

    topNatureOfOrders: {
      id: [],
      totalQuantity: [],
    },

    brandVsSize: {
      brand: [],
      size: [],
      quantity: [],
    },

    //-----------------------
  },
};

const PurchaseSlice = createSlice({
  name: "PURCHASE_SLICE_STATE_MANAGEMENT",
  initialState,
  reducers: {
    // ----------------------------------
    updateHeaderData(state, action) {
      return {
        ...state,
        headerData: action.payload,
      };
    },

    updatePurchaseList(state, action) {
      return {
        ...state,
        purchaseList: action.payload,
      };
    },

    // Update Filter  State
    updateFilterDate(state, action) {
      return {
        ...state,
        filter: {
          ...state.filter,
          date: {
            ...state.filter.date,
            ...action.payload,
          },
        },
      };
    },
    updateFilterState(state, action) {
      return {
        ...state,
        filter: {
          ...state.filter,
          state: action.payload,
        },
      };
    },

    // Add
    updateStructureAndState(state, action) {
      return {
        ...state,
        add: {
          ...state.add,
          structure: action.payload?.structure,
          state: action.payload?.state,
        },
      };
    },

    updateState(state, action) {
      return {
        ...state,
        add: {
          ...state.add,
          state: action.payload,
        },
      };
    },

    //Edit
    updateEditStructureAndState(state, action) {
      return {
        ...state,
        edit: {
          ...state?.edit,
          structure: action.payload?.structure,
          state: action.payload?.state,
          id: action.payload?.id,
        },
      };
    },

    updateEditState(state, action) {
      return {
        ...state,
        edit: {
          ...state?.edit,
          state: action.payload,
        },
      };
    },

    // Analytics
    updateAnalytics(state, action) {
      return {
        ...state,
        analytics: {
          ...state?.analytics,
          [action.payload?.key]: action?.payload?.value,
        },
      };
    },

    updateAnalyticsContent(state, action) {
      return {
        ...state,
        analytics: {
          ...state?.analytics,
          ...action.payload,
        },
      };
    },
  },
});

export const PurchaseSliceActions = PurchaseSlice.actions;
export default PurchaseSlice;

export const purchaseFetchHeaderDataThunk = () => {
  return async (dispatch) => {
    let response = await api_purchase_headercount();
    if (response?.length) {
      dispatch(PurchaseSliceActions?.updateHeaderData(response));
    }
  };
};

export const purchaseListingFetchThunk = () => {
  return async (dispatch) => {
    return;
  };
};

export const purchaseFilterArrayFetchThunk = () => {
  return async (dispatch) => {
    return;
  };
};

export const purchaseFilterListFetchThunk = (filter) => {
  return async (dispatch) => {
    return;
  };
};
