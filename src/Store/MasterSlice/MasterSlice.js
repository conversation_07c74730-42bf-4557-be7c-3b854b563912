import { createSlice } from "@reduxjs/toolkit";
import { userListFetchHelperFunction } from "../TicketsSlice/TicketsSlice";
import { customerListFetchHelperFunction } from "../CustomerSlices/CustomerSlice";

export const Master_Roles = {
  admin: "Admin",
  user: "User",
  customer: "Customer",
};
const initialState = {
  auth: {
    isAuth: false,
    id: "",
    username: "",
    token: "",
    mobilephone: "",
    email: "",
    role: "",
  },
  access: {
    isSuperUser: false,
    user: {
      read: true,
      write: true,
      insights: true,
      edit: true,
      delete: true,
    },
    purchase: {
      read: true,
      write: true,
      insights: true,
      edit: true,
      delete: true,
    },
    sales: {
      read: true,
      write: true,
      insights: true,
      edit: true,
      delete: true,
    },
    marketing: {
      read: true,
      write: true,
      insights: true,
      edit: true,
      delete: true,
    },
    orders: {
      read: true,
      write: true,
      insights: true,
      edit: true,
      delete: true,
    },
    customers: {
      read: true,
      write: true,
      insights: true,
      edit: true,
      delete: true,
    },
    networking: {
      read: true,
      write: true,
      insights: true,
      edit: true,
      delete: true,
    },
    serviceComplaints: {
      read: true,
      write: true,
      insights: true,
      edit: true,
      delete: true,
    },
    tickets: {
      read: true,
      write: true,
      insights: true,
      edit: true,
      delete: true,
    },
    taskManagement: {
      read: true,
      write: true,
      insights: true,
      edit: true,
      delete: true,
    },
    sap: {
      read: true,
      write: true,
      insights: true,
      edit: true,
      delete: true,
    },
  },

  ui: {
    isExtended: false,
    isMobileScreenOpen: false,

    userList: [],
    customerList: [],
  },
};

const MasterSlice = createSlice({
  name: "MASTER_SLICE_STATE_MANAGEMENT",
  initialState,
  reducers: {
    updateAuthObj(state, action) {
      const obj = action.payload;
      return {
        ...state,
        auth: {
          ...state.auth,
          isAuth: true,
          id: obj?.id,
          username: obj?.username,
          token: obj?.token,
          mobilephone: obj.mobilephone,
          email: obj?.email,
          role: obj?.role,
        },
      };
    },

    updateAccessIsAdmin(state, action) {
      return {
        ...state,
        access: {
          ...state.access,
          isAdmin: action.payload,
        },
      };
    },

    // Access
    updateAccess(state, action) {
      return { ...state, access: { ...state.access, ...action.payload } };
    },

    updateAccessElements(state, action) {
      return {
        ...state,
        access: {
          ...state.access,
          // isAdmin: false,
          leads: action.payload.leads,
          customers: action.payload.customers,
          orderDetails: action.payload.orderDetails,
          products: action.payload.products,
          reports: action.payload.reports,
        },
      };
    },

    modifyIsExtended(state, action) {
      return {
        ...state,
        ui: {
          ...state.ui,
          isExtended: action.payload,
        },
      };
    },

    modifyIsMobileScreenOpen(state, action) {
      return {
        ...state,
        ui: {
          ...state.ui,
          isMobileScreenOpen: action.payload,
        },
      };
    },

    updateUserList(state, action) {
      return {
        ...state,
        ui: {
          ...state.ui,
          userList: action.payload,
        },
      };
    },

    updateCustomerList(state, action) {
      return {
        ...state,
        ui: {
          ...state.ui,
          customerList: action.payload,
        },
      };
    },
  },
});

export const MasterSliceActions = MasterSlice.actions;
export default MasterSlice;

// ------------------------------------------------------------

export const updateUserListThunkMaster = () => {
  return async (dispatch) => {
    let userList = await userListFetchHelperFunction();
    dispatch(MasterSliceActions.updateUserList(userList));
  };
};

export const updateCustomerListThunkMaster = () => {
  return async (dispatch) => {
    let customerList = await customerListFetchHelperFunction();
    let array = [];
    for (let item of customerList) {
      array.push({
        id: item.id,
        content: item.id,
      });
    }
    dispatch(MasterSliceActions.updateCustomerList(array));
  };
};

// ---------------- Middle ware to update master auth obj and other side effects -------------------------

export const update_auth_obj_master_slice_thunk = (authObj) => {
  return (dispatch) => {
    dispatch(MasterSliceActions.updateAuthObj(authObj));

    dispatch(updateCustomerListThunkMaster());
    dispatch(updateUserListThunkMaster());

    //setLoginAuth_LocalStorage(authObj); // Setting Local storage
  };
};

// ----------- Token ---- LOCAL STORAGE AUTHENTICATION

let key = "loginAuth";

export const setLoginAuth_LocalStorage = (obj, access) => {
  let setObj = {
    username: obj.username,
    email: obj.email, // Email Is not present in login api (Admin )
    token: obj.token,
    id: obj?.id,
    mobilephone: obj.mobilephone,
    role: obj.role,
    setTime: new Date(),
    access,
  };

  localStorage.setItem(key, JSON.stringify(setObj));
};

//---- Helper function
function isWithinLast24Hours(date) {
  const now = new Date();
  const twentyFourHoursAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
  return date >= twentyFourHoursAgo && date <= now;
}

export const getLoginAuth_LocalStorage = () => {
  let localStorageAuth = localStorage.getItem(key);

  if (!localStorageAuth) return false;

  localStorageAuth = JSON.parse(localStorageAuth);
  let obj = {
    id: localStorageAuth?.id,
    username: localStorageAuth?.username,
    mobilephone: localStorageAuth?.mobilephone,
    token: localStorageAuth?.token,
    email: localStorageAuth?.email,
    role: localStorageAuth?.role,
    access: localStorageAuth?.access,
  };
  let settedTime = localStorageAuth?.setTime;
  if (isWithinLast24Hours(new Date(settedTime))) {
    return obj;
  }

  return false;
};
