import { configureStore } from "@reduxjs/toolkit";

import userEditSlice from "./UserSlices/UserEditSlice";
import userViewSlice from "./UserSlices/UserViewSlice";
import UserInviteUserSlice from "./UserSlices/UserInviteUser";
import AdminSigninSlice from "./AuthenticationSlices/AdminSlices/AdminSigninSlice";
import AdminSignupSlice from "./AuthenticationSlices/AdminSlices/AdminSignupSlice";

import MasterSlice from "./MasterSlice/MasterSlice";
import UserMainSlice from "./UserSlices/UserMainSlice";
import ServiceComplaintsSlice from "./ServiceComplaintsSlice/ServiceComplaintSlice";
import SalesAndMarkeingSlice from "./SalesAndMarketingSlice/SalesAndMarketingSlice";
import OrderManagementSlice from "./OrderManagementSlice/OrderManagementSlice";
import PurchaseSlice from "./PurchaseSlices/PurchaseSlice";
import TicketsSlice from "./TicketsSlice/TicketsSlice";
import customerSlice from "./CustomerSlices/CustomerSlice";
import singleCustomerSlice from "./CustomerSlices/singleCustomerSlice";

import CustomerResetPasswordSlice from "./AuthenticationSlices/CustomerSlices/CustomerResetPasswordSlice";
import CustomerEditSlice from "./CustomerSlices/CustomerEditSlice";
import taskManagementSlice from "./TaskManagement/taskManagementSlice";
import taskMangementGridSlice from "./TaskManagement/TaskmanagementGridSlice";
import singleCustomerBillTrackingSlice from "./CustomerSlices/singleCustomerBillTracking";
import NetworkingSlice from "./NetworkingSlice/NetworkingSlice";
import marketingSlice from "./MarketingSlice/MarketingSlices";
import CustomerDashboardOrderSlice from "./CustomerDashboardSlices/CustomerDashboardOrderSlice";
import marketingWalkinSlice from "./MarketingSlice/MarketingWalkinSlice";

export const store = configureStore({
  reducer: {
    userView: userViewSlice.reducer,
    userEdit: userEditSlice.reducer,
    userinviteuser: UserInviteUserSlice.reducer,
    userMain: UserMainSlice.reducer,

    adminSignIn: AdminSigninSlice.reducer,
    adminSignUp: AdminSignupSlice.reducer,

    customerResetPassword: CustomerResetPasswordSlice.reducer,

    customer: customerSlice.reducer,
    customerEdit: CustomerEditSlice.reducer,
    singlecustomer: singleCustomerSlice.reducer,
    singlecustomerbilltracking: singleCustomerBillTrackingSlice.reducer,

    taskManagement: taskManagementSlice.reducer,
    taskManagementGrid: taskMangementGridSlice.reducer,

    customerDashboardOrders: CustomerDashboardOrderSlice?.reducer,

    orderManagement: OrderManagementSlice.reducer, // Order Management Dashboard

    // Master Slice -> Store : auth, access
    master: MasterSlice.reducer,
    // Dashboard Optimized
    serviceComplaints: ServiceComplaintsSlice.reducer, // Service Complaints Dashboard : list, add, edit, headerdata, filter
    salesAndMarketing: SalesAndMarkeingSlice.reducer, // Sales Dashboard : list, add, edit, headerdata, filter, analytics, table
    purchase: PurchaseSlice.reducer, // Purchase Dashboard : list, add, edit, headerdata, filter, analytics
    tickets: TicketsSlice.reducer, // Ticket Dashboard : list, add, edit, headerdata, filter
    marketing: marketingSlice.reducer, // Marketing Dashboard : list, add, edit, headerdata, filter, analytics
    marketingWalkin: marketingWalkinSlice.reducer, // Marketing Dashboard : list, add, edit, headerdata, filter
    networking: NetworkingSlice?.reducer, // Networking Dashboard, : list, add
  },
});
