import React from "react";
import NotificationSVG from "../../Assest/SVG/NotificationSVG";
import { useSelector } from "react-redux";

const PurchaseAnalyticsThirdSectionChart = () => {
  const {
    NumberofTrucks,
    MaterialsUnloaded,
    PurchaseEntry,
    PurchaseAgainstOrder,
  } = useSelector((state) => {
    return state.purchase?.analytics;
  });

  return (
    <div className=" w-full h-[600px] md:h-[300px] flex flex-col md:flex-row justify-evenly items-center   gap-[1rem]  flex-shrink-0 flex-wrap">
      <div className="w-[320px] h-[96px] rounded-[8px] bg-[#DBF0F2] flex flex-row  justify-start">
        <div className=" w-[80px] h-full flex flex-col justify-center items-center">
          <div className=" w-[51px] h-[51px] rounded-[50%] bg-greenButtonGradient flex flex-row justify-center items-center">
            <NotificationSVG />
          </div>
        </div>

        <div className=" w-fit h-full flex flex-col justify-center gap-[0.2rem]">
          <h1 className=" text-[#192434] text-[12px] font-inter font-[600]">
            Purchase Against Order
          </h1>
          <p className=" text-[24px] font-inter font-[700]">
            {PurchaseAgainstOrder}
          </p>
        </div>
      </div>
      <div className="w-[220px] h-[96px] rounded-[8px] bg-[#DBF0F2] flex flex-row justify-start">
        <div className=" w-[80px] h-full flex flex-col justify-center items-center">
          <div className=" w-[51px] h-[51px] rounded-[50%] bg-greenButtonGradient flex flex-row justify-center items-center">
            <NotificationSVG />
          </div>
        </div>

        <div className=" w-fit h-full flex flex-col justify-center gap-[0.2rem]">
          <h1 className=" text-[#192434] text-[12px] font-inter font-[600]">
            Materials Unloaded
          </h1>
          <p className=" text-[24px] font-inter font-[700]">
            {MaterialsUnloaded}
          </p>
        </div>
      </div>
      <div className="w-[220px] h-[96px] rounded-[8px] bg-[#DBF0F2] flex flex-row justify-start">
        <div className=" w-[80px] h-full flex flex-col justify-center items-center">
          <div className=" w-[51px] h-[51px] rounded-[50%] bg-greenButtonGradient flex flex-row justify-center items-center">
            <NotificationSVG />
          </div>
        </div>

        <div className=" w-fit h-full flex flex-col justify-center gap-[0.2rem]">
          <h1 className=" text-[#192434] text-[12px] font-inter font-[600]">
            Purchase Entry
          </h1>
          <p className=" text-[24px] font-inter font-[700]">{PurchaseEntry}</p>
        </div>
      </div>
      <div className="w-[220px] h-[96px] rounded-[8px] bg-[#DBF0F2] flex flex-row justify-start">
        <div className=" w-[80px] h-full flex flex-col justify-center items-center">
          <div className=" w-[51px] h-[51px] rounded-[50%] bg-greenButtonGradient flex flex-row justify-center items-center">
            <NotificationSVG />
          </div>
        </div>

        <div className=" w-fit h-full flex flex-col justify-center gap-[0.2rem]">
          <h1 className=" text-[#192434] text-[12px] font-inter font-[600]">
            Number of Trucks
          </h1>
          <p className=" text-[24px] font-inter font-[700]">{NumberofTrucks}</p>
        </div>
      </div>
    </div>
  );
};

export default PurchaseAnalyticsThirdSectionChart;
