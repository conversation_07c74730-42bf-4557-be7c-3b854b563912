import React from "react";
import ReactECharts from "echarts-for-react";
import { useSelector } from "react-redux";

const StockPurchasedVsStockSold2 = ({ content = {} }) => {
  let option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      top: "90%",
      left: "10%",
      data: ["Stock Purchased", "Stock Sold"],
    },

    xAxis: [
      {
        type: "category",
        axisTick: { show: false },
        data: content?.unloadingDate,
      },
    ],
    yAxis: [
      {
        type: "value",
      },
    ],
    series: [
      {
        name: "Stock Purchased",
        type: "bar",
        barGap: 0,
        emphasis: {
          focus: "series",
        },
        data: content?.StockPurchased,
      },
      {
        name: "Stock Sold",
        type: "bar",
        emphasis: {
          focus: "series",
        },
        data: content?.StockSold,
      },
    ],

    color: ["#4C49ED", "#AFAEFE"],

    dataZoom: [
      {
        type: "inside",
        id: "insideX",
        xAxisIndex: 0,
        start: 0,
        end: 10,
        zoomOnMouseWheel: false,
        moveOnMouseMove: true,
        moveOnMouseWheel: true,
      },
    ],
  };

  return (
    <div className=" w-full h-full ">
      <div className=" font-inter text-[20px] font-[700] ps-[10%]">
        Stock Purchased Vs Stock Sold
      </div>
      <ReactECharts option={option} />
    </div>
  );
};

const MSLVsOutofStockVsDamaged2 = ({ content = {} }) => {
  let option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      top: "90%",
      left: "10%",
      data: ["MSL", "B", "C"],
    },

    xAxis: [
      {
        type: "category",
        axisTick: { show: false },
        data: content?.unloadingDate,
      },
    ],
    yAxis: [
      {
        type: "value",
      },
    ],
    series: [
      {
        name: "MSL",
        type: "bar",
        barGap: 0,
        emphasis: {
          focus: "series",
        },
        data: content?.MSL,
      },
      {
        name: "B",
        type: "bar",
        emphasis: {
          focus: "series",
        },
        data: content?.B,
      },
      {
        name: "C",
        type: "bar",
        emphasis: {
          focus: "series",
        },
        data: content?.c,
      },
    ],

    color: ["#820263", "#D90368", "#FB8B24"],

    dataZoom: [
      {
        type: "inside",
        id: "insideX",
        xAxisIndex: 0,
        start: 0,
        end: 10,
        zoomOnMouseWheel: false,
        moveOnMouseMove: true,
        moveOnMouseWheel: true,
      },
    ],
  };

  return (
    <div className=" w-full h-full ">
      <div className=" font-inter text-[20px] font-[700] ps-[10%]">
        MSL Vs B Vs C
      </div>
      <ReactECharts option={option} />
    </div>
  );
};

const PurchaseAnalyticsFourthSectionChart = () => {
  const { StockPurchasedVsStockSold, MSLVsOutofStockVsDamaged } = useSelector(
    (state) => {
      return state.purchase?.analytics;
    }
  );
  return (
    <div className=" w-full h-[800px] md:h-[400px] flex flex-col md:flex-row justify-center">
      <div className=" w-full md:w-[50%] h-full ">
        <StockPurchasedVsStockSold2 content={StockPurchasedVsStockSold} />
      </div>
      <div className=" w-full  md:w-[50%] h-full ">
        <MSLVsOutofStockVsDamaged2 content={MSLVsOutofStockVsDamaged} />
      </div>
    </div>
  );
};

export default PurchaseAnalyticsFourthSectionChart;
