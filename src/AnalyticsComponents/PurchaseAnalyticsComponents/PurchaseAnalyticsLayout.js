import React, { useEffect } from "react";
import DashboardHeaderTemplate from "../../Components/DashBoardTemplate/DashboardHeaderTemplate";
import PurchaseAnalyticsFirstSectionChart from "./PurchaseAnalyticsFirstSectionChart";
import PurchaseAnalyticsSecondSectionChart from "./PurchaseAnalyticsSecondSectionChart";
import PurchaseAnalyticsThirdSectionChart from "./PurchaseAnalyticsThirdSectionChart";
import PurchaseAnalyticsFourthSectionChart from "./PurchaseAnalyticsFourthSectionChart";
import PurchaseAnalyticsFivthSection from "./PurchaseAnalyticsFivthSection";
import { useDispatch, useSelector } from "react-redux";

import PurchaseAnalyticsDateFilter from "./PurchaseAnalyticsDateFilter";
import PurchaseSixthSectionChart from "./PurchaseSixthSectionChart";
import UseRouterAuthentication from "../../Hooks/UseRouterAuthentication";
import DashboardNavbar_template from "../../UI_templates/Dashboard_Templates/DashboardNavbar_template";
import { routes } from "../../Config/routesConfig";
import UsePurchase from "../../Components/PurchaseComponents/UsePurchase";
const PurchaseAnalyticsLayout = () => {
  const { auth, access } = useSelector((state) => {
    return state.master;
  });

  // Router Authentication
  const { isAnalyticsAccessable } = UseRouterAuthentication();

  const { fetchList, fetchAnalytics } = UsePurchase();

  useEffect(() => {
    isAnalyticsAccessable("purchase");

    // Fetch Listing
    fetchList();
    fetchAnalytics();
  }, [auth, access]);

  return (
    <div className=" w-full h-full flex flex-col ">
      <DashboardHeaderTemplate heading="Purchase" />
      <DashboardNavbar_template
        listLink={routes?.purchase?.directLink}
        insightsLink={routes?.purchaseAnalytics?.directLink}
        dashboard="purchase"
      />
      <div className=" flex-1  overflow-hidden">
        <div className=" w-full h-full overflow-y-auto overflow-x-hidden">
          <PurchaseAnalyticsDateFilter />
          <PurchaseAnalyticsFirstSectionChart />
          <PurchaseAnalyticsSecondSectionChart />
          <PurchaseAnalyticsThirdSectionChart />
          <PurchaseAnalyticsFourthSectionChart />
          <PurchaseSixthSectionChart />
          <PurchaseAnalyticsFivthSection />
        </div>
      </div>
    </div>
  );
};

export default PurchaseAnalyticsLayout;
