import React from "react";
import ReactECharts from "echarts-for-react";
import { convertFloatToDaysHours } from "../OrderManagementsAnalyticsComponents/OrderManagementAnalyticsSecondSection";
import { useSelector } from "react-redux";
const PurchaseAnalyticsSecondSectionChart = () => {
  const { AverageStockDeliveryTime, OnTimeDeliveryDelayedCommitments } =
    useSelector((state) => {
      return state.purchase?.analytics;
    });

  const option = {
    tooltip: {
      trigger: "item",
    },
    //   legend: {
    //        top: "5%",
    //        left: "center",
    //   },
    series: [
      {
        name: "",
        type: "pie",
        radius: ["130%", "90%"],
        center: ["50%", "70%"],
        // adjust the start and end angle
        startAngle: 180,
        endAngle: 360,
        data: [
          {
            value: OnTimeDeliveryDelayedCommitments?.OnTimeDelivery,
            name: "On Time Delivery",
          },
          {
            value: OnTimeDeliveryDelayedCommitments?.DelayedCommitments,
            name: "Delayed Commitments",
          },
        ],
        color: ["#FD8C5D", "#FFCA58"],
      },
    ],
  };
  return (
    <div className=" w-full lg:h-[300px] h-[600px] flex flex-col lg:flex-row justify-evenly mt-[1rem]">
      <div className=" lg:w-[20%] w-full lg:h-full h-fit flex flex-col lg:items-start items-center justify-center ">
        <div className=" w-[305px] h-[94px] rounded-[6px] flex flex-col justify-evenly items-center bg-[#EB7F000F] bg-opacity-[6%] ">
          <h1 className="  font-[700] text-[16px] text-[#222222]">
            Average Stock Delivery Time
          </h1>
          <p className=" font-[800] font-inter text-[24px]">
            {convertFloatToDaysHours(AverageStockDeliveryTime)}
          </p>
        </div>
      </div>
      <div className=" w-full lg:w-[50%] lg:h-full h-[300px]">
        <ReactECharts option={option} />
      </div>

      <div className=" w-full lg:w-[20%] lg:h-full h-fit flex flex-col justify-center lg:items-start items-center">
        <div className=" w-fit h-fit flex flex-row gap-[7vw]">
          {/* First Section */}
          <div className=" flex flex-col items-center gap-[0.5rem]">
            <h2 className=" text-[16px] font-[700] font-inter  ">
              {OnTimeDeliveryDelayedCommitments?.OnTimeDelivery}
            </h2>
            <div
              style={{ backgroundColor: "#FD8C5D" }}
              className=" w-[25px] h-[25px] rounded-[3px]"
            ></div>
            <h2 className=" text-[14px] text-[#6E7079] font-[700] font-inter  ">
              On Time Delivery
            </h2>
          </div>
          {/* Second Section */}
          <div className=" flex flex-col items-center gap-[0.5rem]">
            <h2 className=" text-[16px] font-[700] font-inter  ">
              {OnTimeDeliveryDelayedCommitments?.DelayedCommitments}
            </h2>
            <div
              style={{ backgroundColor: "#FFCA58" }}
              className=" w-[25px] h-[25px] rounded-[3px]"
            ></div>
            <h2 className=" text-[14px] text-[#6E7079] font-[700] font-inter  ">
              Delayed Commitments
            </h2>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PurchaseAnalyticsSecondSectionChart;
