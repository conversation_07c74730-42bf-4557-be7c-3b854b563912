import React from "react";
import ReactECharts from "echarts-for-react";
import { useDispatch, useSelector } from "react-redux";

import AnalyticsTable from "../Utils/AnalyticsTable";
import SelectComponent from "../../BasicUIElements/SelectComponent";
import { PurchaseSliceActions } from "../../Store/PurchaseSlices/PurchaseSlice";
import UsePurchase from "../../Components/PurchaseComponents/UsePurchase";

const BrandVsSize = ({ content = {}, optionarr = {} }) => {
  const dispatch = useDispatch();
  const { fetchAnalytics } = UsePurchase();
  const analytics = useSelector((state) => {
    return state.purchase?.analytics;
  });
  let brand = content?.brand;
  let size = content?.size;
  let quantity = content?.quantity;

  let array = [];

  for (let i = 0; i < size?.length; i++) {
    array?.push({
      name: size[i],
      type: "bar",
      barGap: 0,
      emphasis: {
        focus: "series",
      },
      data: quantity[i],
    });
  }

  let option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      top: "90%",
      left: "10%",
      data: size,
    },

    xAxis: [
      {
        type: "category",
        axisTick: { show: false },
        axisLabel: {
          interval: 0,
          rotate: 0,
        },
        data: brand,
      },
    ],
    yAxis: [
      {
        type: "value",
      },
    ],

    series: array,

    color: ["#820263", "#D90368", "#FB8B24"],

    dataZoom: [
      {
        type: "inside",
        id: "insideX",
        xAxisIndex: 0,
        start: 0,
        end: 50,
        zoomOnMouseWheel: false,
        moveOnMouseMove: true,
        moveOnMouseWheel: true,
      },
    ],
  };

  return (
    <div className=" w-full h-full ">
      <div className=" w-full font-inter text-[20px] font-[700] ps-[10%] flex flex-row gap-[1rem] justify-evenly flex-wrap items-center">
        <div>Brand Vs Size</div>
        <div></div>
        <div className=" w-[175px] h-fit">
          <SelectComponent
            className=" w-[200px] h-[48px] border-2 rounded-[8px]"
            value={optionarr}
            optionarr={[
              { id: "Category 1", content: "Category 1" },
              { id: "Category 2", content: "Category 2" },
            ]}
            updateFunction={(e) => {
              dispatch(
                PurchaseSliceActions?.updateAnalyticsContent({
                  brandVsSizeOptions: e,
                  brandVsSize: {
                    brand: [],
                    size: [],
                    quantity: [],
                  },
                })
              );
              setTimeout(() => {
                fetchAnalytics({
                  ...analytics,
                  brandVsSizeOptions: e,
                });
              }, 1);
            }}
          />
        </div>
      </div>
      <ReactECharts option={option} />
    </div>
  );
};

const PurchaseSixthSectionChart = () => {
  const { topSizes, brandVsSize, brandVsSizeOptions } = useSelector((state) => {
    return state.purchase?.analytics;
  });

  return (
    <div className=" w-full h-[800px] md:h-[400px] flex flex-col md:flex-row justify-center">
      <div className=" w-full md:w-[50%] h-full ">
        <BrandVsSize content={brandVsSize} optionarr={brandVsSizeOptions} />
      </div>
      <div className=" w-full  md:w-[50%] h-full ">
        <div className=" w-full  h-fit flex flex-row justify-center ">
          <AnalyticsTable
            title="Top Sizes"
            firstRow={{
              title: "Top Sizes",
              array: topSizes?.id,
            }}
            secondRow={{
              title: "Total Quantities",
              array: topSizes?.totalQuantity,
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default PurchaseSixthSectionChart;
