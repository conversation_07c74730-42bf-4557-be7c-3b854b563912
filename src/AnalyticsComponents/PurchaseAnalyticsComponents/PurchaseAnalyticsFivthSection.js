import React from "react";
import AnalyticsTable from "../Utils/AnalyticsTable";
import { useSelector } from "react-redux";

const PurchaseAnalyticsFivthSection = () => {
  const {
    topBrands,
    topCategories,
    topSubCategories,
    topProducts,
    topGrades,
    topDivisions,
    topMaterials,
    topBatches,
    topWarehouseStatus,
    topNatureOfOrders,
  } = useSelector((state) => {
    return state.purchase?.analytics;
  });

  return (
    <div className=" w-full h-fit flex flex-col md:flex-row flex-wrap flex-shrink-0 justify-evenly ">
      <div className=" w-full lg:w-[50%] md:w-[45%] h-fit flex flex-row justify-center ">
        <AnalyticsTable
          title="Top Brands   "
          firstRow={{
            title: "Top Brands  ",
            array: topBrands.id,
          }}
          secondRow={{
            title: "Total Quantities",
            array: topBrands?.totalQuantity,
          }}
        />
      </div>
      <div className=" w-full lg:w-[50%] md:w-[45%] h-fit flex flex-row justify-center ">
        <AnalyticsTable
          title="Top Categories"
          firstRow={{
            title: "Top Categories",
            array: topCategories?.id,
          }}
          secondRow={{
            title: "Total Quantities",
            array: topCategories.totalQuantity,
          }}
        />
      </div>

      <div className=" w-full lg:w-[50%] md:w-[45%] h-fit flex flex-row justify-center ">
        <AnalyticsTable
          title="Top Sub categories"
          firstRow={{
            title: "Top Sub categories",
            array: topSubCategories.id,
          }}
          secondRow={{
            title: "Total Quantities",
            array: topSubCategories?.totalQuantity,
          }}
        />
      </div>
      <div className=" w-full lg:w-[50%] md:w-[45%] h-fit flex flex-row justify-center ">
        <AnalyticsTable
          title="Top Products"
          firstRow={{
            title: "Top Products",
            array: topProducts.id,
          }}
          secondRow={{
            title: "Total Quantities",
            array: topProducts?.totalQuantity,
          }}
        />
      </div>
      <div className=" w-full lg:w-[50%] md:w-[45%] h-fit flex flex-row justify-center ">
        <AnalyticsTable
          title="Top Batches"
          firstRow={{
            title: "Top Batches",
            array: topBatches?.id,
          }}
          secondRow={{
            title: "Total Quantities",
            array: topBatches?.totalQuantity,
          }}
        />
      </div>

      <div className=" w-full lg:w-[50%] md:w-[45%] h-fit flex flex-row justify-center ">
        <AnalyticsTable
          title="Top Materials"
          firstRow={{
            title: "Top Materials",
            array: topMaterials?.id,
          }}
          secondRow={{
            title: "Total Quantities",
            array: topMaterials?.totalQuantity,
          }}
        />
      </div>

      <div className=" w-full lg:w-[50%] md:w-[45%] h-fit flex flex-row justify-center ">
        <AnalyticsTable
          title="Top Divisions"
          firstRow={{
            title: "Top Divisions",
            array: topDivisions?.id,
          }}
          secondRow={{
            title: "Total Quantities",
            array: topDivisions?.totalQuantity,
          }}
        />
      </div>
      <div className=" w-full lg:w-[50%] md:w-[45%] h-fit flex flex-row justify-center ">
        <AnalyticsTable
          title="Top Grades"
          firstRow={{
            title: "Top Grades",
            array: topGrades?.id,
          }}
          secondRow={{
            title: "Total Quantities",
            array: topGrades?.totalQuantity,
          }}
        />
      </div>
      <div className=" w-full lg:w-[50%] md:w-[45%] h-fit flex flex-row justify-center ">
        <AnalyticsTable
          title="top Nature Of Orders"
          firstRow={{
            title: "top Nature Of Orders",
            array: topNatureOfOrders.id,
          }}
          secondRow={{
            title: "Total Quantities",
            array: topNatureOfOrders?.totalQuantity,
          }}
        />
      </div>

      <div className=" w-full lg:w-[50%] md:w-[45%] h-fit flex flex-row justify-center ">
        <AnalyticsTable
          title="Top Ware House Status "
          firstRow={{
            title: "Top Ware House Status ",
            array: topWarehouseStatus.id,
          }}
          secondRow={{
            title: "Total Quantities",
            array: topWarehouseStatus?.totalQuantity,
          }}
        />
      </div>
    </div>
  );
};

export default PurchaseAnalyticsFivthSection;
