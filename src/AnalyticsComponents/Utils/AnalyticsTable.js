import React from "react";
import ButtonComponent from "../../BasicUIElements/ButtonComponent";

const RowElement = ({ title = "", array = [] }) => {
  return (
    <div className=" flex-1 h-fit flex flex-col gap-[1rem]">
      <div className=" w-full h-fit text-start font-inter text-[#1E2022] text-[16px] truncate ">
        {title}
      </div>
      {array.map((item) => {
        return (
          <div className=" w-full h-[30px] text-start font-inter text-[#77838F] text-[14px] overflow-x-auto ">
            {/* {item === "" ? (item === 0 ? item : "Null") : item} */}
            {item}
          </div>
        );
      })}
    </div>
  );
};

const AnalyticsTable = ({
  title = "",
  firstRow = { title: 0, array: [] },
  secondRow = false,
  thirdRow = false,
  fourthRow = false,
}) => {
  let array = [];

  return (
    <div className=" md:w-[410px] w-[85%] h-[320px] rounded-[8px] overflow-y-auto border-[#F0EFEF] border-[2px] flex flex-col justify-between   mb-[8rem]">
      <div className=" w-full h-fit flex flex-col gap-[1rem]">
        <h1 className="text-[#222222] text-[16px] font-inter font-[600] p-4">
          {title}
        </h1>

        <div className="w-full h-fit flex flex-row gap-[1rem] p-4">
          {/* <div className=" w-full h-fit flex flex-row justify-between ">
                         <div className=" w-[33%] h-fit text-start font-inter text-[#1E2022] text-[16px] ">
                              Sales Person
                         </div>
                         <div className=" w-[33%] h-fit text-center font-inter text-[#1E2022] text-[16px] ">
                              Quantity Sold
                         </div>
                         <div className="  w-[33%] h-fit text-end font-inter text-[#1E2022] text-[16px] ">
                              Incentive
                         </div>
                    </div>

                    <div className=" w-full h-fit flex flex-row justify-between ">
                         <div className=" w-[33%] h-fit text-start font-inter text-[#77838F] text-[14px] ">
                              Person 1
                         </div>
                         <div className=" w-[33%] h-fit text-center  font-inter text-[#77838F] text-[14px] ">
                              7,000
                         </div>
                         <div className="  w-[33%] h-fit text-end  font-inter text-[#77838F] text-[14px] ">
                              ₹4,650
                         </div>
                    </div> */}

          {/* <div className=" w-[33%] h-fit flex flex-col gap-[1rem]">
                              <div className=" w-full h-fit text-start font-inter text-[#1E2022] text-[16px] truncate ">
                                   Sales Person
                              </div>
                              <div className=" w-full h-fit text-start font-inter text-[#77838F] text-[14px] overflow-x-auto ">
                                   Person 1
                              </div>
                         </div> */}
          {/* <div className=" w-[33%] h-fit flex flex-col gap-[1rem]">
                              <div className=" w-full h-fit text-center font-inter text-[#1E2022] text-[16px] truncate ">
                                   Quantity Sold
                              </div>
                              <div className=" w-full h-fit text-start  font-inter text-[#77838F] text-[14px] overflow-x-auto ">
                                   7,000
                                   </div>
                                   </div>
                         <div className=" w-[33%] h-fit flex flex-col gap-[1rem]">
                              <div className="  w-full h-fit text-end font-inter text-[#1E2022] text-[16px] truncate ">
                              Incentive
                              </div>
                              <div className="  w-full h-fit text-end  font-inter text-[#77838F] text-[14px] overflow-x-auto ">
                              ₹4,650
                              </div>
                            </div> */}
          {firstRow && (
            <RowElement title={firstRow?.title} array={firstRow?.array} />
          )}
          {secondRow && (
            <RowElement title={secondRow?.title} array={secondRow?.array} />
          )}
          {thirdRow && (
            <RowElement title={thirdRow?.title} array={thirdRow?.array} />
          )}
          {fourthRow && (
            <RowElement title={fourthRow?.title} array={fourthRow?.array} />
          )}
        </div>
      </div>
      {/* <div className=" w-full p-4 ">
                    <ButtonComponent className=" border-[2px] border-[#019BA2] w-[82px] h-[25px] rounded-[8px] font-inter font-[500] text-[10px] text-[#019BA2]">
                         View All
                    </ButtonComponent>
               </div> */}
    </div>
  );
};

export default AnalyticsTable;
