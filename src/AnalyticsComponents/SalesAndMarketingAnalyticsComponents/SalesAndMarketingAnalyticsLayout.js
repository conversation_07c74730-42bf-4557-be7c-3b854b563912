import React, { useEffect } from "react";
import DashboardHeaderTemplate from "../../Components/DashBoardTemplate/DashboardHeaderTemplate";
import FirstSectionPieChart from "./FirstSectionPieChart";
import SalesAndMarketingAnalyticsSecondBarChart from "./SalesAndMarketingAnalyticsSecondBarChart";
import SalesAndMarketingAnalyticsThirdPieCharts from "./SalesAndMarketingAnalyticsThirdPieCharts";
import SalesAndMarketingFourthChartBar from "./SalesAndMarketingFourthChartBar";
import SalesAndMarketingAnalyticsFivthBarLineChart from "./SalesAndMarketingAnalyticsFivthBarLineChart";
import SalesAndMarketingAnalyticsSixthSectionChart from "./SalesAndMarketingAnalyticsSixthSectionChart";
import SalesAndMarketingDateFilter from "./SalesAndMarketingDateFilter";
import UseRouterAuthentication from "../../Hooks/UseRouterAuthentication";
import DashboardNavbar_template from "../../UI_templates/Dashboard_Templates/DashboardNavbar_template";
import { routes } from "../../Config/routesConfig";
import UseSalesAndMarketing from "../../Hooks/UseSalesAndMarketing";
import { useSelector } from "react-redux";

const SalesAndMarketingAnalyticsLayout = () => {
  const { auth, access } = useSelector((state) => {
    return state.master;
  });
  const { isAnalyticsAccessable } = UseRouterAuthentication();

  const { fetchAnalytics } = UseSalesAndMarketing();

  useEffect(() => {
    fetchAnalytics();
  }, [auth, access]);

  useEffect(() => {
    isAnalyticsAccessable("sales");
  }, [auth, access]);

  return (
    <div className=" w-full h-full flex flex-col ">
      <DashboardHeaderTemplate heading="Sales" />
      <DashboardNavbar_template
        listLink={routes?.salesandmarketing?.directLink}
        insightsLink={routes?.salesAndMarketingAnalytics?.directLink}
        dashboard="sales"
      />
      <div className=" flex-1  overflow-hidden">
        <div className=" w-full h-full overflow-y-auto overflow-x-hidden">
          <SalesAndMarketingDateFilter />
          <FirstSectionPieChart />
          <SalesAndMarketingAnalyticsSecondBarChart />
          <SalesAndMarketingAnalyticsThirdPieCharts />
          <SalesAndMarketingFourthChartBar />
          <SalesAndMarketingAnalyticsFivthBarLineChart />
          <SalesAndMarketingAnalyticsSixthSectionChart />
        </div>
      </div>
    </div>
  );
};

export default SalesAndMarketingAnalyticsLayout;
