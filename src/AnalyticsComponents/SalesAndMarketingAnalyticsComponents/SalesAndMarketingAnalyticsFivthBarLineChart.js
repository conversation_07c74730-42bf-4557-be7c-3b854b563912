import React from "react";
import ReactECharts from "echarts-for-react";
import { useSelector } from "react-redux";

const SalesTrendBar = ({ content }) => {
  let option = {
    xAxis: {
      type: "category",
      data: content.sales,
    },
    yAxis: {
      type: "value",
    },
    tooltip: {
      trigger: "item",
      formatter: "{b} : {c}",
    },
    title: {
      text: "Sales Trend",
      left: "10%",
    },
    series: [
      {
        data: content.count,
        type: "line",
        smooth: true,
      },
    ],

    color: ["#4416C6"],
  };
  return (
    <div className=" w-full h-full ">
      <ReactECharts option={option} />
    </div>
  );
};

const CityBarChart = ({ content }) => {
  const seriesLabel = {
    show: true,
  };
  let option = {
    title: {
      text: "Top 5 sales Cities ",
      left: "10%",
    },

    grid: {
      left: 100,
    },
    toolbox: {
      show: true,
      feature: {
        saveAsImage: {},
      },
    },
    xAxis: {
      type: "value",
      name: "Days",
      axisLabel: {
        formatter: "{value}",
      },
    },
    yAxis: {
      type: "category",
      inverse: true,
      data: content?.city,
    },
    series: [
      {
        name: "Sales",
        type: "bar",
        data: content?.count,
        label: seriesLabel,
      },
    ],
  };
  return (
    <div className=" w-full h-full ">
      <ReactECharts option={option} />
    </div>
  );
};

const SalesAndMarketingAnalyticsFivthBarLineChart = () => {
  const { top5, salesTrend } = useSelector(
    (state) => state.salesAndMarketing?.analytics
  );
  return (
    <div className=" w-full h-[800px] md:h-[400px] flex flex-col  md:flex-row justify-center">
      <div className="w-full md:w-[50%] h-full">
        <SalesTrendBar content={salesTrend} />
      </div>
      <div className="w-full md:w-[50%] h-full ">
        <CityBarChart content={top5} />
      </div>
    </div>
  );
};

export default SalesAndMarketingAnalyticsFivthBarLineChart;
