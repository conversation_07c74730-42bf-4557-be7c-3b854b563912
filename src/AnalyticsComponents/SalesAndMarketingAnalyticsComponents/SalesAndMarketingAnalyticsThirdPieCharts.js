import React from "react";
import ReactECharts from "echarts-for-react";
import { useSelector } from "react-redux";

const SalesPulse = ({ content }) => {
  let option = {
    legend: {
      top: "30%",
      left: "75%",
      orient: "vertical",
    },
    tooltip: {
      trigger: "item",
      formatter: "{b} : {c} ({d}%)",
    },

    series: [
      {
        name: "Nightingale Chart",
        type: "pie",
        radius: [10, 100],
        center: ["40%", "50%"],
        roseType: "area",
        itemStyle: {
          borderRadius: 8,
        },
        data: [
          { value: content?.new, name: "New" },
          { value: content?.potential, name: "Potential" },
          { value: content?.dropped, name: "Dropped" },
          { value: content?.closed, name: "Closed" },

          // {
          //   value: content?.QUOTE_SENT,
          //   name: "Quotation Sent",
          // },
          // { value: content?.dropped, name: "Dropped" },
          // { value: content?.IN_FOLLOW, name: "In Follow" },
          // { value: content?.WALK_IN, name: "Walk In" },
          // { value: content?.IN_FOLLOW, name: "Dropped" },
          // { value: content?.FIELD_VISIT, name: "Field Visit" },
        ],

        color: ["#5368F0", "#9D57D5", "#EB7F00", "#FDC171"],
      },
    ],
  };

  let total =
    content?.new + content?.potential + content?.dropped + content?.closed;

  return (
    <div className=" w-full h-full ">
      <div className=" w-full h-fit flex flex-col gap-[1rem] ">
        <h1 className=" font-inter text-[20px] font-[700] ps-[10%]">
          Sales Pulse
        </h1>

        <p className=" font-inter text-[14px] font-[700] ps-[10%]">
          Total Leads: {total}
        </p>
      </div>
      <ReactECharts option={option} />
    </div>
  );
};

const LeadSource = ({ content }) => {
  let option = {
    legend: {
      top: "30%",
      left: "75%",
      orient: "vertical",
    },

    tooltip: {
      trigger: "item",
      formatter: "{b} : {c} ({d}%)",
    },

    series: [
      {
        name: "Nightingale Chart",
        type: "pie",
        radius: [10, 100],
        center: ["40%", "50%"],
        roseType: "area",
        itemStyle: {
          borderRadius: 8,
        },
        data: [
          {
            value: content["INDIVIDUAL RESIDENT"],
            name: "INDIVIDUAL RESIDENT",
          },
          { value: content?.RESIDENT, name: "RESIDENT" },
          { value: content?.APARTMENT, name: "APARTMENT" },
          { value: content?.BUNGALOW, name: "BUNGALOW" },
          { value: content?.COMMERCIAL, name: "COMMERCIAL" },
          { value: content?.INDIVIDUAL, name: "INDIVIDUAL" },
          { value: content?.PROJECT, name: "PROJECT" },
        ],

        color: ["#5368F0", "#9D57D5", "#EB7F00", "#FEFDED"],
      },
    ],
  };

  let total =
    content["INDIVIDUAL RESIDENT"] +
    content?.RESIDENT +
    content?.APARTMENT +
    content?.BUNGALOW +
    content?.COMMERCIAL +
    content?.INDIVIDUAL +
    content?.PROJECT;
  return (
    <div className=" w-full h-full ">
      <div className=" w-full h-fit flex flex-col gap-[1rem] ">
        <h1 className=" font-inter text-[20px] font-[700] ps-[10%]">
          Lead Source
        </h1>

        <p className=" font-inter text-[14px] font-[700] ps-[10%]">
          Total Leads:
          {total}
        </p>
      </div>
      <ReactECharts option={option} />
    </div>
  );
};

const SalesAndMarketingAnalyticsThirdPieCharts = () => {
  const { salesPulse, leadSource } = useSelector(
    (state) => state.salesAndMarketing?.analytics
  );
  return (
    <div className=" w-full h-[800px] md:h-[400px] flex md:flex-row flex-col justify-center">
      <div className=" w-full md:w-[50%] h-full">
        <SalesPulse content={salesPulse} />
      </div>
      <div className=" w-full md:w-[50%] h-full">
        <LeadSource content={leadSource} />
      </div>
    </div>
  );
};

export default SalesAndMarketingAnalyticsThirdPieCharts;
