import React from "react";
import SelectComponent from "../../BasicUIElements/SelectComponent";
import DatePickerComponents from "../../Components/Utils/DatePickerComponents";
import { useDispatch, useSelector } from "react-redux";
import { SalesAndMarketingSliceActions } from "../../Store/SalesAndMarketingSlice/SalesAndMarketingSlice";
import UseSalesAndMarketing, {
  dateObj,
} from "../../Hooks/UseSalesAndMarketing";

const SalesAndMarketingDateFilter = () => {
  const { startDate, endDate, filter } = useSelector((state) => {
    return state.salesAndMarketing?.analytics;
  });

  const dispatch = useDispatch();

  const { fetchAnalytics } = UseSalesAndMarketing();

  const updateAnalyticsObj = (obj) => {
    dispatch(SalesAndMarketingSliceActions?.updateAnalytics(obj));
    fetchAnalytics({ startDate, endDate, filter, [obj.key]: obj.value });
  };
  return (
    <div className=" w-full flex flex-row justify-between pe-[2rem] pt-[8px]">
      <div></div>
      <div className={` w-fit h-fit flex flex-col gap-[0.5rem]  `}>
        <SelectComponent
          optionarr={dateObj}
          value={filter}
          className=" w-[200px] h-[40px]"
          updateFunction={(e) => {
            updateAnalyticsObj({ key: "filter", value: e });
          }}
        />

        <div
          className={` w-[200px] h-[45px] flex-shrink-0 flex flex-row  justify-between gap-[0.25rem] ${
            "Custom Date" === filter.content ? " flex" : " hidden"
          } `}
        >
          <DatePickerComponents
            title="Start Date"
            className=" relative  border-2 w-[90px] h-[52px] rounded-[8px] bg-[#F6F7FB] flex flex-row justify-center gap-[1rem]"
            value={startDate}
            isLogoPresent={false}
            onChange={(e) => {
              updateAnalyticsObj({ key: "startDate", value: e.target.value });
            }}
          />
          <DatePickerComponents
            title="End Date"
            className=" relative  border-2 w-[90px] h-[52px] rounded-[8px] bg-[#F6F7FB] flex flex-row justify-center gap-[1rem]"
            value={endDate}
            isLogoPresent={false}
            onChange={(e) => {
              updateAnalyticsObj({ key: "endDate", value: e.target.value });
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default SalesAndMarketingDateFilter;
