import React from "react";
import AnalyticsTable from "../Utils/AnalyticsTable";
import { useSelector } from "react-redux";

const SalesAndMarketingAnalyticsSixthSectionChart = () => {
  const { topSalesPerson, topSellingProducts } = useSelector(
    (state) => state.salesAndMarketing?.analytics
  );

  return (
    <div className=" w-full h-fit flex flex-col md:flex-row justify-evenly ">
      <div className=" w-full md:w-[50%] h-fit flex flex-row justify-center ">
        <AnalyticsTable
          title="Top Sales Persons"
          firstRow={{
            title: "Sales Person",
            array: topSalesPerson.name,
          }}
          secondRow={{
            title: "Quantity Sold",
            array: topSalesPerson.totalQuantitySold,
          }}
          thirdRow={{
            title: "Total Order Value",
            array: topSalesPerson.totalOrderValue,
          }}
          // fourthRow={{
          //      title: "Incentive",
          //      array: topSalesPerson.incentive,
          // }}
        />
      </div>
      <div className=" w-full md:w-[50%] h-fit flex flex-row justify-center ">
        <AnalyticsTable
          title="Products Vs Quantity"
          firstRow={{
            title: "Products",
            array: topSellingProducts.name,
          }}
          secondRow={{
            title: "Quantity",
            array: topSellingProducts.totalQuantity,
          }}
        />
      </div>
    </div>
  );
};

export default SalesAndMarketingAnalyticsSixthSectionChart;
