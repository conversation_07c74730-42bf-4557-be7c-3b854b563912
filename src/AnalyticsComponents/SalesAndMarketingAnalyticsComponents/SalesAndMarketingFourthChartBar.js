import React from "react";
import ReactECharts from "echarts-for-react";
import NotificationSVG from "../../Assest/SVG/NotificationSVG";
import { useSelector } from "react-redux";
import { indiaCurrencyFormat } from "../../Config/AnalyticsConfig";
const WalkinStatus = ({ content }) => {
  let option = {
    legend: {
      top: "30%",
      left: "75%",
      orient: "vertical",
    },
    tooltip: {
      trigger: "item",
      formatter: "{b} : {c} ({d}%)",
    },

    series: [
      {
        name: "Nightingale Chart",
        type: "pie",
        radius: [10, 100],
        center: ["40%", "50%"],
        roseType: "area",
        itemStyle: {
          borderRadius: 8,
        },
        data: [
          { value: content?.DNSP, name: "DNSP" },

          {
            value: content?.DBF,
            name: "DBF",
          },
          { value: content?.DUS, name: "DUS" },
          { value: content?.DC, name: "DC" },
        ],

        color: ["#5368F0", "#9D57D5", "#EB7F00", "#FDC171"],
      },
    ],    
  };

  return (
    <div className=" w-full h-full ">
      <div className=" font-inter text-[20px] font-[700] ps-[10%]">
        Walk-in Status - Dcermic Vs DNSP Vs DBF
      </div>
      <ReactECharts option={option} />
    </div>
  );
};

const AOVCHART = ({ content }) => {
  let end = 50;
  if (content.date?.length > 20) {
    end = 5;
  } else if (content.date?.length < 5) {
    end = 100;
  }

  let option = {
    legend: {
      top: "90%",
      left: "10%",
      data: ["AVERAGE ORDER VALUE"],
    },

    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },

    xAxis: [
      {
        type: "category",
        axisTick: { show: false },
        axisLabel: {
          interval: 0,
          rotate: 0,
        },
        data: content.date,
      },
    ],
    yAxis: [
      {
        type: "value",
      },
    ],
    series: [
      {
        name: "AVERAGE ORDER VALUE",
        type: "bar",
        barGap: 0,
        emphasis: {
          focus: "series",
        },
        data: content.data,
      },
    ],

    color: ["#1446A0", "#D90368", "#F5D547"],

    dataZoom: [
      {
        type: "inside",
        id: "insideX",
        xAxisIndex: 0,
        start: 0,
        end: end,
        zoomOnMouseWheel: false,
        moveOnMouseMove: true,
        moveOnMouseWheel: true,
      },
    ],
  };

  return (
    <div className=" w-full h-full ">
      <div className=" font-inter text-[20px] font-[700] ps-[10%]">
        Average Order Value
      </div>
      <ReactECharts option={option} />
    </div>
  );
};

const SalesAndMarketingFourthChartBar = () => {
  const { Walk_in_Status, AOVvsDay } = useSelector(
    (state) => state.salesAndMarketing?.analytics
  );
  return (
    <div className=" w-full h-[800px] md:h-[400px] flex flex-col md:flex-row justify-center">
      <div className=" w-full md:w-[50%] h-full ">
        <WalkinStatus content={Walk_in_Status} />
      </div>
      <div className=" w-full md:w-[50%] h-full ">
        <AOVCHART content={AOVvsDay} />
      </div>
    </div>
  );
};

export default SalesAndMarketingFourthChartBar;
