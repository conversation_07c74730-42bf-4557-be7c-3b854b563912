import React from "react";
import ReactECharts from "echarts-for-react";
import { useSelector } from "react-redux";
function convertFloatToDaysHours(floatDays) {
  if (floatDays <= 0) {
    return "0 Days";
  }

  // Calculate days and hours
  const days = Math.floor(floatDays);
  const hours = Math.round((floatDays - days) * 24);

  // Construct the string result
  const daysText = days > 0 ? `${days} Days` : "";
  const hoursText = hours > 0 ? `${hours} Hours` : "";

  // Combine days and hours text with space in between, if both are present
  return [daysText, hoursText].filter(Boolean).join(" ");
}

const FirstSectionPieChart = () => {
  const { targetData, daysLeadClosure } = useSelector(
    (state) => state.salesAndMarketing?.analytics
  );
  const option = {
    tooltip: {
      trigger: "item",
    },
    //   legend: {
    //        top: "5%",
    //        left: "center",
    //   },
    series: [
      {
        name: "",
        type: "pie",
        radius: ["130%", "90%"],
        center: ["50%", "70%"],
        // adjust the start and end angle
        startAngle: 180,
        endAngle: 360,
        data: [
          { value: targetData?.targetAchieved, name: "Target Acheived " },
          { value: targetData?.pendingTargets, name: "Pending Targets" },
        ],
        color: ["#6BD4D4", "#D29AFF"],
      },
    ],
  };
  return (
    <div className=" w-full h-[600px] lg:h-[300px] flex flex-col lg:flex-row justify-evenly">
      <div className=" lg:w-[20%] w-full h-full flex flex-col justify-center lg:items-start items-center ">
        <div className=" w-[205px] h-[94px] rounded-[6px] flex flex-col justify-evenly items-center bg-[#2F80ED] bg-opacity-[16%] ">
          <h1 className="  font-[700] text-[16px] text-[#222222]">
            Days Sales Closure
          </h1>
          <p className=" font-[800] font-inter text-[20px]">
            {convertFloatToDaysHours(daysLeadClosure)}
          </p>
        </div>
      </div>
      <div className=" w-full lg:w-[50%] h-full">
        <ReactECharts option={option} />
      </div>

      <div className=" w-full lg:w-[20%] h-full flex flex-col justify-center lg:items-start items-center">
        <div className=" w-fit h-fit flex flex-row gap-[4vw]">
          {/* First Section */}
          <div className=" flex flex-col items-center gap-[0.5rem]">
            <h2 className=" text-[16px] font-[700] font-inter  ">
              {targetData?.targetAchieved}
            </h2>
            <div
              style={{ backgroundColor: "#6BD4D4" }}
              className=" w-[25px] h-[25px] rounded-[3px]"
            ></div>
            <h2 className=" text-[14px] text-[#6E7079] font-[700] font-inter  ">
              Target Achieved
            </h2>
          </div>
          {/* Second Section */}
          <div className=" flex flex-col items-center gap-[0.5rem]">
            <h2 className=" text-[16px] font-[700] font-inter  ">
              {targetData?.pendingTargets}
            </h2>
            <div
              style={{ backgroundColor: "#D29AFF" }}
              className=" w-[25px] h-[25px] rounded-[3px]"
            ></div>
            <h2 className=" text-[14px] text-[#6E7079] font-[700] font-inter  ">
              Pending Targets
            </h2>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FirstSectionPieChart;
