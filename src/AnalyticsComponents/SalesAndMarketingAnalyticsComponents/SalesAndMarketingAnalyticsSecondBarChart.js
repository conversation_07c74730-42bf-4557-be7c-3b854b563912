import React from "react";
import ReactECharts from "echarts-for-react";
import { useSelector } from "react-redux";

const LeadAndClosureComponent = ({ content }) => {
  let id = content?.date;
  let leads = content?.leads;
  let closures = content?.closures;

  let end = 50;

  if (id?.length > 20) {
    end = 5;
  } else if (id?.length < 5) {
    end = 100;
  }

  let option = {
    legend: {
      top: "90%",
      left: "10%",
      data: ["Leads", "Closures"],
    },

    tooltip: {
      trigger: "item",
    },

    xAxis: [
      {
        type: "category",
        axisTick: { show: false },
        axisLabel: {
          interval: 1,
          rotate: 0,
        },
        data: [...id],
      },
    ],
    yAxis: [
      {
        type: "value",
      },
    ],
    series: [
      {
        name: "Leads",
        type: "bar",
        barGap: 0,
        emphasis: {
          focus: "series",
        },
        data: leads,
      },
      {
        name: "Closures",
        type: "bar",
        emphasis: {
          focus: "series",
        },
        data: closures,
      },
    ],

    color: ["#FF8140", "#FFCC63"],

    dataZoom: [
      {
        type: "inside",
        id: "insideX",
        xAxisIndex: 0,
        start: 0,
        end: end,
        zoomOnMouseWheel: false,
        moveOnMouseMove: true,
        moveOnMouseWheel: true,
      },
    ],
  };

  return (
    <div className=" w-full h-full ">
      <div className=" font-inter text-[20px] font-[700] ps-[10%]">
        Leads Vs Closures
      </div>
      <ReactECharts option={option} />
    </div>
  );
};

const TargetsVsAchievedComponent = ({ content }) => {
  let option = {
    legend: {
      top: "90%",
      left: "10%",
      data: ["NEW", "POTENTIAL"],
    },

    tooltip: {
      trigger: "item",
    },

    xAxis: [
      {
        type: "category",
        axisTick: { show: false },
        axisLabel: {
          interval: 1,
          rotate: 0,
        },
        data: content?.date,
      },
    ],
    yAxis: [
      {
        type: "value",
      },
    ],
    series: [
      {
        name: "POTENTIAL",
        type: "bar",
        barGap: 0,
        emphasis: {
          focus: "series",
        },
        data: content?.POTENTIAL,
      },
      {
        name: "NEW",
        type: "bar",
        emphasis: {
          focus: "series",
        },
        data: content?.NEW,
      },
    ],

    color: ["#B84F8B", "#F1A3CF"],

    dataZoom: [
      {
        type: "inside",
        id: "insideX",
        xAxisIndex: 0,
        start: 0,
        end: 75,
        zoomOnMouseWheel: false,
        moveOnMouseMove: true,
        moveOnMouseWheel: true,
      },
    ],
  };

  return (
    <div className=" w-full h-full ">
      <div className=" font-inter text-[20px] font-[700] ps-[10%]">
        Targets Vs Achieved
      </div>
      <ReactECharts option={option} />
    </div>
  );
};
const SalesAndMarketingAnalyticsSecondBarChart = () => {
  const { leadClosure, TargetsVsAchieved } = useSelector(
    (state) => state.salesAndMarketing?.analytics
  );
  return (
    <div className=" w-full md:h-[400px] h-[800px] flex flex-col md:flex-row justify-center">
      <div className=" w-full md:w-[50%] h-full ">
        <LeadAndClosureComponent content={leadClosure} />
      </div>
      <div className=" w-full md:w-[50%] h-full ">
        <TargetsVsAchievedComponent content={TargetsVsAchieved} />{" "}
      </div>
    </div>
  );
};

export default SalesAndMarketingAnalyticsSecondBarChart;
