import React, { useEffect } from "react";
import DashboardHeaderTemplate from "../../Components/DashBoardTemplate/DashboardHeaderTemplate";
import OrderManagementAnalyticsFirstSectionChart from "./OrderManagementAnalyticsFirstSectionChart";
import OrderManagementAnalyticsSecondSection from "./OrderManagementAnalyticsSecondSection";
import OrderManagementAnalyticsThirdSectionCharts from "./OrderManagementAnalyticsThirdSectionCharts";
import OrderManagementAnalyticsSixthSectionChart from "./OrderManagementAnalyticsSixthSectionChart";
import { useSelector } from "react-redux";

import OrderManagementAnalyticsDateFilter from "./OrderManagementAnalyticsDateFilter";
import OrderManagementAnalyticsSeventhSection from "./OrderManagementAnalyticsSeventhSection";
import UseRouterAuthentication from "../../Hooks/UseRouterAuthentication";
import { routes } from "../../Config/routesConfig";
import DashboardNavbar_template from "../../UI_templates/Dashboard_Templates/DashboardNavbar_template";
import useOrderManagement from "../../Components/OrderManagementComponents/useOrderManagement";

const OrderManagementAnalyticsLayout = () => {
  const { auth, access } = useSelector((state) => {
    return state.master;
  });

  // Router Authentication
  const { isAnalyticsAccessable } = UseRouterAuthentication();
  const { fetchAnalytics } = useOrderManagement();
  useEffect(() => {
    isAnalyticsAccessable("orders");

    fetchAnalytics();
  }, [auth, access]);

  return (
    <div className=" w-full h-full flex flex-col ">
      <DashboardHeaderTemplate heading="Order Management" />
      <DashboardNavbar_template
        listLink={routes?.orderManagement?.directLink}
        insightsLink={routes?.orderManagementAnalytics?.directLink}
        dashboard="orders"
      />
      <div className=" flex-1  overflow-hidden">
        <div className=" w-full h-full overflow-y-auto overflow-x-hidden pt-[1rem]">
          <OrderManagementAnalyticsDateFilter />
          <OrderManagementAnalyticsFirstSectionChart />
          <OrderManagementAnalyticsSecondSection />
          <OrderManagementAnalyticsThirdSectionCharts />
          <OrderManagementAnalyticsSeventhSection />
          <OrderManagementAnalyticsSixthSectionChart />
        </div>
      </div>
    </div>
  );
};

export default OrderManagementAnalyticsLayout;
