import React from "react";
import ReactECharts from "echarts-for-react";
import { useSelector } from "react-redux";
const WalkinStatus = ({ content }) => {
  let end = 50;

  if (content.date?.length > 20) {
    end = 5;
  } else if (content.date?.length < 5) {
    end = 100;
  }

  let option = {
    barWidth: "30%",
    barCategoryGap: "10%",
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      top: "90%",
      left: "10%",
      data: ["Orders", "Available", "Out of stock"],
    },

    xAxis: [
      {
        type: "category",
        axisTick: { show: false },
        axisLabel: {
          interval: 1,
          rotate: 0,
        },
        data: content.date,
      },
    ],
    yAxis: [
      {
        type: "value",
      },
    ],
    series: [
      {
        name: "Orders",
        type: "bar",
        barGap: 0,
        emphasis: {
          focus: "series",
        },
        data: content.orderCount,
      },
      {
        name: "Available",
        type: "bar",
        emphasis: {
          focus: "series",
        },
        data: content.Available,
      },
      {
        name: "Out of stock",
        type: "bar",
        emphasis: {
          focus: "series",
        },
        data: content.out_of_stock,
      },
    ],

    color: ["#1446A0", "#D90368", "#F5D547"],

    dataZoom: [
      {
        type: "inside",
        id: "insideX",
        xAxisIndex: 0,
        start: 0,
        end: end,
        zoomOnMouseWheel: false,
        moveOnMouseMove: true,
        moveOnMouseWheel: true,
      },
    ],
  };

  return (
    <div className=" w-full h-full ">
      <div className=" font-inter text-[20px] font-[700] ps-[10%]">
        Orders Vs Available Vs Out-Of-Stock
      </div>
      <ReactECharts option={option} />
    </div>
  );
};

const CityBarChart = ({ content = {} }) => {
  const seriesLabel = {
    show: true,
  };
  let option = {
    title: {
      text: "Top 5 sales Cities ",
      left: "10%",
    },

    grid: {
      left: 100,
    },

    xAxis: {
      type: "value",
      name: "Days",
      axisLabel: {
        formatter: "{value}",
      },
    },
    yAxis: {
      type: "category",
      inverse: true,
      data: content?.location,
    },
    series: [
      {
        name: "Sales",
        type: "bar",
        data: content?.count,
        label: seriesLabel,
      },
    ],
  };
  return (
    <div className=" w-full h-full ">
      <ReactECharts option={option} />
    </div>
  );
};

const OrderManagementAnalyticsThirdSectionCharts = () => {
  const { Orderavailableoutofstock, orderByLocation } = useSelector((state) => {
    return state.orderManagement?.analytics;
  });
  return (
    <div className=" w-full h-[800px] md:h-[400px] flex md:flex-row flex-col justify-center">
      <div className=" md:w-[50%] w-full h-full ">
        <WalkinStatus content={Orderavailableoutofstock} />
      </div>
      <div className=" md:w-[50%] w-full h-full ">
        <CityBarChart content={orderByLocation} />
      </div>
    </div>
  );
};

export default OrderManagementAnalyticsThirdSectionCharts;
