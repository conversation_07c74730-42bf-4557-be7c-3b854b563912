import React from "react";
import { useNavigate } from "react-router-dom";
import ButtonComponent from "../../BasicUIElements/ButtonComponent";
import { routes } from "../../Config/routesConfig";

const OrderManagementAnalyticsNavbar = () => {
     const navigate = useNavigate();
     return (
          <div className=" w-full h-[44px] border-b-2 border-[#EBEBEB]">
               <div className=" w-fit h-full flex flex-row justify-start ">
                    <ButtonComponent
                         className=" w-[94px] h-full font-poppins font-[400] text-[16px] text-[#00BD94] border-b-[3px] border-[#00BD94] "
                         onClick={() => {
                              navigate(
                                   routes.orderManagementAnalytics.directLink
                              );
                         }}
                    >
                         Insigths
                    </ButtonComponent>
                    <ButtonComponent
                         onClick={() => {
                              navigate(routes.orderManagement.directLink);
                         }}
                         className=" w-[94px] h-full font-poppins font-[400] text-[16px] text-[#212529] border-b-[3px] border-[#212529]"
                    >
                         List
                    </ButtonComponent>
               </div>
          </div>
     );
};

export default OrderManagementAnalyticsNavbar;
