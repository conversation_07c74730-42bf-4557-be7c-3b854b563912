import React from "react";
import ReactECharts from "echarts-for-react";
import { useSelector } from "react-redux";

export function convertFloatToDaysHours(floatDays) {
  if (floatDays <= 0) {
    return "0 Days";
  }

  // Calculate days and hours
  const days = Math.floor(floatDays);
  const hours = Math.round((floatDays - days) * 24);

  // Construct the string result
  const daysText = days > 0 ? `${days} Days` : "";
  const hoursText = hours > 0 ? `${hours} Hours` : "";

  // Combine days and hours text with space in between, if both are present
  return [daysText, hoursText].filter(Boolean).join(" ");
}

const OrderManagementAnalyticsSecondSection = () => {
  const { averageDeliveryTime, commitment } = useSelector((state) => {
    return state.orderManagement?.analytics;
  });
  const option = {
    tooltip: {
      trigger: "item",
    },

    series: [
      {
        name: "",
        type: "pie",
        radius: ["130%", "90%"],
        center: ["50%", "70%"],
        // adjust the start and end angle
        startAngle: 180,
        endAngle: 360,
        data: [
          {
            //   value: commitment.commitmentsHonored,
            value: 90,
            name: "Commitments Honoured",
          },
          {
            value: commitment.commitmentsDelayed,
            name: "Commitments Delayed",
          },
        ],
        color: ["#C47AFF", "#FFCA58"],
      },
    ],
  };
  return (
    <div className=" w-full lg:h-[300px] h-[600px] flex lg:flex-row flex-col justify-evenly">
      <div className=" lg:w-[20%] w-full h-full flex lg:flex-col flex-row justify-center ">
        <div className=" w-[205px] h-[94px] rounded-[6px] flex flex-col justify-evenly items-center bg-[#2F80ED] bg-opacity-[16%] ">
          <h1 className="  font-[700] text-[16px] text-[#222222]">
            Average Delivery time
          </h1>
          <p className=" font-[800] font-inter text-[20px]">
            {convertFloatToDaysHours(averageDeliveryTime)}
          </p>
        </div>
      </div>
      <div className=" lg:w-[50%] w-full h-full">
        <ReactECharts option={option} />
      </div>

      <div className=" lg:w-[20%] w-full h-full flex flex-col justify-center lg:items-start items-center">
        <div className=" w-fit h-fit flex flex-row gap-[4vw]">
          {/* First Section */}
          <div className=" flex flex-col items-center gap-[0.5rem]">
            <h2 className=" text-[16px] font-[700] font-inter  ">
              {commitment.commitmentsHonored}
            </h2>
            <div
              style={{ backgroundColor: "#C47AFF" }}
              className=" w-[25px] h-[25px] rounded-[3px]"
            ></div>
            <h2 className=" text-[14px] text-[#6E7079] font-[700] font-inter  ">
              Commitments Honoured
            </h2>
          </div>
          {/* Second Section */}
          <div className=" flex flex-col items-center gap-[0.5rem]">
            <h2 className=" text-[16px] font-[700] font-inter  ">
              {commitment.commitmentsDelayed}
            </h2>
            <div
              style={{ backgroundColor: "#FFCA58" }}
              className=" w-[25px] h-[25px] rounded-[3px]"
            ></div>
            <h2 className=" text-[14px] text-[#6E7079] font-[700] font-inter  ">
              Commitments Delayed
            </h2>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderManagementAnalyticsSecondSection;
