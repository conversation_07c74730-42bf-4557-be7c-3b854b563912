import React, { useState } from "react";
import ReactECharts from "echarts-for-react";
import { useSelector } from "react-redux";
import { indiaCurrencyFormat } from "../../Config/AnalyticsConfig";

const OrderOverView = ({ content }) => {
  let option = {
    legend: {
      top: "30%",
      left: "75%",
      orient: "vertical",
    },

    tooltip: {
      trigger: "item",
      formatter: "{b} : {c} ({d}%)",
    },

    series: [
      {
        name: "Order Overview",
        type: "pie",
        radius: [10, 100],
        center: ["40%", "50%"],
        roseType: "area",
        itemStyle: {
          borderRadius: 8,
        },
        data: [
          { value: content.delivered, name: "Delivered" },
          {
            value: content.readyForDelivery,
            name: "Ready for Delivery",
          },
          {
            value: content.pendingForStock,
            name: "Pending for Stock",
          },
          {
            value: content.damaged,
            name: "Damaged Products",
          },
        ],

        color: ["#51AF7D", "#46B9FA", "#FE6733", "#F9ECB3"],
      },
    ],
  };

  let totalOrder = content.Total_orders;
  return (
    <div className=" w-full h-full ">
      <div className=" w-full h-fit flex flex-col gap-[1rem] ">
        <h1 className=" font-inter text-[20px] font-[700] ps-[10%]">
          Order Overview
        </h1>

        <p className=" font-inter text-[14px] font-[700] ps-[10%]">
          Total orders: {totalOrder}
        </p>
      </div>
      <ReactECharts option={option} />
    </div>
  );
};

const TotalOrderVsPaymentReceived = ({ content }) => {
  let option = {
    legend: {
      top: "30%",
      left: "75%",
      orient: "vertical",
    },

    tooltip: {
      trigger: "item",
      formatter: "{b} : {c} ({d}%)",
    },

    series: [
      {
        name: "Total Order Value vs Payments received",
        type: "pie",
        radius: [10, 100],
        center: ["40%", "50%"],
        roseType: "area",
        itemStyle: {
          borderRadius: 8,
        },
        data: [
          {
            value: content.totalPaymentReceived,
            name: "Payment Received",
          },
          {
            value: content?.paymentOutstanding,
            name: "Payment Outstanding",
          },
        ],

        color: ["#6BAA7B", "#FDC171"],
      },
    ],
  };
  let total = indiaCurrencyFormat(content?.totalOrderValue);
  return (
    <div className=" w-full h-full ">
      <div className=" w-full h-fit flex flex-col gap-[1rem] ">
        <h1 className=" font-inter text-[20px] font-[700] ps-[10%]">
          Total Order Value vs Payments received
        </h1>

        <p className=" font-inter text-[14px] font-[700] ps-[10%]">
          Total Order Value: {total}
        </p>
      </div>
      <ReactECharts option={option} />
    </div>
  );
};

const OrderManagementAnalyticsFirstSectionChart = () => {

  const { orderOverview, totalOrderValueVsPaymentReceived } = useSelector(
    (state) => {
      return state?.orderManagement?.analytics;
    }
  );

  return (
    <div className=" w-full md:h-[400px] h-[800px] flex flex-col md:flex-row justify-center">
      <div className=" md:w-[50%] w-full h-full">
        <OrderOverView content={orderOverview} />
      </div>
      <div className=" md:w-[50%] w-full h-full">
        <TotalOrderVsPaymentReceived
          content={totalOrderValueVsPaymentReceived}
        />
      </div>
    </div>
  );
};

export default OrderManagementAnalyticsFirstSectionChart;
