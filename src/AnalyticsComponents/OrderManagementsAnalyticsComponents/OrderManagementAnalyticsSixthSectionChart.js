import React from "react";
import AnalyticsTable from "../Utils/AnalyticsTable";
import { useSelector } from "react-redux";

const OrderManagementAnalyticsSixthSectionChart = () => {

  const {
    OrderSalespersonProductSoldTotalOrder,
    SalespersonProductSoldTotalPaymentReceived,
  } = useSelector((state) => {
    return state?.orderManagement?.analytics;
  });

  return (
    <>
      <div className=" w-full h-fit flex md:flex-row flex-col justify-evenly ">
        <div className=" md:w-[50%] w-full h-fit flex flex-row justify-center ">
          <AnalyticsTable
            title="Sales Persons Vs Total Quantity Vs Total Order"
            firstRow={{
              title: "Sales Person",
              array: OrderSalespersonProductSoldTotalOrder.id,
            }}
            secondRow={{
              title: "Total Quantity",
              array: OrderSalespersonProductSoldTotalOrder.totalQuantity,
            }}
            thirdRow={{
              title: "Total Order",
              array: OrderSalespersonProductSoldTotalOrder?.totalOrder,
            }}
          />
        </div>

        <div className=" md:w-[50%] w-full h-fit flex flex-row justify-center ">
          <AnalyticsTable
            title="Sales Persons Vs Total Quantity Vs Total Payment Received"
            firstRow={{
              title: "Sales Person",
              array: SalespersonProductSoldTotalPaymentReceived.id,
            }}
            secondRow={{
              title: "Total Quantity",
              array: SalespersonProductSoldTotalPaymentReceived.totalQuantity,
            }}
            thirdRow={{
              title: "Total Order",
              array:
                SalespersonProductSoldTotalPaymentReceived?.Total_Payment_Received,
            }}
          />
        </div>
      </div>
    </>
  );
};

export default OrderManagementAnalyticsSixthSectionChart;
