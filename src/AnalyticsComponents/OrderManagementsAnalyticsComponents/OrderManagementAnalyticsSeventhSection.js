import React from "react";

import ReactECharts from "echarts-for-react";
import SelectComponent from "../../BasicUIElements/SelectComponent";
import { useDispatch, useSelector } from "react-redux";

import AnalyticsTable from "../Utils/AnalyticsTable";
const BrandVsSales = ({ content = {} }) => {
  const dispatch = useDispatch();
  //   const orderAnalytics = useSelector((state) => {
  //     return state.orderManagementAnalytics;
  //   });

  //   let ui = orderAnalytics.ui;
  //   let filter = orderAnalytics.filter;

  let end = 10;

  //   if (orderAnalytics.brandvsValue.brand?.length > 20) {
  //     end = 5;
  //   } else if (orderAnalytics.brandvsValue.brand?.length < 5) {
  //     end = 100;
  //   }

  let option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      top: "90%",
      left: "10%",
      data: ["MSL"],
    },

    xAxis: [
      {
        type: "category",
        axisTick: { show: true },
        axisLabel: {
          interval: 0,
          rotate: 0,
        },
        data: content?.brand,
      },
    ],
    yAxis: [
      {
        type: "value",
      },
    ],
    series: [
      {
        name: "Total Quantity",
        type: "bar",
        barGap: 0,
        emphasis: {
          focus: "series",
        },
        data: content?.value,
      },
    ],

    color: ["#FB8B24", "#D90368", "#FB8B24"],

    dataZoom: [
      {
        type: "inside",
        id: "insideX",
        xAxisIndex: 0,
        start: 0,
        end: end,
        zoomOnMouseWheel: false,
        moveOnMouseMove: true,
        moveOnMouseWheel: true,
      },
    ],
  };

  return (
    <div className=" w-full h-full ">
      <div className=" w-full font-inter text-[20px] font-[700] ps-[10%] flex flex-row gap-[1rem] justify-evenly flex-wrap items-center">
        <div>Brand Vs Value</div>
        <div></div>
        {/* <div className=" w-[175px] h-fit">
          <SelectComponent
            optionarr={ui.salesPersonList}
            value={filter?.salesPerson}
            updateFunction={(e) => {}}
            className=" w-[175px] h-[42px] rounded-[4px] border-[2px] border-[#D8E1F2] text-[#53545C] text-[16px] font-[500] font-inter ps-5 pe-5"
            textClassName="text-[#53545C] text-[16px] font-[500] font-inter"
          />
        </div> */}
      </div>
      <ReactECharts option={option} />
    </div>
  );
};

const OrderManagementAnalyticsSeventhSection = () => {
  const { productVsQuantity, brandsValue } = useSelector((state) => {
    return state?.orderManagement?.analytics;
  });

  return (
    <div className=" w-full h-[400px] md:h-[400px] flex flex-col md:flex-row justify-center">
      <div className=" w-full md:w-[50%] h-full md:block hidden ">
        <BrandVsSales content={brandsValue} />
      </div>
      <div className=" w-full  md:w-[50%] h-full ">
        <div className="  w-full h-fit flex flex-row justify-center ">
          <AnalyticsTable
            title="Products Vs Quantity"
            firstRow={{
              title: "Products",
              array: productVsQuantity?.id,
            }}
            secondRow={{
              title: "Quantity",
              array: productVsQuantity.totalQuantity,
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default OrderManagementAnalyticsSeventhSection;
