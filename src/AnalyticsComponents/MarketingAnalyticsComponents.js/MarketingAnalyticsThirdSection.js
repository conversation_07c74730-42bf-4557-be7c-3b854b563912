import React from "react";
import { useSelector } from "react-redux";

import ReactECharts from "echarts-for-react";

const LeadSource = ({ content }) => {
  let option = {
    legend: {
      top: "30%",
      left: "75%",
      orient: "vertical",
    },

    tooltip: {
      trigger: "item",
      formatter: "{b} : {c} ({d}%)",
    },

    series: [
      {
        name: "Nightingale Chart",
        type: "pie",
        radius: [10, 100],
        center: ["40%", "50%"],
        roseType: "area",
        itemStyle: {
          borderRadius: 8,
        },
        data: [
          // {
          //   value: content["INDIVIDUAL RESIDENT"],
          //   name: "INDIVIDUAL RESIDENT",
          // },
          { value: content?.RESIDENT, name: "RESIDENT" },
          { value: content?.APARTMENT, name: "APARTMENT" },
          // { value: content?.BUNGALOW, name: "BUNGALOW" },
          { value: content?.COMMERCIAL, name: "COMMERCIAL" },
          { value: content?.HOSPITALS, name: "HOSPITALS" },
          { value: content?.INDUSTRIES, name: "INDUSTRIES" },
        ],

        color: ["#5368F0", "#9D57D5", "#EB7F00", "#FEFDED"],
      },
    ],
  };

  let total =
    // content["INDIVIDUAL RESIDENT"] +
    content?.RESIDENT +
    content?.APARTMENT +
    // content?.BUNGALOW +
    content?.COMMERCIAL +
    content?.INDIVIDUAL +
    content?.PROJECT;
  return (
    <div className=" w-full h-full ">
      <div className=" w-full h-fit flex flex-col gap-[1rem] ">
        <h1 className=" font-inter text-[20px] font-[700] ps-[10%]">
          Lead Source
        </h1>

        <p className=" font-inter text-[14px] font-[700] ps-[10%]">
          Total Leads:
          {total}
        </p>
      </div>
      <ReactECharts option={option} />
    </div>
  );
};

const WalkinStatus = ({ content }) => {
  let option = {
    legend: {
      top: "30%",
      left: "75%",
      orient: "vertical",
    },
    tooltip: {
      trigger: "item",
      formatter: "{b} : {c} ({d}%)",
    },

    series: [
      {
        name: "Nightingale Chart",
        type: "pie",
        radius: [10, 100],
        center: ["40%", "50%"],
        roseType: "area",
        itemStyle: {
          borderRadius: 8,
        },
        data: [
          { value: content?.DNSP, name: "DNSP" },

          {
            value: content?.DBF,
            name: "DBF",
          },
          { value: content?.DUS, name: "DUS" },
          { value: content?.DC, name: "DC" },
        ],

        color: ["#5368F0", "#9D57D5", "#EB7F00", "#FDC171"],
      },
    ],
  };

  return (
    <div className=" w-full h-full ">
      <div className=" font-inter text-[20px] font-[700] ps-[10%]">
        Walk-in Status - Dcermic Vs DNSP Vs DBF
      </div>
      <ReactECharts option={option} />
    </div>
  );
};

const MarketingAnalyticsThirdSection = () => {
  const { Walk_in_Status, leadSource } = useSelector(
    (state) => state.marketing?.analytics
  );
  return (
    <div className=" w-full h-[800px] md:h-[400px] flex md:flex-row flex-col justify-center">
      <div className=" w-full md:w-[50%] h-full">
        <LeadSource content={leadSource} />
      </div>
      <div className=" w-full md:w-[50%] h-full">
        <WalkinStatus content={Walk_in_Status} />
      </div>
    </div>
  );
};

export default MarketingAnalyticsThirdSection;
