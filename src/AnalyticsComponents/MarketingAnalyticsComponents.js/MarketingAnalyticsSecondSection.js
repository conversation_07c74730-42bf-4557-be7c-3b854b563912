import React from "react";
import ReactECharts from "echarts-for-react";
import { useSelector } from "react-redux";

const LeadAndClosureComponent = ({ content }) => {
  let id = content?.date;
  let leads = content?.leads;
  let closures = content?.closures;

  let end = 50;

  if (id?.length > 20) {
    end = 5;
  } else if (id?.length < 5) {
    end = 100;
  }

  let option = {
    legend: {
      top: "90%",
      left: "10%",
      data: ["Leads", "Closures"],
    },

    tooltip: {
      trigger: "item",
    },

    xAxis: [
      {
        type: "category",
        axisTick: { show: false },
        axisLabel: {
          interval: 1,
          rotate: 0,
        },
        data: [...id],
      },
    ],
    yAxis: [
      {
        type: "value",
      },
    ],
    series: [
      {
        name: "Leads",
        type: "bar",
        barGap: 0,
        emphasis: {
          focus: "series",
        },
        data: leads,
      },
      {
        name: "Closures",
        type: "bar",
        emphasis: {
          focus: "series",
        },
        data: closures,
      },
    ],

    color: ["#FF8140", "#FFCC63"],

    dataZoom: [
      {
        type: "inside",
        id: "insideX",
        xAxisIndex: 0,
        start: 0,
        end: end,
        zoomOnMouseWheel: false,
        moveOnMouseMove: true,
        moveOnMouseWheel: true,
      },
    ],
  };

  return (
    <div className=" w-full h-full ">
      <div className=" font-inter text-[20px] font-[700] ps-[10%]">
        Leads Vs Closures
      </div>
      <ReactECharts option={option} />
    </div>
  );
};

const SalesPulse = ({ content }) => {
  let option = {
    legend: {
      top: "30%",
      left: "75%",
      orient: "vertical",
    },
    tooltip: {
      trigger: "item",
      formatter: "{b} : {c} ({d}%)",
    },

    series: [
      {
        name: "Nightingale Chart",
        type: "pie",
        radius: [10, 100],
        center: ["40%", "50%"],
        roseType: "area",
        itemStyle: {
          borderRadius: 8,
        },
        data: [
          { value: content?.new_lead, name: "New" },

          {
            value: content?.QUOTE_SENT,
            name: "Quotation Sent",
          },
          { value: content?.dropped, name: "Dropped" },
          { value: content?.IN_FOLLOW, name: "In Follow" },
          { value: content?.ORDER_CONFIRM, name: "Order Confirm" },
        ],

        color: ["#5368F0", "#9D57D5", "#EB7F00", "#FDC171"],
      },
    ],
  };

  let total =
    content?.new_lead +
    content?.QUOTE_SENT +
    content?.dropped +
    content?.IN_FOLLOW +
    content?.ORDER_CONFIRM;

  return (
    <div className=" w-full h-full ">
      <div className=" w-full h-fit flex flex-col gap-[1rem] ">
        <h1 className=" font-inter text-[20px] font-[700] ps-[10%]">
          Sales Pulse
        </h1>

        <p className=" font-inter text-[14px] font-[700] ps-[10%]">
          Total Leads: {total}
        </p>
      </div>
      <ReactECharts option={option} />
    </div>
  );
};

const TargetsVsAchievedComponent = ({ content }) => {
  let option = {
    legend: {
      top: "90%",
      left: "10%",
      data: ["In Follow", "Quote Sent"],
    },

    xAxis: [
      {
        type: "category",
        axisTick: { show: false },
        axisLabel: {
          interval: 1,
          rotate: 0,
        },
        data: content?.date,
      },
    ],
    yAxis: [
      {
        type: "value",
      },
    ],
    series: [
      {
        name: "In Follow",
        type: "bar",
        barGap: 0,
        emphasis: {
          focus: "series",
        },
        data: content?.IN_FOLLOW,
      },
      {
        name: "Quote Sent",
        type: "bar",
        emphasis: {
          focus: "series",
        },
        data: content?.QUOTE_SENT,
      },
    ],

    color: ["#B84F8B", "#F1A3CF"],

    dataZoom: [
      {
        type: "inside",
        id: "insideX",
        xAxisIndex: 0,
        start: 0,
        end: 75,
        zoomOnMouseWheel: false,
        moveOnMouseMove: true,
        moveOnMouseWheel: true,
      },
    ],
  };

  return (
    <div className=" w-full h-full ">
      <div className=" font-inter text-[20px] font-[700] ps-[10%]">
        Targets Vs Achieved
      </div>
      <ReactECharts option={option} />
    </div>
  );
};

const MarketingAnalyticsSecondSection = () => {
  const { leadClosure, salesPulse } = useSelector(
    (state) => state.marketing?.analytics
  );
  return (
    <div className=" w-full md:h-[400px] h-[800px] flex flex-col md:flex-row justify-center">
      <div className=" w-full md:w-[50%] h-full ">
        <LeadAndClosureComponent content={leadClosure} />
      </div>
      <div className=" w-full md:w-[50%] h-full ">
        <SalesPulse content={salesPulse} />{" "}
      </div>
    </div>
  );
};

export default MarketingAnalyticsSecondSection;
