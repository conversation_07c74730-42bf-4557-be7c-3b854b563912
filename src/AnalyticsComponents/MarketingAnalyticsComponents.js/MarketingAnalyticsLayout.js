import React, { useEffect } from "react";
import DashboardHeaderTemplate from "../../Components/DashBoardTemplate/DashboardHeaderTemplate";
import DashboardNavbar_template from "../../UI_templates/Dashboard_Templates/DashboardNavbar_template";
import { routes } from "../../Config/routesConfig";
import MarketingDateFilter from "./MarketingDateFilter";
import MarketingAnalyticsFirstSection from "./MarketingAnalyticsFirstSection";
import { useDispatch, useSelector } from "react-redux";
import UseRouterAuthentication from "../../Hooks/UseRouterAuthentication";
import UseMarketing from "../../Hooks/UseMarketing";
import MarketingAnalyticsSecondSection from "./MarketingAnalyticsSecondSection";
import MarketingAnalyticsThirdSection from "./MarketingAnalyticsThirdSection";
import MarketingAnalyticsFourthSection from "./MarketingAnalyticsFourthSection";
import MarketingAnalyticsFivthSection from "./MarketingAnalyticsFivthSection";
import MarketingAnalyticsSixthSection from "./MarketingAnalyticsSixthSection";
import MarketingAnalyticsSeventhSection from "./MarketingAnalyticsSeventhSection";

const MarketingAnalyticsLayout = () => {
  const { auth, access } = useSelector((state) => {
    return state.master;
  });
  const { isAnalyticsAccessable } = UseRouterAuthentication();

  const { fetchAnalytics } = UseMarketing();

  useEffect(() => {
    fetchAnalytics();
  }, [auth, access]);

  useEffect(() => {
    isAnalyticsAccessable("marketing");
  }, [auth, access]);

  return (
    <div className=" w-full h-full flex flex-col ">
      <DashboardNavbar_template
        listLink={routes?.marketing?.directLink}
        insightsLink={routes?.marketingAnalytics?.directLink}
        dashboard="marketing"
        subpages={[
          { title: "Walk In", path: routes?.marketingWalkin.directLink },
        ]}
      />
      <div className=" flex-1  overflow-hidden">
        <div className=" w-full h-full overflow-y-auto overflow-x-hidden">
          <MarketingDateFilter />
          <MarketingAnalyticsFirstSection />
          <MarketingAnalyticsSecondSection />
          <MarketingAnalyticsThirdSection />
          <MarketingAnalyticsFourthSection />
          {/* <MarketingAnalyticsFivthSection /> */}
          {/* <MarketingAnalyticsSixthSection /> */}
          <MarketingAnalyticsSeventhSection />
        </div>
      </div>
    </div>
  );
};

export default MarketingAnalyticsLayout;
