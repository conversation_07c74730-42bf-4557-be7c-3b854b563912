import React from "react";
import ReactECharts from "echarts-for-react";
import { useSelector } from "react-redux";



const MarketingAnalyticsFivthSection = () => {
  const { top5, salesTrend } = useSelector(
    (state) => state.marketing?.analytics
  );
  return (
    <div className=" w-full h-[800px] md:h-[400px] flex flex-col  md:flex-row justify-center">
      <div className="w-full md:w-[50%] h-full">
        {/* <SalesTrendBar content={salesTrend} /> */}
      </div>
      <div className="w-full md:w-[50%] h-full ">
        {/* <CityBarChart content={top5} /> */}
      </div>
    </div>
  );
};

export default MarketingAnalyticsFivthSection;
