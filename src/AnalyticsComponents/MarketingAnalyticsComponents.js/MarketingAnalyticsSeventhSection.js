import React from "react";
import AnalyticsTable from "../Utils/AnalyticsTable";
import { useSelector } from "react-redux";

import ReactECharts from "echarts-for-react";

const Brand = ({ content }) => {
  let id = content?.brand;
  let leads = content?.newLeads;
  let closures = content?.quoteSent;

  let end = 50;

  // if (id?.length > 20) {
  //   end = 5;
  // } else if (id?.length < 5) {
  //   end = 100;
  // }

  let option = {
    legend: {
      top: "90%",
      left: "10%",
      data: ["Leads", "Closures"],
    },

    tooltip: {
      trigger: "item",
    },

    xAxis: [
      {
        type: "category",
        axisTick: { show: false },
        axisLabel: {
          interval: 1,
          rotate: 0,
        },
        data: [...id],
      },
    ],
    yAxis: [
      {
        type: "value",
      },
    ],
    series: [
      {
        name: "Leads",
        type: "bar",
        barGap: 0,
        emphasis: {
          focus: "series",
        },
        data: leads,
      },
      {
        name: "Closures",
        type: "bar",
        emphasis: {
          focus: "series",
        },
        data: closures,
      },
    ],

    color: ["#CDC1FF", "#FFF6E3"],

    dataZoom: [
      {
        type: "inside",
        id: "insideX",
        xAxisIndex: 0,
        start: 0,
        end: end,
        zoomOnMouseWheel: false,
        moveOnMouseMove: true,
        moveOnMouseWheel: true,
      },
    ],
  };

  return (
    <div className=" w-full h-full ">
      <div className=" font-inter text-[20px] font-[700] ps-[10%]">
        Brand Vs Value
      </div>
      <ReactECharts option={option} />
    </div>
  );
};

const RESIDENT = ({ content = {} }) => {
  // BUNGALOW: {
  //   newLead: 0,
  //   quoteSent: 0,
  // },

  // PROJECT: {
  //   newLead: 0,
  //   quoteSent: 0,
  // },

  // COMMERCIAL: {
  //   newLead: 0,
  //   quoteSent: 0,
  // },

  // INDIVIDUAL: {
  //   newLead: 0,
  //   quoteSent: 0,
  // },

  // APARTMENT: {
  //   newLead: 0,
  //   quoteSent: 0,
  // },
  let option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      top: "90%",
      left: "10%",
      data: ["New Lead", "Quote Sent"],
    },

    xAxis: [
      {
        type: "category",
        axisTick: { show: true },
        axisLabel: {
          interval: 0,
          rotate: 0,
        },
        data: [
          "Resident",
          "Hospitals",
          "Industries",
          "Commercial",

          "Apartment",
        ],
      },
    ],
    yAxis: [
      {
        type: "value",
      },
    ],
    series: [
      {
        name: "New Lead",
        type: "bar",
        barGap: 0,
        emphasis: {
          focus: "series",
        },
        data: [
          content?.RESIDENT?.newLead,
          content?.HOSPITALS?.newLead,
          content?.INDUSTRIES?.newLead,
          // content?.INDIVIDUALRESIDENT?.newLead,
          // content?.BUNGALOW?.newLead,
          // content?.PROJECT?.newLead,
          content?.COMMERCIAL?.newLead,
          // content?.INDIVIDUAL?.newLead,
          content?.APARTMENT?.newLead,
        ],
      },
      {
        name: "Quote Sent",
        type: "bar",
        barGap: 0,
        emphasis: {
          focus: "series",
        },
        data: [
          content?.RESIDENT?.quoteSent,
          content?.HOSPITALS?.quoteSent,
          content?.INDUSTRIES?.quoteSent,

          content?.COMMERCIAL?.quoteSent,

          content?.APARTMENT?.quoteSent,
        ],
      },
    ],

    color: ["#CBDCEB", "#608BC1", "#FB8B24"],

    dataZoom: [
      {
        type: "inside",
        id: "insideX",
        xAxisIndex: 0,
        start: 0,
        end: 65,
        zoomOnMouseWheel: false,
        moveOnMouseMove: true,
        moveOnMouseWheel: true,
      },
    ],
  };
  return (
    <div className=" w-full h-full ">
      <div className=" font-inter text-[20px] font-[700] ps-[10%]">
        Lead Source
      </div>
      <ReactECharts option={option} />
    </div>
  );
};

const MarketingAnalyticsSeventhSection = () => {
  const {
    topSalesPerson,
    topSellingProducts,
    ShowLeadsquoteengineers,
    ShowLeadsquoteARCH_NAME,
    ShowLeadsquoteBrand,
    Leadsquoteresidentindividual,
  } = useSelector((state) => state.marketing?.analytics);

  return (
    <>
      <div className=" w-full h-fit flex flex-col md:flex-row justify-evenly ">
        <div className=" w-full md:w-[50%] h-fit flex flex-row justify-center ">
          <Brand content={ShowLeadsquoteBrand} />
        </div>
        <div className=" w-full md:w-[50%] h-fit flex flex-row justify-center ">
          <AnalyticsTable
            title="Architect Vs NewLead/QuoteSent"
            firstRow={{
              title: "Architect",
              array: ShowLeadsquoteARCH_NAME.architect,
            }}
            secondRow={{
              title: "New Lead",
              array: ShowLeadsquoteARCH_NAME.newLeads,
            }}
            thirdRow={{
              title: "Quote Sent",
              array: ShowLeadsquoteARCH_NAME?.quoteSent,
            }}
          />
        </div>
      </div>
      <div className=" w-full h-fit flex flex-col md:flex-row justify-evenly ">
        <div className=" w-full md:w-[50%] h-fit flex flex-row justify-center ">
          <AnalyticsTable
            title="Engineer Vs Newlead/QuoteSent"
            firstRow={{
              title: "Engineer",
              array: ShowLeadsquoteengineers.engineer,
            }}
            secondRow={{
              title: "New Lead",
              array: ShowLeadsquoteengineers.newLeads,
            }}
            thirdRow={{
              title: "Quote Sent",
              array: ShowLeadsquoteengineers?.quoteSent,
            }}
          />
        </div>
        <div className=" w-full md:w-[50%] h-fit flex flex-row justify-center ">
          <RESIDENT content={Leadsquoteresidentindividual} />
        </div>
      </div>

      {/* ------------------------------------------------ */}
      <div className=" w-full h-fit flex flex-col md:flex-row justify-evenly ">
        <div className=" w-full md:w-[50%] h-fit flex flex-row justify-center ">
          <AnalyticsTable
            title="Top Sales Persons"
            firstRow={{
              title: "Sales Person",
              array: topSalesPerson.name,
            }}
            secondRow={{
              title: "Quantity Sold",
              array: topSalesPerson.totalQuantitySold,
            }}
            thirdRow={{
              title: "Total Order Value",
              array: topSalesPerson.totalOrderValue,
            }}
            // fourthRow={{
            //      title: "Incentive",
            //      array: topSalesPerson.incentive,
            // }}
          />
        </div>
        <div className=" w-full md:w-[50%] h-fit flex flex-row justify-center ">
          <AnalyticsTable
            title="Products Vs Quantity"
            firstRow={{
              title: "Products",
              array: topSellingProducts.name,
            }}
            secondRow={{
              title: "Quantity",
              array: topSellingProducts.totalQuantity,
            }}
          />
        </div>
      </div>
    </>
  );
};

export default MarketingAnalyticsSeventhSection;
