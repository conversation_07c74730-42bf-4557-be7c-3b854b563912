import React from "react";
import ReactECharts from "echarts-for-react";
import { useSelector } from "react-redux";

const AOVCHART = ({ content }) => {
  let end = 50;
  if (content.date?.length > 20) {
    end = 5;
  } else if (content.date?.length < 5) {
    end = 100;
  }

  let option = {
    legend: {
      top: "90%",
      left: "10%",
      data: ["AVERAGE ORDER VALUE"],
    },

    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },

    xAxis: [
      {
        type: "category",
        axisTick: { show: false },
        axisLabel: {
          interval: 0,
          rotate: 0,
        },
        data: content.date,
      },
    ],
    yAxis: [
      {
        type: "value",
      },
    ],
    series: [
      {
        name: "AVERAGE ORDER VALUE",
        type: "bar",
        barGap: 0,
        emphasis: {
          focus: "series",
        },
        data: content.data,
      },
    ],

    color: ["#1446A0", "#D90368", "#F5D547"],

    dataZoom: [
      {
        type: "inside",
        id: "insideX",
        xAxisIndex: 0,
        start: 0,
        end: end,
        zoomOnMouseWheel: false,
        moveOnMouseMove: true,
        moveOnMouseWheel: true,
      },
    ],
  };

  return (
    <div className=" w-full h-full ">
      <div className=" font-inter text-[20px] font-[700] ps-[10%]">
        Average Order Value
      </div>
      <ReactECharts option={option} />
    </div>
  );
};

const SalesTrendBar = ({ content }) => {
  let option = {
    xAxis: {
      type: "category",
      data: content.sales,
    },
    yAxis: {
      type: "value",
    },
    tooltip: {
      trigger: "item",
      formatter: "{b} : {c}",
    },
    title: {
      text: "Marketing Trend",
      left: "10%",
    },
    series: [
      {
        data: content.count,
        type: "line",
        smooth: true,
      },
    ],

    color: ["#4416C6"],
  };
  return (
    <div className=" w-full h-full ">
      <ReactECharts option={option} />
    </div>
  );
};

const CityBarChart = ({ content }) => {
  const seriesLabel = {
    show: true,
  };
  let option = {
    title: {
      text: "Top 5 sales Cities ",
      left: "10%",
    },

    grid: {
      left: 100,
    },
    toolbox: {
      show: true,
      feature: {
        saveAsImage: {},
      },
    },
    xAxis: {
      type: "value",
      name: "Days",
      axisLabel: {
        formatter: "{value}",
      },
    },
    yAxis: {
      type: "category",
      inverse: true,
      data: content?.city,
    },
    series: [
      {
        name: "Sales",
        type: "bar",
        data: content?.count,
        label: seriesLabel,
      },
    ],
  };
  return (
    <div className=" w-full h-full ">
      <ReactECharts option={option} />
    </div>
  );
};

const MarketingAnalyticsFourthSection = () => {
  const { top5, salesTrend } = useSelector(
    (state) => state.marketing?.analytics
  );
  return (
    <div className=" w-full h-[800px] md:h-[400px] flex flex-col md:flex-row justify-center">
      <div className=" w-full md:w-[50%] h-full ">
        <CityBarChart content={top5} />
      
      </div>
      <div className=" w-full md:w-[50%] h-full ">
     
        <SalesTrendBar content={salesTrend} />
      </div>
    </div>
  );
};

export default MarketingAnalyticsFourthSection;
