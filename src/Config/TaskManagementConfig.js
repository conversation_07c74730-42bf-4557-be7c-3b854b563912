// Configs for task management dashboard

// Tables

export const taskManagementTableConfig = [
     {
          title: "Task Description",
          className: "w-[300px] h-full me-[1rem]",
     },
     {
          title: "Department",
          className: "w-[200px] h-full",
     },
     {
          title: "Project",
          className: "w-[180px] h-full",
     },
     {
          title: "Assignee",
          className: "w-[250px] h-full",
     },
     {
          title: "Assigned By",
          className: "w-[250px] h-full",
     },
     {
          title: "Due Date",
          className: "w-[150px] h-full",
     },
     {
          title: "Status",
          className: "w-[180px] h-full",
     },
     {
          title: "Completed Date",
          className: "w-[150px] h-full",
     },
];

// Status
// Open task
export const taskmanagement_opentask_status = {
     id: "Open",
     content: "Open",
};
// Over due
export const taskmanagement_overdue_status = {
     id: "Overdue",
     content: "Over Due",
};
// Completed
export const taskmanagement_completed_status = {
     id: "Completed",
     content: "Completed",
};
// Status List
export const status_dropdown_taskmanagement = [
     taskmanagement_opentask_status,
     taskmanagement_overdue_status,
     taskmanagement_completed_status,
];

// Create TaskManagement

export const department_dropdown_taskmanagement = [
     { id: "purchase", content: "Purchase" },
     { id: "sales", content: "Sales" },
     { id: "order", content: "Order" },
];

export const project_dropdown_taskmanagement = [
     { id: "Dceramic", content: "Dceramic" },
     { id: "DNSP", content: "DNSP" },
     { id: "Dharan Bath Fitting", content: "Dharan Bath Fitting" },
     { id: "Dharan Bath Care", content: "Dharan Bath Care" },
     { id: "Dharan Rock", content: "Dharan Rock" },
];

//Date Format

export function formatDate_taskManagement(dataObj) {
     const year = dataObj.getFullYear();
     const month = String(dataObj.getMonth() + 1).padStart(2, "0"); // Months are zero-based
     const day = String(dataObj.getDate()).padStart(2, "0");

     // Format the date as YYYY-MM-DD
     return `${year}-${month}-${day}`;
}
