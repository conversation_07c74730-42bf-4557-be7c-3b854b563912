export const salesAndMarketingTableConfig = [
  {
    title: "Owner Name",
    className: " w-[180px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "display",
    key: "OWNER_NAME",
  },
  {
    title: "Contact Number 1",
    className: "w-[180px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "display",
    key: "CONTACT_NO_1",
  },
  // {
  //   title: "Contact Number 2",
  //   className: "w-[180px] h-full ms-[1rem]",
  //   listtype: "display",
  //   headertype: "display",
  //   key: "CONTACT_NO_2",
  // },
  // {
  //   title: "Contact Number 3",
  //   className: "w-[180px] h-full ms-[1rem]",
  //   listtype: "display",
  //   headertype: "display",
  //   key: "CONTACT_NO_3",
  // },
  // {
  //   title: "Contact Number 4",
  //   className: "w-[180px] h-full ms-[1rem]",
  //   listtype: "display",
  //   headertype: "display",
  //   key: "CONTACT_NO_4",
  // },
  {
    title: "Date of Visit",
    className: "w-[180px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "display",
    key: "DATE_OF_VISIT",
  },
  {
    title: "Visited By",
    className: "w-[180px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "filterwithsearch",
    key: "VISITED_BY",
    options: "userList",
  },
  {
    title: "Lead Status",
    className: "w-[180px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "filter",
    key: "LEAD_STATUS",
    options: [
      { id: "NEW LEAD", content: "NEW LEAD" },
      { id: "QUOTE SENT", content: "QUOTE SENT" },
      { id: "IN FOLLOW", content: "IN FOLLOW" },
      { id: "DROPPED", content: "DROPPED" },
      {
        id: "WALK IN",
        content: "WALK IN",
      },
      { id: "FIELD VISIT", content: "FIELD VISIT" },
    ],
  },
  {
    title: "Lead to company",
    className: "w-[180px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "filter",
    key: "LEAD_TO_COMPANY",
    options: [
      { id: "DNSP", content: "DNSP" },
      { id: "DBF", content: "DBF" },
      { id: "DC", content: "DC" },
      { id: "DUS", content: "DUS" },
    ],
  },
  {
    title: "Lead Sales Person",
    className: "w-[180px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "filterwithsearch",
    key: "LEAD_SALES_PERSON",
    options: "userList",
  },
  {
    title: "Lead Type",
    className: "w-[180px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "filter",
    key: "LEAD_TYPE",
    options: [
      { id: "INDIVIDUAL RESIDENT", content: "INDIVIDUAL RESIDENT" },
      { id: "RESIDENT", content: "RESIDENT" },
    ],
  },

  {
    title: "City",
    className: "w-[180px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "filter",
    key: "CITY",
    options: [
      { id: "Chennai", content: "Chennai" },
      { id: "Coimbatore", content: "Coimbatore" },
      { id: "Madurai", content: "Madurai" },
      { id: "Tiruchirappalli", content: "Tiruchirappalli" },
      { id: "Salem", content: "Salem" },
      { id: "Erode", content: "Erode" },
      { id: "Tirunelveli", content: "Tirunelveli" },
      { id: "Vellore", content: "Vellore" },
      { id: "Thoothukudi", content: "Thoothukudi" },
      { id: "Dindigul", content: "Dindigul" },
      { id: "Thanjavur", content: "Thanjavur" },
      { id: "Cuddalore", content: "Cuddalore" },
      { id: "Kanchipuram", content: "Kanchipuram" },
      { id: "Karur", content: "Karur" },
      { id: "Nagapattinam", content: "Nagapattinam" },
      { id: "Nagercoil", content: "Nagercoil" },
      { id: "Pudukkottai", content: "Pudukkottai" },
      { id: "Sivakasi", content: "Sivakasi" },
      { id: "Tiruppur", content: "Tiruppur" },
      { id: "Pollachi", content: "Pollachi" },
      { id: "Rajapalayam", content: "Rajapalayam" },
      { id: "Dharmapuri", content: "Dharmapuri" },
      { id: "Ariyalur", content: "Ariyalur" },
      { id: "Perambalur", content: "Perambalur" },
      { id: "Ramanathapuram", content: "Ramanathapuram" },
      { id: "Tiruvannamalai", content: "Tiruvannamalai" },
      { id: "Villupuram", content: "Villupuram" },
      { id: "Virudhunagar", content: "Virudhunagar" },
      { id: "Chengalpattu", content: "Chengalpattu" },
      { id: "Karaikudi", content: "Karaikudi" },
      { id: "Sivagangai", content: "Sivagangai" },
      { id: "Namakkal", content: "Namakkal" },
      { id: "Udhagamandalam (Ooty)", content: "Udhagamandalam (Ooty)" },
      { id: "Kodaikanal", content: "Kodaikanal" },
      { id: "Arakkonam", content: "Arakkonam" },
      { id: "Mettur", content: "Mettur" },
      { id: "Gudiyatham", content: "Gudiyatham" },
      { id: "Palani", content: "Palani" },
      { id: "Ambur", content: "Ambur" },
      { id: "Cheyyar", content: "Cheyyar" },
      { id: "Mayiladuthurai", content: "Mayiladuthurai" },
      { id: "Tenkasi", content: "Tenkasi" },
      { id: "Thiruvarur", content: "Thiruvarur" },
    ],
  },
  // {
  //   title: "Area",
  //   className: "w-[180px] h-full ms-[1rem]",
  //   listtype: "display",
  //   headertype: "display",
  //   key: "AREA",
  // },
  // {
  //   title: "Landmark",
  //   className: "w-[180px] h-full ms-[1rem]",
  //   listtype: "display",
  //   headertype: "display",
  //   key: "LANDMARK",
  // },

  // {
  //   title: "Engineer Name",
  //   className: "w-[200px] h-full ms-[1rem]",
  //   listtype: "display",
  //   headertype: "display",
  //   key: "engineer_name",
  // },

  // {
  //   title: "Arch Firm Name",
  //   className: "w-[180px] h-full ms-[1rem]",
  //   listtype: "display",
  //   headertype: "display",
  //   key: "ARCH_FIRM_NAME",
  // },

  // {
  //   title: "Architect Name",
  //   className: "w-[200px] h-full ms-[1rem]",
  //   listtype: "display",
  //   headertype: "display",
  //   key: "ARCH_NAME",
  // },

  // {
  //   title: "Plumber Name",
  //   className: "w-[180px] h-full ms-[1rem]",
  //   listtype: "display",
  //   headertype: "display",
  //   key: "PLUMBER_NAME",
  // },

  // {
  //   title: "Key Person Name ",
  //   className: "w-[220px] h-full ms-[1rem]",
  //   listtype: "display",
  //   headertype: "display",
  //   key: "KEY_PERSON_NAME",
  // },
  // {
  //   title: "Key Person Number ",
  //   className: "w-[220px] h-full ms-[1rem]",
  //   listtype: "display",
  //   headertype: "display",
  //   key: "KEY_PERSON_NUMBER",
  // },
  {
    title: "Brand To Promote",
    className: "w-[180px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "display",
    key: "BRAND_TO_PROMOTE",
  },
  // {
  //   title: "No of Bathrooms",
  //   className: "w-[180px] h-full ms-[1rem]",
  //   headertype: "display",
  //   listtype: "display",
  //   key: "No_of_Bathrooms",
  // },
  // {
  //   title: "Tiles_Requirement",
  //   className: "w-[180px] h-full ms-[1rem]",
  //   headertype: "display",
  //   listtype: "display",
  //   key: "Tiles_Requirement",
  // },
  {
    title: "Lead Person Remarks",
    className: "w-[180px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "display",
    key: "LEAD_PERSON_REMARKS",
  },
  {
    title: "Follow Up Date 1",
    className: "w-[180px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "display",
    key: "FOLLOW_UP_DATE_1",
  },
  {
    title: "Unit",
    className: "w-[120px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "display",
    key: "unit",
  },
  // {
  //   title: "Follow Up Date 2",
  //   className: "w-[180px] h-full ms-[1rem]",
  //   listtype: "display",
  //   headertype: "display",
  //   key: "FOLLOW_UP_DATE_2",
  // },

  // {
  //   title: " Telecalling Remarks",
  //   className: "w-[180px] h-full ms-[1rem]",
  //   listtype: "display",
  //   headertype: "display",
  //   key: "TELECALLING_REMARKS",
  // },
  // {
  //   title: "Revisit Date 1",
  //   className: "w-[180px] h-full ms-[1rem]",
  //   listtype: "display",
  //   headertype: "display",
  //   key: "RE_VISIT_DATE_1",
  // },
  // {
  //   title: "Revisit Date 2",
  //   className: "w-[180px] h-full ms-[1rem]",
  //   listtype: "display",
  //   headertype: "display",
  //   key: "RE_VISIT_DATE_2",
  // },
  // {
  //   title: "Revisit Date 3",
  //   className: "w-[180px] h-full ms-[1rem]",
  //   listtype: "display",
  //   headertype: "display",
  //   key: "RE_VISIT_DATE_3",
  // },
  // {
  //   title: "Revisit Date 4",
  //   className: "w-[180px] h-full ms-[1rem]",
  //   listtype: "display",
  //   headertype: "display",
  //   key: "RE_VISIT_DATE_4",
  // },
];

// FORM BUILDER CODE
export const salesandmarketing_addForm = [
  {
    type: "text",
    key: "OWNER_NAME",
    placeholder: "Enter the Owner Name",
    defaultValue: "",
  },
  {
    type: "number",
    key: "CONTACT_NO_1",
    placeholder: "Contatct Number 1",
    defaultValue: "",
  },
  {
    type: "number",
    key: "CONTACT_NO_2",
    placeholder: "Contatct Number 2",
    defaultValue: "",
  },
  {
    type: "date",
    key: "DATE_OF_VISIT",
    title: "Date of Visit",
    defaultValue: new Date(),
    format: "date",
  },

  {
    type: "text",
    key: "VISITED_BY",
    placeholder: "Visited By",
    defaultValue: "",
  },

  {
    type: "singleselect",
    key: "LEAD_STATUS",
    options: ["NEW LEAD", "QUOTE SENT", "IN FOLLOW", "DROPPED"],
    defaultValue: "Lead Status",
    placeholder: "Lead Status",
  },

  {
    type: "text",
    key: "LEAD_TO_COMPANY",
    placeholder: "Lead to company",
    defaultValue: "",
  },
  {
    type: "singleselect",
    key: "LEAD_SALES_PERSON",
    options: "userList",
    defaultValue: "Lead Sales Person",
    placeholder: "Lead Sales Person",
  },
  {
    type: "singleselect",
    key: "LEAD_TYPE",
    options: ["INDIVIDUAL RESIDENT", "RESIDENT"],
    defaultValue: "Lead Type",
    placeholder: "Lead Type",
  },
  {
    type: "text",
    key: "CITY",
    placeholder: "City",
    defaultValue: "",
  },
  {
    type: "text",
    key: "AREA",
    placeholder: "Area",
    defaultValue: "",
  },
  {
    type: "text",
    key: "LANDMARK",
    placeholder: "Landmark",
    defaultValue: "",
  },

  {
    type: "text",
    key: "engineer_name",
    placeholder: "Engineer Name",
    defaultValue: "",
  },
  {
    type: "number",
    key: "engineer_number",
    placeholder: "Engineer Number",
    defaultValue: "",
  },
  {
    type: "text",
    key: "ARCH_FIRM_NAME",
    placeholder: "Architech Firm Name",
    defaultValue: "",
  },

  {
    type: "text",
    key: "ARCH_NAME",
    placeholder: "Architect Name",
    defaultValue: "",
  },
  {
    type: "number",
    key: "ARCH_NUMBER",
    placeholder: "Architect Number",
    defaultValue: "",
  },
  {
    type: "text",
    key: "PLUMBER_NAME",
    placeholder: "Plumber Name",
    defaultValue: "",
  },
  {
    type: "number",
    key: "PLUMBER_NUMBER",
    placeholder: "Plumber Number",
    defaultValue: "",
  },
  {
    type: "text",
    key: "KEY_PERSON_NAME",
    placeholder: "Key Person Name",
    defaultValue: "",
  },
  {
    type: "number",
    key: "KEY_PERSON_NUMBER",
    placeholder: "key Person Number",
    defaultValue: "",
  },

  {
    type: "singleselect",
    key: "BRAND_TO_PROMOTE",
    options: [
      "HANSGROHE",
      "GROHE",
      "jAGUAR",
      "TOTO",
      "DURAVIT",
      "KOHLER",
      "GEBERIT",
      "KAJARIA",
      "SOMANY",
      "NEXION",
      "QUTONE",
      "RAK",
    ],
    defaultValue: "Brand to promote",
    placeholder: "Brand to promote",
  },
  {
    type: "text",
    key: "LEAD_PERSON_REMARKS",
    placeholder: "Lead Person Remarks",
    defaultValue: "",
  },
  {
    type: "date",
    key: "FOLLOW_UP_DATE_1",
    title: "Follow Up Date 1",
    defaultValue: new Date(),
    format: "format",
  },
  {
    type: "date",
    key: "FOLLOW_UP_DATE_2",
    title: "Follow Up Date 2",
    defaultValue: new Date(),
    format: "format",
  },
  {
    type: "text",
    key: "TELECALLING_REMARKS",
    placeholder: "Telecalling Remarks",
    defaultValue: "",
  },
  {
    type: "multiDate",
    key: "RE_VISIT_DATE",
    title: "Revisit Date",
    defaultValue: [],
    format: "format",
  },
  {
    type: "textarea",
    key: "RE_VISIT_REMARKS",
    placeholder: "Revisit Remarks",
    defaultValue: "",
  },
  {
    type: "text",
    key: "location",
    placeholder: "Location (e.g., Kovai)",
    defaultValue: "",
  },
];

//-------------------------

export const siteStatus_Dropdown = [
  {
    id: "Electricals",
    content: "Electricals",
  },
  {
    id: "Carpentry",
    content: "Carpentry",
  },
  {
    id: "Interior",
    content: "Interior",
  },
  {
    id: "Finishing",
    content: "Finishing",
  },
  {
    id: "Painting",
    content: "Painting",
  },
  {
    id: "Post Construction",
    content: "Post Construction",
  },
  {
    id: "Plumbing",
    content: "Plumbing",
  },
  {
    id: "new",
    content: "new",
  },
];

export const purpose_SalesAndMarketing_Dropdown = [
  {
    id: "Site visit",
    content: "Site visit",
  },
  {
    id: "Customer visit",
    content: "Customer visit",
  },
];

export const walkInStatus_dropDown = [
  { id: "dceramic", content: "DCeramic" },
  { id: "dnsp", content: "DNSP" },
  { id: "dbf", content: "DBP" },
  { id: "notvisited", content: "Not Visited" },
];

export const LeadStatus_Qualified = { id: "Qualified", content: "Qualified" };

export const Lead_Status_Dropdown = [
  { id: "New", content: "New" },
  { id: "Quotation Sent", content: "Quotation Sent" },
  { id: "In Follow-up", content: "In Follow-up" },
  { id: "Dropped", content: "Dropped" },
  LeadStatus_Qualified,
];

export const selectColorCode_Sales = (content) => {
  if (content.id === "New") {
    return " bg-[#4C49ED] text-[#4C49ED] bg-opacity-20 ";
  }

  if (content.id === LeadStatus_Qualified.id) {
    return " bg-[#63A676] text-[#63A676] bg-opacity-20 ";
  }

  if (content.id === "Quotation Sent") {
    return " bg-[#019BA2] text-[#019BA2] bg-opacity-20 ";
  }

  if (content.id === "In Follow-up") {
    return " bg-[#5961F9] text-[#5961F9] bg-opacity-20 ";
  }

  if (content.id === "Dropped") {
    return " bg-[#AB1917] text-[#AB1917] bg-opacity-20 ";
  }
  return "bg-[#019BA229]";
};

export const LeadConcern_dropDown = [
  { id: "DCeramic", content: "DCeramic" },
  { id: "DNSP", content: "DNSP" },
  { id: "DBP", content: "DBP" },
];

// lead_type:{
//      type:'string',
//      enum:['A','B','C'],
//    },
//    lead_source:{
//      type:'string',
//      enum:['', 'walkin']

export const leadType_SalesMarketing_enums = [
  {
    id: "A",
    content: "A",
  },
  {
    id: "B",
    content: "B",
  },
  {
    id: "C",
    content: "C",
  },
];

export const leadSource_salesMarketing_enums = [
  { id: "field visit", content: "field visit" },
  { id: "walkin", content: "walkin" },
];
