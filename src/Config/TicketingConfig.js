export const TicketingTableConfig = [
  {
    title: "Tickets ID",
    className: "w-[170px]  h-full ms-[1rem]",
    headertype: "display",
    key: "ticket_id",
  },
  {
    title: "Tickets Name",
    className: "w-[170px]  h-full ms-[1rem]",
    headertype: "display",
    key: "name",
  },
  {
    title: "Date",
    className: " w-[100px] h-full ms-[1rem]",
    headertype: "display",
    key: "closedAt",
  },
  {
    title: "Raised By",
    className: " w-[180px] h-full ms-[1rem]",
    headertype: "filter",
    key: "raisedBy",
    options: "userList",
  },
  {
    title: "Assigned To",
    className: " w-[200px] h-full ms-[3rem]",
    headertype: "filter",
    options: "userList",
    key: "raisedTo",
  },
  {
    title: "Department",
    className: " w-[180px] h-full ms-[2rem]",
    headertype: "filter",
    options: [
      {
        id: "Purchase",
        content: "Purchase",
      },
      {
        id: "Sales",
        content: "Sales",
      },
      {
        id: "Marketing",
        content: "Marketing",
      },
      {
        id: "SOP",
        content: "SOP",
      },
      {
        id: "Others",
        content: "Others",
      },
    ],
    key: "department",
  },
  {
    title: "Ticket Description",
    className: " w-[250px] h-full ps-[1rem] pe-[1rem]",
    headertype: "display",
    key: "description",
  },
  {
    title: "Status",
    className: " w-[200px] h-full ms-[0rem]",
    headertype: "filter",
    key: "status",
    options: [
      {
        id: "New",
        content: "New",
      },
      {
        id: "In Progress",
        content: "In Progress",
      },
      {
        id: "Closed",
        content: "Closed",
      },
    ],
  },
  {
    title: "Expected Resolution Time",
    className: "w-[220px] h-full ms-[1rem]",
    headertype: "display",
    key: "timeline",
  },
  {
    title: "Remarks",
    className: "w-[220px] h-full ms-[1rem]",
    headertype: "display",
    key: "remarks",
  },
  {
    title: "Time Taken",
    className: "w-[220px] h-full ms-[1rem]",
    headertype: "display",
    key: "time_taken",
  },
];

export const TicketType_Closed = {
  id: "Closed",
  content: "Closed",
};

export const TicketType_InProgress = {
  id: "In Progress",
  content: "In Progress",
};

export const TicketType_TicketsDropdown = [
  {
    id: "New",
    content: "New",
  },
  TicketType_InProgress,
  TicketType_Closed,
];

export const selectColorCode_Ticket = (content) => {
  if (content.id === "New") {
    return " bg-[#4C49ED] text-[#4C49ED] bg-opacity-20 ";
  }

  if (content.id === TicketType_InProgress.id) {
    return " bg-[#EB7F00] bg-opacity-20 text-[#F2994A] ";
  }

  if (content.id === TicketType_Closed.id) {
    return " bg-[#32936F] bg-opacity-25 text-[#05AF9B]";
  }

  return "bg-[#019BA229]";
};

export const Department_TicketsDropdown = [
  {
    id: "Purchase",
    content: "Purchase",
  },
  {
    id: "Sales",
    content: "Sales",
  },
  {
    id: "Marketing",
    content: "Marketing",
  },
  {
    id: "SOP",
    content: "SOP",
  },
  {
    id: "Others",
    content: "Others",
  },
];

export const returnUserNameWIthID_tickets = (id, userList) => {
  for (const item of userList) {
    if (id === item.id) {
      return item.content;
    }
  }

  return id;
};

export const ticketAddForm_Config = [
  {
    type: "text",
    key: "name",
    defaultValue: "",
    placeholder: "Ticket Name",
    required: true,
  },
  {
    type: "multipleselect",
    key: "assignee",
    options: "userList",
    title: "Assignee",
    defaultValue: [],
    required: true,
  },
  {
    type: "singleselect",
    key: "department",
    options: ["Purchase", "Sales", "Marketing", "SOP", "Others"],
    defaultValue: "Department",
    placeholder: "Department",
    required: true,
  },
  {
    type: "text",
    key: "description",
    defaultValue: "",
    placeholder: "Description",
    required: true,
  },
  {
    type: "singleselect",
    key: "status",
    options: ["New", "In Progress", "Closed"],
    defaultValue: "Department",
    placeholder: "Department",
    required: true,
  },
  {
    type: "text",
    key: "remarks",
    defaultValue: "",
    placeholder: "Remarks",
    required: true,
  },
  {
    type: "date",
    key: "timeline",
    title: "Expected Resolution Time",
    format: "date",
    defaultValue: new Date(),
    required: true,
  },
];
