export const orderTableConfig = [
  {
    title: "Order ID",
    key: "order_id",
    className: " w-[120px] h-full ms-[1rem]",
    headertype: "display",
  },
  {
    title: "Name",
    key: "Name",
    className: "w-[120px] h-full ms-[1rem]",
    headertype: "display",
  },
  {
    title: "Customer Id",
    key: "customer_id",
    className: "w-[150px] h-full ms-[1rem]",
    headertype: "display",
  },
  {
    title: "Order Date",
    key: "order_date",
    className: "w-[150px] h-full ms-[1rem]",
    headertype: "display",
  },
  {
    title: "Product Details",
    key: "product_details",
    className: "w-[200px] h-full ms-[1rem]",
    headertype: "display",
  },
  {
    title: "Batch no",
    key: "batch_no",
    className: " w-[120px] h-full ms-[1rem] ",
    headertype: "display",
  },
  {
    title: "Quantity",
    key: "quantity",
    className: "w-[120px] h-full ms-[1rem]",
    headertype: "display",
  },
  {
    title: "Stock Availablity",
    key: "stock_availability",
    className: "w-[180px] h-full ms-[1rem]",
    headertype: "filter",
    options: [
      //  { id: "Partially Available", content: "Partially Available" },
      { id: "Available", content: "Available" },
      { id: "Out Of Stock", content: "Out Of Stock" },
    ],
  },
  {
    title: "Unavailable Quantity",
    key: "unavailable_quantity",
    className: "w-[200px] h-full ms-[1rem]",
    headertype: "display",
  },
  // {
  //      title: "Damaged Products",
  //      className: "w-[200px] h-full ms-[1rem]",
  // },
  {
    title: "ETA of Unavailable Stock",
    key: "eta_unavailable_stock",
    className: "w-[220px] h-full ms-[1rem]",
    headertype: "display",
  },
  {
    title: "Order Total",
    key: "order_total",
    className: "w-[200px] h-full ms-[1rem]",
    headertype: "display",
  },
  {
    title: "Payment Received",
    key: "payment_received",
    className: "w-[220px] h-full ms-[1rem] ",
    headertype: "display",
  },
  {
    title: "Location",
    key: "location",
    className: "w-[200px] h-full ms-[1rem]",
    headertype: "display",
  },
  {
    title: "Order Status",
    key: "order_status",
    className: "w-[200px] h-full ms-[1rem]",
    headertype: "filter",
    options: [
      { id: "Order Confirm", content: "Order Confirm" },
      { id: "Ready for Delivery", content: "Ready for Delivery" },
      { id: "Out for Delivery", content: "Out for Delivery" },
      { id: "Deliverd", content: "Deliverd" },
    ],
  },

  {
    title: "Sales Person",
    key: "sales_person",
    className: "w-[200px] h-full ms-[1rem]",
    headertype: "filter",
    options: "userList",
  },
  {
    title: "Delivery Date",
    key: "delivery_date",
    className: "w-[200px] h-full ms-[1rem]",
    headertype: "display",
  },
  {
    title: "Brand",
    key: "Brand",
    className: "w-[180px] h-full ms-[1rem]",
    headertype: "display",
  },
];

export const stockAvailablityTypes = [
  {
    text: "Partially Available",
    color: "#FF6F24",
  },
  {
    text: "Available",
    color: "#05AF9B",
  },
  {
    text: "Out of Stock",
    color: "#AB1917",
  },
];

// Damaged products
export const damagedProduct_DamagedOption = {
  id: "Damaged product",
  content: "Damaged product",
};
export const damagedProduct_NotDamagedOption = {
  id: "Not Damaged",
  content: "Not Damaged",
};

export const damagedProduct_orderDashboard = [
  damagedProduct_DamagedOption,
  damagedProduct_NotDamagedOption,
];

//Stock Availablily
export const stockAvailablity_available = {
  id: "Available",
  content: "Available",
};

export const stockAvailablity_outOfStock = {
  id: "Out Of Stock",
  content: "Out Of Stock",
};

export const stockAvailablity_orderDashbaord = [
  stockAvailablity_available,
  stockAvailablity_outOfStock,
];

export const gettingStockAvailablity = (text) => {
  for (let i = 0; i < stockAvailablityTypes?.length; i++) {
    if (text === stockAvailablityTypes[i].text) {
      return stockAvailablityTypes[i];
    }
  }

  return {
    text,
    color: "black",
  };
};
