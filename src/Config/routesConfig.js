export const routes = {
  home: {
    directLink: "/",
    relativeLink: "/",
  },

  resetPassword: {
    directLink: "/reset-password",
    relativeLink: "reset-password",
    userIdSearchParam: "userId",
    tokenSeachParam: "token",
    signFormSearchParam: "signinform",
  },

  customerSignIn: {
    directLink: "/customer/signin",
    relativeLink: "customer/signin",
  },
  customerSignUp: {
    directLink: "/customer/signup",
    relativeLink: "/customer/signup",
  },

  customerResetPassword: {
    directLink: "/customer/resetpassword",
    relativeLink: "resetpassword",
  },

  signin: {
    directLink: "/signin",
    relativeLink: "/signin",
  },
  signup: {
    directLink: "/signup",
    relativeLink: "/signup",
  },

  dashboard: {
    directLink: "/dashboard",
    relativeLink: "dashboard",
  },

  userManagement: {
    directLink: "/dashboard/users",
    relativeLink: "users",
  },

  userManagementEdit: {
    directLink: "/dashboard/users/edit",
    relativeLink: "edit",
  },

  userManagementView: {
    directLink: "/dashboard/users/view",
    relativeLink: "view",
  },

  userManagementInviteUser: {
    directLink: "/dashboard/users/inviteuser",
    relativeLink: "inviteuser",
  },

  userManagementDelete: {
    directLink: "/dashboard/users/delete",
    relativeLink: "delete",
  },

  // Create user
  userManagementCreateUser: {
    directLink: "/dashboard/users/createuser",
    relativeLink: "createuser",
  },

  // Purchase
  purchase: {
    directLink: "/dashboard/purchase",
    relativeLink: "purchase",
  },

  purchaseAnalytics: {
    directLink: "/dashboard/purchase/analytics",
    relativeLink: "analytics",
  },

  purchaseAdd: {
    searchParams: "addapurchase",
  },

  purchaseEdit: {
    searchParams: "editapurchase",
  },

  orderManagement: {
    directLink: "/dashboard/orders",
    relativeLink: "orders",
  },

  orderManagementAnalytics: {
    directLink: "/dashboard/orders/analytics",
    relativeLink: "analytics",
  },

  editOrderManagement: {
    directLink: "/dashboard/orders/edit",
    relativeLink: "edit",
  },

  deleteOrderManagement: {
    directLink: "/dashboard/orders/delete",
    relativeLink: "delete",
  },

  addleadOrderManagement: {
    directLink: "/dashboard/orders/addalead",
    relativeLink: "addalead",
  },

  customer: {
    directLink: "/dashboard/customers",
    relativeLink: "customers",
  },

  solocustomer: {
    directLink: "/dashboard/customers/:customerid",
    relativeLink: "customers",
  },

  solocustomerTracking: {
    directLink: "/dashboard/customers/:customerid/tracking",
    relativeLink: "tracking",
  },

  // Service Complaints

  servicecomplaints: {
    directLink: "/dashboard/servicecomplaints",
    relativeLink: "servicecomplaints",
  },

  servicecomplaintsAddNew: {
    directLink: "/dashboard/servicecomplaints/addacomplaint",
    relativeLink: "addacomplaint",
    searchParams: "addacomplaint",
  },

  servicecomplaintsEdit: {
    directLink: "/dashboard/servicecomplaints/editacomplaint",
    relativeLink: "editacomplaint",
    searchParams: "editthecomplaint",
  },

  salesandmarketing: {
    directLink: "/dashboard/sales",
    relativeLink: "sales&marketing",
  },

  salesAndMarketingAddLead: {
    searchParams: "addalead",
  },

  salesAndMarketingEditLead: {
    searchParams: "editalead",
  },

  salesAndMarketingAnalytics: {
    directLink: "/dashboard/sales/analytics",
    relativeLink: "analytics",
  },

  // marketing

  marketing: {
    directLink: "/dashboard/marketing",
    relativeLink: "marketing",
  },
  marketingAdd: {
    searchParams: "add",
  },
  marketingAnalytics: {
    directLink: "/dashboard/marketing/analytics",
    relationLink: "analytics",
  },
  marketingWalkin: {
    directLink: "/dashboard/marketing/walkin",
    relativeLink: "walkin",
  },

  marketingWalkinAdd: {
    searchParams: "add",
  },
  marketingWalkinEdit: {
    searchParams: "edit",
  },

  //--------------

  ticketing: {
    directLink: "/dashboard/tickets",
    relativeLink: "tickets",
  },
  ticketingadd: {
    directLink: "/dashboard/tickets/add",
    relativeLink: "add",
    searchParams: "addaticket",
  },
  ticketingedit: {
    directLink: "/dashboard/tickets/edit",
    relativeLink: "edit",
    searchParams: "edit",
  },

  // Networking
  networking: {
    directLink: "/dashboard/networking",
    relativeLink: "networking",
  },

  networkingadd: {
    directLink: "/dashboard/networking/add",
    relationLink: "add",
  },

  //Task - Management
  taskManagementMain: {
    directLink: "/dashboard/taskmanagement",
    relativeLink: "taskmanagement",
  },

  taskManagementAdd: {
    directLink: "/dashboard/taskmanagement/add",
    relativeLink: "add",
  },

  // Customer Dashboard
  customerDashboard: {
    directLink: "/customer-dashboard",
    relativeLink: "customer-dashboard",
  },

  customerDashboardOrders: {
    directLink: "/customer-dashboard/orders",
    relativeLink: "orders",
  },

  customerDashboardTracking: {
    directLink: "/customer-dashboard/orders/tracking",
    relativeLink: "tracking",
    searchParams: "billid",
  },

  // SAP
  sap: {
    directLink: "/dashboard/sap",
    relativeLink: "sap",
  },
  
  sapadd: {
    directLink: "/dashboard/sap/add",
    relativeLink: "add",
    searchParams: "addasap",
  },
  
  sapedit: {
    directLink: "/dashboard/sap/edit",
    relativeLink: "edit",
    searchParams: "editsap",
  },
};
