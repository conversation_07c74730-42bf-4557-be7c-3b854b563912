import dashboradImage from "../Assest/Utils/fourBox.png";
import swapImage from "../Assest/Utils/Swap.png";
import activeImage from "../Assest/Utils/active.png";
import BagImage from "../Assest/Utils/active.png";
import settingImage from "../Assest/Utils/Setting.png";
import bottonImage from "../Assest/Utils/bottom.png";
import userImage from "../Assest/Utils/UserLogo.png";
import { routes } from "./routesConfig";

export const adminOnlynavBarConfig = [
  {
    name: "Swap",
    image: swapImage,
    link: routes.userManagement.directLink,
  },
];

export const navBarObjConfig = {
  users: {
    name: "User Management",
    link: routes.userManagement.directLink,
  },
  purchase: {
    name: "Purchase",
    link: routes.purchase.directLink,
  },
  salesAndMarketing: {
    name: "Sales",
    link: routes.salesandmarketing.directLink,
  },
  marketing: {
    name: "Marketing",
    link: routes?.marketing?.directLink,
  },
  orderDetails: {
    name: "Order Management",
    link: routes.orderManagement.directLink,
  },
  customers: {
    name: "Customers",
    link: routes.customer.directLink,
  },
  networking: {
    name: "Networking",
    link: routes.networking.directLink,
  },
  serviceComplaints: {
    name: "Service Complaints",
    link: routes.servicecomplaints.directLink,
  },
  ticketing: {
    name: "Ticketing",
    link: routes.ticketing.directLink,
  },
  sap: {
    name: "SAP",
    link: routes.sap.directLink,
  },
  taskmanagement: {
    name: "Task Management",
    link: routes.taskManagementMain.directLink,
  },
};

export const customer_nav_bar_config_obj = {
  orders: {
    name: "Your Orders",
    link: routes.customerDashboardOrders?.directLink,
  },
};

export const navBarConfig = [
  {
    name: "Dashboard",
    image: dashboradImage,
    link: routes.orderManagement.directLink,
    title: "orderDetails",
    element: "view",
  },
  {
    name: "Setting",
    image: activeImage,
    link: routes.purchase.directLink,
    title: "products",
    element: "view",
  },
  {
    name: "Bag Imaage ",
    image: BagImage,
    link: routes.salesandmarketing.directLink,
    title: "leads",
    element: "view",
  },
  {
    name: "User name ",
    image: userImage,
    link: routes.customer.directLink,
    title: "customers",
    element: "view",
  },
  {
    name: "seeting",
    image: settingImage,
    link: routes.servicecomplaints.directLink,
    title: "reports",
    element: "view",
  },
];

export const ExitConfig = {
  image: bottonImage,
  name: "Exit image",
};
