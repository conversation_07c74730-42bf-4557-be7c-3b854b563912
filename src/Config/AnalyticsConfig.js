export const indiaCurrencyFormat = (x) => {
     let total = x.toLocaleString("en-IN", {
          style: "currency",
          symbol: "₹",
          currency: "INR",
     });

     return total;
};

function getNextDate(dateObj) {
     // Create a new Date object based on the original date
     let nextDate = new Date(dateObj);
     // Increment the date by 1
     nextDate.setDate(nextDate.getDate() + 1);
     return nextDate;
}

function formatDateToString(dateObj) {
     let year = dateObj.getFullYear();
     let month = String(dateObj.getMonth() + 1).padStart(2, "0"); // Months are zero-based
     let day = String(dateObj.getDate()).padStart(2, "0");

     return `${year}-${month}-${day}`;
}

function areDatesEqual(dateObj1, dateObj2) {
     dateObj1 = new Date(dateObj1);
     dateObj2 = new Date(dateObj2);
     return (
          dateObj1.getFullYear() === dateObj2.getFullYear() &&
          dateObj1.getMonth() === dateObj2.getMonth() &&
          dateObj1.getDate() === dateObj2.getDate()
     );
}

export const salesTrend_DataPhase = (salesTrendArray) => {
     let id = [];
     let count = [];

     if (salesTrendArray.length === 0) return { id, count };

     let startingDateObj = new Date(salesTrendArray[0]._id);
     for (let i = 0; i < salesTrendArray.length; i++) {
          let currentObj = salesTrendArray[i];
          const dateObj = new Date(currentObj._id);

          let isSameDate = areDatesEqual(dateObj, startingDateObj);

          if (isSameDate) {
               id.push(currentObj._id);
               count.push(currentObj.count);
          } else {
               i--;
               id.push(formatDateToString(startingDateObj));
               count.push(0);
          }

          startingDateObj = getNextDate(startingDateObj);
     }

     return { id, count };
};
