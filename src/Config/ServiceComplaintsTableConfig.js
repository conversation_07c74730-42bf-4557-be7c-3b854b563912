export const serviceComplaintsTableConfig = [
  {
    title: "Complaints Date",
    className: " w-[180px] h-full ms-[1rem]",
    key: "complaint_date",
    headertype: "display",
  },
  {
    title: "Type of Request",
    className: " w-[180px] h-full ms-[1rem]",
    headertype: "filter",
    key: "type_of_request",
    options: [
      {
        content: "General",
        id: "General",
      },
      {
        content: "Order Related",
        id: "Order Related",
      },
    ],
  },

  {
    title: "Customer ID",
    className: " w-[260px] h-full ms-[1rem]",
    headertype: "display",
    key: "customer_id",
  },
  {
    title: "Complaints",
    className: "w-[220px] h-full ms-[1rem]",
    headertype: "display",
    key: "complaint",
  },
  {
    title: "Attending Person",
    className: "w-[180px] h-full ms-[1rem]",
    headertype: "filter",
    key: "attending_person",
    options: "userList",
  },
  {
    title: "Brand",
    className: "w-[180px] h-full ms-[1rem]",
    headertype: "display",
    key: "attending_person",
    options: [],
  },
  {
    title: "Complaint Status",
    className: "w-[200px] h-full ms-[2.5rem]",
    headertype: "filter",
    key: "complaint_status",
    options: [
      {
        content: "New",
        id: "New",
      },

      {
        content: "In Progress",
        id: "In Progress",
      },
      {
        content: "Resolved",
        id: "Resolved",
      },
    ],
  },

  {
    title: "Time Taken",
    className: "w-[150px] h-full ms-[1rem]",
    headertype: "display",
    key: "time_taken",
  },
];

export const typesOfRequestDropDown_serviceComplaints = [
  {
    content: "General",
    id: "General",
  },
  {
    content: "Order Related",
    id: "Order Related",
  },
];

export const complaintStatusDropDown_ServiceComplaitns = [
  // {
  //      content: "Complaints Status",
  //      id: "ComplaintsStatus",
  // },
  {
    content: "New",
    id: "New",
  },

  {
    content: "In Progress",
    id: "In Progress",
  },
  {
    content: "Resolved",
    id: "Resolved",
  },
];

export const complaintStatusAsObj = {
  resolved: {
    content: "Resolved",
    id: "Resolved",
  },
  inProgress: {
    content: "In Progress",
    id: "In Progress",
  },
  new: {
    content: "New",
    id: "New",
  },
};

export const selectColorCode_ServiceComplaint = (content) => {
  if (content.id === complaintStatusAsObj.new.id) {
    return " bg-[#4C49ED] text-[#4C49ED] bg-opacity-20 ";
  }

  if (content.id === complaintStatusAsObj.inProgress.id) {
    return " bg-[#EB7F00] bg-opacity-20 text-[#F2994A] ";
  }

  if (content.id === complaintStatusAsObj.resolved.id) {
    return " bg-[#32936F] bg-opacity-25 text-[#05AF9B]";
  }

  return "bg-[#019BA229]";
};

export const findContentObj = (text, array) => {
  for (let i = 0; i < array.length; i++) {
    let indiv = array[i];

    if (indiv?.content?.toUpperCase() === text?.toUpperCase()) {
      return indiv;
    }
  }

  return { id: text, content: text };
};
