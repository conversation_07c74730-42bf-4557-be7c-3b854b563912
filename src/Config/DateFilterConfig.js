export const dataFilter_DropDownObj = {
  lastyear: {
    id: "Last year",
    content: "Last year",
  },
  lastMonth: {
    id: "Last Month",
    content: "Last Month",
  },
  lastWeek: {
    id: "Last Week",
    content: "Last Week",
  },
  customDate: {
    id: "Custom Date",
    content: "Custom Date",
  },
  all: {
    id: "Till now",
    content: "Till now",
  },
};

export const dataFilter_Dropdown = [
  dataFilter_DropDownObj.all,
  dataFilter_DropDownObj.lastMonth,
  dataFilter_DropDownObj.lastWeek,
  dataFilter_DropDownObj.lastyear,
  dataFilter_DropDownObj.customDate,
];

export const orderDateFilter_Obj = {
  today: {
    id: "Today",
    content: "Today",
  },
  lastWeek: {
    id: "Last week",
    content: "Last week",
  },
  lastMonth: {
    id: "Last month",
    content: "Last month",
  },
  lastYear: {
    id: "Last year",
    content: "Last year",
  },
  custom: {
    id: "Custom",
    content: "Custom",
  },
};

export const orderDateFilter_dropDown = [
  orderDateFilter_Obj.today,
  orderDateFilter_Obj.lastWeek,
  orderDateFilter_Obj.lastMonth,
  orderDateFilter_Obj.lastYear,
  orderDateFilter_Obj.custom,
];

export const getStartAndEndDate = (n = 7) => {
  const today = new Date(); // Get today's date
  const sevenDaysBack = new Date(today); // Create a new date object based on today
  sevenDaysBack.setDate(today.getDate() - n); // Subtract 7 days

  let endDate = new Date(today);
  endDate.setHours(23, 59, 59, 999);

  return [sevenDaysBack, endDate];
};

export const LastWeek_DateFilter_StartAndDateAPI = () => {
  const today = new Date(); // Get today's date
  const sevenDaysBack = new Date(today); // Create a new date object based on today
  sevenDaysBack.setDate(today.getDate() - 7); // Subtract 7 days

  let endDate = new Date(today);
  endDate.setHours(23, 59, 59, 999);

  return [sevenDaysBack, endDate];
};

export const LastMonth_DateFilter_StartAndDateAPI = () => {
  const today = new Date(); // Get today's date
  const sevenDaysBack = new Date(today); // Create a new date object based on today
  sevenDaysBack.setDate(today.getDate() - 30); // Subtract 7 days

  let endDate = new Date(today);
  endDate.setHours(23, 59, 59, 999);

  return [sevenDaysBack, endDate];
};

export const LastYear_DateFilter_StartAndDateAPI = () => {
  const today = new Date(); // Get today's date
  const sevenDaysBack = new Date(today); // Create a new date object based on today
  sevenDaysBack.setDate(today.getDate() - 365); // Subtract 7 days

  let endDate = new Date(today);
  endDate.setHours(23, 59, 59, 999);

  return [sevenDaysBack, endDate];
};
