export const marketingWalkinTableConfig = [
  {
    title: "Assigned By",
    className: " w-[180px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "filterwithsearch",
    key: "assignedBy",
    options: "userList"
  },

  {
    title: "Salutation",
    className: " w-[180px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "display",
    key: "salutation",
  },
  {
    title: "Name",
    className: " w-[180px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "display",
    key: "name",
  },
  {
    title: "Date of Birth",
    className: "w-[180px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "display",
    key: "date_of_birth",
  },
  {
    title: "Mobile Phone",
    className: "w-[180px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "display",
    key: "mobile_phone",
  },
  {
    title: "Current Location",
    className: "w-[180px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "display",
    key: "current_location",
  },
  {
    title: "Lead Status",
    className: "w-[180px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "filter",
    key: "lead_status",
    options: [
      { id: "NEW LEAD", content: "NEW LEAD" },
      { id: "QUOTE SENT", content: "QUOTE SENT" },
      { id: "IN FOLLOW UP", content: "IN FOLLOW UP" },
      { id: "DROPPED", content: "DROPPED" },
      { id: "ORDER CLOSED", content: "ORDER CLOSED" },
    ],
  },
  {
    title: "Message Status",
    className: "w-[180px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "display",
    key: "Message sent",
  },
  {
    title: "Architect Details",
    className: "w-[180px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "display",
    key: "architect_details",
  },
  {
    title: "Engineer Details",
    className: "w-[180px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "display",
    key: "engineer_details",
  },
  {
    title: "Building Area",
    className: "w-[180px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "display",
    key: "building_area",
  },
  {
    title: "No of Bathrooms",
    className: "w-[180px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "display",
    key: "no_of_bathrooms",
  },
  {
    title: "Category",
    className: "w-[180px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "filter",
    key: "category",
    options: [
      { id: "RESIDENCE", content: "RESIDENCE" },
      { id: "COMMERCIAL", content: "COMMERCIAL" },
    ],
  },
  {
    title: "Status Of Building",
    className: "w-[180px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "filter",
    key: "status_of_building",
    options: [
      { id: "BRICK", content: "BRICK" },
      { id: "PILLAR", content: "PILLAR" },
      { id: "PLASTERING", content: "PLASTERING" },
      { id: "PLUMBING", content: "PLUMBING" },
      { id: "FINAL", content: "FINAL" },
    ],
  },

  {
    title: "Showroom Visited",
    className: "w-[180px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "filter",
    key: "customer_visited_following_showroom",
    options: [
      { id: "DHARAN NATURAL STONE PARK", content: "DHARAN NATURAL STONE PARK" },
      { id: "DHARAN BATH FITTINGS", content: "DHARAN BATH FITTINGS" },
      { id: "D-CERAMICS", content: "D-CERAMICS" },
      { id: "DHARAN UNIVERSAL STONES", content: "DHARAN UNIVERSAL STONES" }
    ]
  },
  {
    title: "Category",
    className: "w-[180px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "display",
    key: "Category",
  },
  {
    title: "Sales Person",
    className: "w-[180px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "filter",
    options: "userList",
    key: "name_of_the_sales_person",
  },

  {
    title: "Customer In Time",
    className: "w-[200px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "display",
    key: "customer_in_time",
  },
  {
    title: "Customer Out Time",
    className: "w-[200px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "display",
    key: "customer_out_time",
  },
  {
    title: "Remarks",
    className: "w-[180px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "display",
    key: "remarks",
  },

  {
    title: "How do you know about the showroom",
    className: "w-[300px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "display",
    key: "how_do_you_know_about_our_showroom",
  },
  {
    title: "Unit",
    className: "w-[120px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "display",
    key: "unit",
  },
  {
    title: "Walk In Type",
    className: "w-[150px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "filter",
    key: "walk_in_type",
    options: [
      { id: "OLD", content: "OLD" },
      { id: "NEW", content: "NEW" },
    ],
  },
  {
    title: "Purpose of Visit",
    className: "w-[180px] h-full ms-[1rem]",
    listtype: "display",
    headertype: "filter",
    key: "purpose_of_visit",
    options: [
      { id: "first visit", content: "first visit" },
      { id: "revisit", content: "revisit" },
      { id: "shortage", content: "shortage" },
      { id: "payment", content: "payment" },
      { id: "complaint", content: "complaint" },
    ],
  },
  {
    title: "History",
    className: "w-[150px] h-full ms-[1rem]",
    listtype: "button",
    headertype: "display",
    key: "history",
  },
];

export const marketingWalkinFormConfig = [
  {
    type: "datetime",
    required: false,
    key: "customer_in_time",
    title: "Customer In Time",
    defaultValue: new Date(),
    format: "date",

    name: "Customer In Time",
  },

  {
    type: "singleselect",
    required: false,
    key: "walk_in_type",
    options: ["OLD", "NEW"],
    defaultValue: "Walk In Type",
    placeholder: "Walk In Type",

    name: "Walk In Type",
  },

  {
    type: "singleselect",
    required: false,
    key: "purpose_of_visit",
    options: ["first visit", "revisit", "shortage", "payment", "complaint"],
    defaultValue: "Purpose of Visit",
    placeholder: "Purpose of Visit",

    name: "Purpose of Visit",
  },

  {
    type: "singleselect",
    required: false,
    key: "salutation",
    options: ["Mr.", "Mrs.", "Ms.", "Dr."],
    defaultValue: "Salutation",
    placeholder: "Salutation",

    name: "Salutation",
  },
  {
    type: "text",
    required: true,
    key: "name",
    placeholder: "Name",
    defaultValue: "",

    name: "Name",
  },

  {
    type: "date",
    required: false,
    key: "date_of_birth",
    title: "DOB",
    defaultValue: new Date(),
    format: "date",

    name: "DOB",
  },

  {
    type: "number",
    required: true,
    key: "mobile_phone",
    placeholder: "Mobile Phone",
    defaultValue: "",

    name: "Mobile Phone",
  },

  {
    type: "text",
    required: false,
    key: "current_location",
    placeholder: "Current Location",
    defaultValue: "",

    name: "Current Location",
  },

  {
    type: "text",
    required: false,
    key: "architect_details",
    placeholder: "Architect Details",
    defaultValue: "",

    name: "Architect Details",
  },

  {
    type: "text",
    required: false,
    key: "engineer_details",
    placeholder: "Engineer Details",
    defaultValue: "",

    name: "Engineer Details",
  },

  {
    type: "number",
    required: false,
    key: "building_area",
    placeholder: "Building Area",
    defaultValue: "",

    name: "Building Area",
  },

  {
    type: "number",
    required: false,
    key: "no_of_bathrooms",
    placeholder: "No of Bathroom",
    defaultValue: "",

    name: "No of Bathrooms",
  },

  {
    type: "singleselect",
    required: false,
    key: "category",
    options: ["RESIDENCE", "COMMERCIAL"],
    defaultValue: "Category",
    placeholder: "Category",

    name: "Category",
  },

  {
    type: "singleselect",
    required: false,
    key: "status_of_building",
    options: ["BRICK", "PILLAR", "PLASTERING", "PLUMBING", "FINAL"],
    defaultValue: "Status Of Building",
    placeholder: "Status Of Building",

    name: "Status of Building",
  },

  {
    type: "textarea",
    required: false,
    key: "remarks",
    options: [],
    defaultValue: "",
    placeholder: "Remarks",

    name: "Remarks",

    //--------------------
    // isChained: true,
    // chain_logic: {
    //   value: "Others",
    //   add: {
    //     type: "text",
    //     required: false,
    //     key: "others",
    //     placeholder: "enter others....",
    //     defaultValue: "",
    //   },
    // },
    //--------------------
  },

  {
    type: "multipleselect",
    required: false,
    key: "customer_visited_following_showroom",
    title: "Customer Visited Following Showroom",
    defaultValue: [],
    options: [
      "DHARAN NATURAL STONE PARK",
      "DHARAN BATH FITTINGS",
      "D-CERAMICS",
      "DHARAN UNIVERSAL STONES",
    ],

    name: "Visited also other showrooms",
  },

  {
    type: "selectortype",
    required: false,
    key: "how_do_you_know_about_our_showroom",
    options: ["Marketing", "Company Staff", "Social Media"],
    defaultValue: "How do you know about the showroom",
    placeholder: "How do you know about the showroom",

    name: "How do you know about the showroom",
  },

  {
    type: "singleselect",
    required: false,
    key: "Category",
    options: ["A", "B", "C", "D"],
    defaultValue: "Category",
    placeholder: "Category",

    name: "Category",
  },

  {
    type: "singleselect",
    required: false,
    key: "lead_status",
    options: [
      "NEW LEAD",
      "QUOTE SENT",
      "IN FOLLOW UP",
      "DROPPED",
      "ORDER CLOSED",
    ],
    defaultValue: "Lead Status",
    placeholder: "Lead Status",

    name: "Lead Status",
  },

  {
    type: "multipleselect",
    required: false,
    key: "name_of_the_sales_person",
    options: "userList",
    defaultValue: [],
    title: "Sales Person",

    name: "Sales Person",
  },

  {
    type: "datetime",
    required: false,
    key: "customer_out_time",
    title: "Customer Out Time",
    defaultValue: new Date(),
    format: "date",

    name: "Customer Out Time",
  },

  {
    type: "date",
    required: false,
    key: "Follow_up_Date_1",
    title: "Follow up Date 1",
    defaultValue: new Date(),
    format: "date",

    name: "Follow up Date 1",
  },

  {
    type: "textarea",
    required: false,
    key: "Follow_up_Date_1_Remarks",
    placeholder: "Follow up Date 1 Remarks",
    defaultValue: "",

    name: "Follow up Date 1 Remarks",
  },

  {
    type: "date",
    required: false,
    key: "Follow_up_Date_2",
    title: "Follow up Date 2",
    defaultValue: new Date(),
    format: "date",

    name: "Follow up Date 2",
  },

  {
    type: "textarea",
    required: false,
    key: "Follow_up_Date_2_Remarks",
    placeholder: "Follow up Date 2 Remarks",
    defaultValue: "",

    name: "Follow up Date 2 Remarks",
  },

  {
    type: "date",
    required: false,
    key: "Follow_up_Date_3",
    title: "Follow up Date 3",
    defaultValue: new Date(),
    format: "date",

    name: "Follow up Date 3",
  },

  {
    type: "textarea",
    required: false,
    key: "Follow_up_Date_3_Remarks",
    placeholder: "Follow up Date 3 Remarks",
    defaultValue: "",

    name: "Follow up Date 3 Remarks",
  },
];
