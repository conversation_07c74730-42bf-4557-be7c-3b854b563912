export const purchaseTableConfig = [
  {
    title: "Product Details",
    className: "w-[180px] h-full ms-[1rem]",
    key: "productDetails",
    headertype: "display",
  },
  {
    title: "Batch no",
    className: "w-[180px] h-full ms-[1rem]",
    key: "batchNo",
    headertype: "display",
    options: [
      { id: "Brand 1", content: "Brand 1" },
      { id: "Brand 2", content: "Brand 2" },
      { id: "Brand 3", content: "Brand 3" },
    ],
  },
  {
    title: "Quantity",
    className: "w-[100px] h-full ms-[1rem]",
    key: "quantity",
    headertype: "display",
  },
  {
    title: "Material",
    className: "w-[150px] h-full ms-[1rem]",
    key: "material",
    headertype: "display",
  },
  {
    title: "Division",
    className: "w-[150px] h-full ms-[1rem]",
    key: "division",
    headertype: "display",
  },
  {
    title: "Size",
    className: "w-[100px] h-full ms-[1rem]",
    key: "size",
    headertype: "display",
  },
  {
    title: "Category",
    className: "w-[150px] h-full ms-[1rem]",
    key: "category",
    headertype: "display",
  },
  {
    title: "Sub-category",
    className: "w-[150px] h-full ms-[1rem]",
    key: "subCategory",
    headertype: "display",
  },
  {
    title: "Brand",
    className: "w-[150px] h-full ms-[1rem]",
    key: "brand",
    headertype: "display",
  },
  {
    title: "Grade",
    className: "w-[150px] h-full ms-[1rem]",
    key: "grade",
    headertype: "display",
  },
  {
    title: "Warehouse Status",
    className: "w-[200px] h-full ms-[1rem]",
    key: "warehouseStatus",
    headertype: "filter",
    options: [
      { id: "Stock Ordered", content: "Stock Ordered" },
      { id: "In stock", content: "In stock" },
      { id: "out of stock", content: "out of stock" },
      { id: "Sold", content: "Sold" },
      { id: "Delayed", content: "Delayed" },
      { id: "Delivery", content: "Delivery" },
    ],
  },
  {
    title: "Vehicle Number",
    className: "w-[200px] h-full ms-[1rem]",
    key: "vehicleNumber",
    headertype: "display",
  },
  {
    title: "Nature of Order",
    className: "w-[200px] h-full ms-[1rem]",
    key: "Nature_of_Order",
    headertype: "filter",
    options: [
      { id: "Available", content: "Available" },
      { id: "Against Order", content: "Against Order" },
      { id: "Deliverd", content: "Deliverd" },
      { id: "out of stock", content: "out of stock" },
      { id: "Stock filling", content: "Stock filling" },
      { id: "B", content: "B" },
      { id: "C", content: "C" },
    ],
  },
  {
    title: "Unloading Date",
    className: "w-[200px] h-full ms-[1rem]",
    key: "unloadingDate",
    headertype: "display",
  },
  {
    title: "ETA",
    className: "w-[200px] h-full ms-[1rem]",
    key: "ETA",
    headertype: "display",
  },
];

export const purchase_NatureOfOrder = [
  {
    id: "Against_Order",
    content: "AO",
  },
];

export const switchToggleButton_IsPresent = (text, array = []) => {
  if (!array) {
    return false;
  }
  if (array?.length > 1 || array?.length === 0) return false;

  if (text.toUpperCase() === array[0].content.toUpperCase()) {
    return true;
  }

  return false;
};

export const purchase_form_config = [
  {
    key: "productDetails",
    type: "text",
    placeholder: "Product Details",
    defaultValue: "",
    required: true,
  },
  {
    key: "batchNo",
    type: "text",
    placeholder: "Batch No",
    defaultValue: "",
    required: true,
  },
  {
    key: "Quantity",
    type: "number",
    placeholder: "Quantity",
    defaultValue: "",
    required: true,
  },
  {
    key: "material",
    type: "text",
    placeholder: "Material",
    defaultValue: "",
    required: true,
  },
  {
    key: "division",
    type: "text",
    placeholder: "Division",
    defaultValue: "",
    required: true,
  },
  {
    key: "size",
    type: "text",
    placeholder: "Size",
    defaultValue: "",
    required: true,
  },
  {
    key: "category",
    type: "text",
    placeholder: "Category",
    defaultValue: "",
    required: true,
  },
  {
    key: "subCategory",
    type: "text",
    placeholder: "Sub-category",
    defaultValue: "",
    required: true,
  },
  {
    key: "brand",
    type: "text",
    placeholder: "Brand",
    defaultValue: "",
    required: true,
  },
  {
    key: "grade",
    type: "text",
    placeholder: "Grade",
    defaultValue: "",
    required: true,
  },

  {
    type: "singleselect",
    options: ["Stock Ordered", "In stock", "out of stock"],
    key: "warehouseStatus",
    defaultValue: "Warehouse Status",
    placeholder: "Warehouse Status",
    required: true,
  },

  {
    key: "vehicleNumber",
    type: "text",
    placeholder: "Vehicle Number",
    defaultValue: "",
    required: true,
  },

  {
    type: "singleselect",
    options: [
      "Available",
      "Against Order",
      "Deliverd",
      "out of stock",
      "Stock filling",
      "B",
      "C",
    ],
    key: "Nature_of_Order",
    defaultValue: "Nature of Order",
    placeholder: "Nature of Order",
    required: true,
  },

  {
    key: "unloadingDate",
    type: "text",
    placeholder: "Unloading Date",
    defaultValue: "",
    required: true,
  },

  {
    key: "ETA",
    type: "text",
    placeholder: "ETA",
    defaultValue: "",
    required: true,
  },
];
