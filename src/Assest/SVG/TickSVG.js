import React from "react";
import {
     taskmanagement_completed_status,
     taskmanagement_opentask_status,
     taskmanagement_overdue_status,
} from "../../Config/TaskManagementConfig";

const TickSVG = ({ content }) => {
     if (content?.id === taskmanagement_opentask_status.id) {
          return (
               <svg
                    width="24"
                    height="25"
                    viewBox="0 0 24 25"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
               >
                    <g clip-path="url(#clip0_2517_9947)">
                         <path
                              d="M23 12.2637C23 18.3503 18.0867 23.2637 12 23.2637C5.91333 23.2637 1 18.3503 1 12.2637C1 6.17701 5.91333 1.26367 12 1.26367C18.0867 1.26367 23 6.17701 23 12.2637Z"
                              fill="#F2F2F2"
                              stroke="#FB8B24"
                              stroke-width="1.1"
                         />
                         <path
                              d="M10.2791 16.2227C10.0611 16.2227 9.91581 16.1501 9.77049 16.0048L6.93673 13.171C6.64609 12.8803 6.64609 12.4444 6.93673 12.1537C7.22737 11.8631 7.66334 11.8631 7.95398 12.1537L10.2065 14.4062L16.092 8.52072C16.3826 8.23007 16.8186 8.23007 17.1092 8.52072C17.3999 8.81136 17.3999 9.24732 17.1092 9.53796L10.6424 16.0048C10.6424 16.1501 10.4971 16.2227 10.2791 16.2227Z"
                              fill="#FB8B24"
                         />
                    </g>
                    <defs>
                         <clipPath id="clip0_2517_9947">
                              <rect
                                   width="24"
                                   height="24"
                                   fill="white"
                                   transform="translate(0 0.263672)"
                              />
                         </clipPath>
                    </defs>
               </svg>
          );
     }
     if (content?.id === taskmanagement_overdue_status.id) {
          return (
               <svg
                    width="24"
                    height="25"
                    viewBox="0 0 24 25"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
               >
                    <path
                         d="M23 12.2637C23 18.3503 18.0867 23.2637 12 23.2637C5.91333 23.2637 1 18.3503 1 12.2637C1 6.17701 5.91333 1.26367 12 1.26367C18.0867 1.26367 23 6.17701 23 12.2637Z"
                         fill="#F2F2F2"
                         stroke="#FF7E7E"
                         stroke-width="1.1"
                    />
                    <path
                         d="M10.2791 16.2227C10.0611 16.2227 9.91581 16.1501 9.77049 16.0048L6.93673 13.171C6.64609 12.8803 6.64609 12.4444 6.93673 12.1537C7.22737 11.8631 7.66334 11.8631 7.95398 12.1537L10.2065 14.4062L16.092 8.52072C16.3826 8.23007 16.8186 8.23007 17.1092 8.52072C17.3999 8.81136 17.3999 9.24732 17.1092 9.53796L10.6424 16.0048C10.6424 16.1501 10.4971 16.2227 10.2791 16.2227Z"
                         fill="#FF7E7E"
                    />
               </svg>
          );
     }
     if (content?.id === taskmanagement_completed_status.id) {
          return (
               <svg
                    width="24"
                    height="25"
                    viewBox="0 0 24 25"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
               >
                    <g clip-path="url(#clip0_2503_9814)">
                         <path
                              d="M22 12.2637C22 17.797 17.5333 22.2637 12 22.2637C6.46667 22.2637 2 17.797 2 12.2637C2 6.73034 6.46667 2.26367 12 2.26367C17.5333 2.26367 22 6.73034 22 12.2637Z"
                              fill="#5DA283"
                              stroke="#5DA283"
                         />
                         <path
                              d="M10.4359 15.8621C10.2378 15.8621 10.1056 15.7961 9.97353 15.6639L7.39738 13.0878C7.13316 12.8236 7.13316 12.4272 7.39738 12.163C7.6616 11.8988 8.05793 11.8988 8.32215 12.163L10.3699 14.2107L15.7203 8.86027C15.9845 8.59605 16.3809 8.59605 16.6451 8.86027C16.9093 9.12449 16.9093 9.52082 16.6451 9.78504L10.7662 15.6639C10.7662 15.7961 10.6341 15.8621 10.4359 15.8621Z"
                              fill="#F5F4F3"
                         />
                    </g>
                    <defs>
                         <clipPath id="clip0_2503_9814">
                              <rect
                                   width="24"
                                   height="24"
                                   fill="white"
                                   transform="translate(0 0.263672)"
                              />
                         </clipPath>
                    </defs>
               </svg>
          );
     }
     return (
          <svg
               width="24"
               height="25"
               viewBox="0 0 24 25"
               fill="none"
               xmlns="http://www.w3.org/2000/svg"
          >
               <path
                    d="M23 12.2637C23 18.3503 18.0867 23.2637 12 23.2637C5.91333 23.2637 1 18.3503 1 12.2637C1 6.17701 5.91333 1.26367 12 1.26367C18.0867 1.26367 23 6.17701 23 12.2637Z"
                    fill="#758694"
                    stroke="#758694"
                    stroke-width="1.1"
               />
               <path
                    d="M10.2791 16.2227C10.0611 16.2227 9.91581 16.1501 9.77049 16.0048L6.93673 13.171C6.64609 12.8803 6.64609 12.4444 6.93673 12.1537C7.22737 11.8631 7.66334 11.8631 7.95398 12.1537L10.2065 14.4062L16.092 8.52072C16.3826 8.23007 16.8186 8.23007 17.1092 8.52072C17.3999 8.81136 17.3999 9.24732 17.1092 9.53796L10.6424 16.0048C10.6424 16.1501 10.4971 16.2227 10.2791 16.2227Z"
                    fill="#F5F4F3"
               />
          </svg>
     );
};

export default TickSVG;
