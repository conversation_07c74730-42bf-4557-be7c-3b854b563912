import React from "react";
import ss from "../Logo/salesandmarketing.png";
import sshover from "../Logo/salesandmarketingHover.png";
const SalesAndMarketingNavBar = ({ isActive = false }) => {
     return (
          <div className=" w-full h-full  flex flex-row justify-center items-center">
               {!isActive ? (
                    <img src={ss} alt="salesandmarketing nav bar " />
               ) : (
                    <img src={sshover} alt="sales" />
               )}
          </div>
     );
};

export default SalesAndMarketingNavBar;
