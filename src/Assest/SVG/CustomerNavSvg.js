import React from "react";

const CustomerNavSvg = ({ color = "black" }) => {
     return (
          <svg
               width="56"
               height="56"
               viewBox="0 0 56 62"
               fill="none"
               xmlns="http://www.w3.org/2000/svg"
          >
               <path
                    d="M24.6867 34.5723C19.7319 34.5723 15.5 35.3377 15.5 38.3968C15.5 41.4584 19.705 42.2501 24.6867 42.2501C29.6414 42.2501 33.8733 41.4847 33.8733 38.4256C33.8733 35.364 29.6683 34.5723 24.6867 34.5723Z"
                    fill={color}
               />
               <path
                    opacity="0.4"
                    d="M24.6882 31.656C28.0627 31.656 30.7669 29.0077 30.7669 25.703C30.7669 22.3983 28.0627 19.75 24.6882 19.75C21.3149 19.75 18.6094 22.3983 18.6094 25.703C18.6094 29.0077 21.3149 31.656 24.6882 31.656Z"
                    fill={color}
               />
               <path
                    opacity="0.4"
                    d="M33.2175 25.8108C33.2175 27.4937 32.7013 29.064 31.7962 30.3684C31.702 30.5025 31.7852 30.6834 31.9491 30.7121C32.1766 30.7493 32.4102 30.772 32.6487 30.7768C35.0215 30.8379 37.1509 29.3419 37.7392 27.0888C38.6113 23.7458 36.0526 20.7441 32.793 20.7441C32.4396 20.7441 32.1008 20.7801 31.7705 20.846C31.7253 20.8555 31.6764 20.8771 31.6519 20.9154C31.6201 20.9645 31.6433 21.028 31.6751 21.0699C32.6548 22.4151 33.2175 24.0524 33.2175 25.8108Z"
                    fill={color}
               />
               <path
                    d="M40.2231 34.9627C39.7889 34.0559 38.7407 33.4343 37.1458 33.1288C36.3936 32.9492 34.3559 32.694 32.4613 32.73C32.4332 32.7336 32.4173 32.7527 32.4148 32.7647C32.4112 32.7839 32.4197 32.8126 32.4564 32.833C33.3322 33.257 36.7165 35.1016 36.2908 38.992C36.2725 39.1621 36.4107 39.3058 36.5831 39.2819C37.4161 39.1657 39.5589 38.7141 40.2231 37.3103C40.5912 36.5677 40.5912 35.7053 40.2231 34.9627Z"
                    fill={color}
               />
          </svg>
     );
};

export default CustomerNavSvg;
