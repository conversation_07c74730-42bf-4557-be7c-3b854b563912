import React from "react";

const OrderNavSvg = ({ color = "black" }) => {
     return (
          <svg
               width="56"
               height="56"
               viewBox="0 0 56 56"
               fill="none"
               xmlns="http://www.w3.org/2000/svg"
          >
               <path
                    opacity="0.4"
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M20.3867 38.7359C20.3867 37.6859 21.2367 36.8359 22.2867 36.8359C23.3242 36.8359 24.1742 37.6859 24.1742 38.7359C24.1742 39.7734 23.3242 40.6234 22.2867 40.6234C21.2367 40.6234 20.3867 39.7734 20.3867 38.7359ZM34.4492 38.7359C34.4492 37.6859 35.2992 36.8359 36.3492 36.8359C37.3867 36.8359 38.2367 37.6859 38.2367 38.7359C38.2367 39.7734 37.3867 40.6234 36.3492 40.6234C35.2992 40.6234 34.4492 39.7734 34.4492 38.7359Z"
                    fill={color}
               />
               <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M38.2384 20.9367C39.0009 20.9367 39.5009 21.1992 40.0009 21.7742C40.5009 22.3492 40.5884 23.1742 40.4759 23.9229L39.2884 32.1229C39.0634 33.6992 37.7134 34.8604 36.1259 34.8604H22.4884C20.8259 34.8604 19.4509 33.5854 19.3134 31.9367L18.1634 18.3104L16.2759 17.9854C15.7759 17.8979 15.4259 17.4104 15.5134 16.9104C15.6009 16.3979 16.0884 16.0604 16.6009 16.1354L19.5822 16.5854C20.0072 16.6617 20.3197 17.0104 20.3572 17.4354L20.5947 20.2354C20.6322 20.6367 20.9572 20.9367 21.3572 20.9367H38.2384ZM30.6634 27.4354H34.1259C34.6509 27.4354 35.0634 27.0104 35.0634 26.4979C35.0634 25.9729 34.6509 25.5604 34.1259 25.5604H30.6634C30.1384 25.5604 29.7259 25.9729 29.7259 26.4979C29.7259 27.0104 30.1384 27.4354 30.6634 27.4354Z"
                    fill={color}
               />
          </svg>
     );
};

export default OrderNavSvg;
