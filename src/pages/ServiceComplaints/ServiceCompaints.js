import React from "react";
import DashboardHeaderTemplate from "../../Components/DashBoardTemplate/DashboardHeaderTemplate";
import ServiceComplaintsLayout from "../../Components/ServiceComplaintsComponents/ServiceComplaintsLayout";
import { useSearchParams } from "react-router-dom";
import ServiceComplaintAddNewLayout from "../../Components/ServiceComplaintsComponents/ServiceComplaintAddNew/ServiceComplaintAddNewLayout";
import ServiceComplaintsEditLayout from "../../Components/ServiceComplaintsComponents/ServiceComplaintEdit/ServiceComplaintsEditLayout";
import ModelComponents from "../../BasicUIElements/ModelComponents";
import { routes } from "../../Config/routesConfig";

const ServiceCompaints = () => {
  const [searchParams] = useSearchParams();
  const page = searchParams.get("page");
  return (
    <div className=" w-full h-full  flex flex-col ">
      <DashboardHeaderTemplate heading="Service Complaints" />
      <div className=" flex-1 overflow-auto ">
        <ServiceComplaintsLayout />
      </div>

      {page === routes?.servicecomplaintsAddNew?.searchParams && (
        <ModelComponents>
          <ServiceComplaintAddNewLayout />
        </ModelComponents>
      )}
      {page === routes?.servicecomplaintsEdit?.searchParams && (
        <ModelComponents>
          <ServiceComplaintsEditLayout />
        </ModelComponents>
      )}
    </div>
  );
};

export default ServiceCompaints;
