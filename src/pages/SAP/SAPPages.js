import React from "react";
import { Outlet, useSearchParams } from "react-router-dom";

// Template ( all dashboard )
import DashboardHeaderTemplate from "../../Components/DashBoardTemplate/DashboardHeaderTemplate";

// Layout
import SAPLayout from "../../Components/SAPComponents/SAPLayout";
import SAPAddLayout from "../../Components/SAPComponents/SAPAdd/SAPAddLayout";
import SAPEditLayout from "../../Components/SAPComponents/SAPEdit/SAPEditLayout";
import ModelComponents from "../../BasicUIElements/ModelComponents";

const SAPPage = () => {
     const [searchParams] = useSearchParams();
     const page = searchParams.get("page");

     return (
          <div className="w-full h-full flex flex-col">
               <DashboardHeaderTemplate heading="SAP" />
               <div className="flex-1 overflow-auto">
                    <SAPLayout />
               </div>
               <Outlet />

               {page === "addasap" && (
                    <ModelComponents>
                         <SAPAddLayout />
                    </ModelComponents>
               )}
               {page === "editsap" && (
                    <ModelComponents>
                         <SAPEditLayout />
                    </ModelComponents>
               )}
          </div>
     );
};

export default SAPPage;