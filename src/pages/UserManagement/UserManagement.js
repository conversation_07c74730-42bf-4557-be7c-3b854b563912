import React from "react";
import DashboardHeaderTemplate from "../../Components/DashBoardTemplate/DashboardHeaderTemplate";
import UserManagementMainPageLayout from "../../Components/UserManagementComponents/UserManagementMainPageLayout";
import { Outlet } from "react-router-dom";

const UserManagement = () => {
     return (
          <div className=" w-full h-full  flex flex-col ">
               <DashboardHeaderTemplate heading="User Management" />
               <div className=" flex-1 overflow-auto ">
                    <UserManagementMainPageLayout />
               </div>
               <Outlet />
          </div>
     );
};

export default UserManagement;
