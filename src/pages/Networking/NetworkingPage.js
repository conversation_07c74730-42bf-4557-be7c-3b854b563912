import React from "react";
import DashboardHeaderTemplate from "../../Components/DashBoardTemplate/DashboardHeaderTemplate";
import NetworkingLayout from "../../Components/NetworkingComponent/NetworkingLayout";
import { Outlet } from "react-router-dom";

const NetworkingPage = () => {
     return (
          <div className=" w-full h-full  flex flex-col ">
               <DashboardHeaderTemplate heading="Networking" />
               <div className=" flex-1 overflow-hidden">
                    <NetworkingLayout />
               </div>

               <Outlet />
          </div>
     );
};

export default NetworkingPage;
