import React from "react";
import { useNavigate } from "react-router-dom";
import ButtonComponent from "../BasicUIElements/ButtonComponent";

const Home = () => {
  const navigate = useNavigate();

  return (
    <div className=" flex flex-col gap-[0.5rem]">
      Home page
      <ButtonComponent
        className=" border-2 p-2 w-fit h-fit font-poppins rounded-[8px] "
        onClick={() => {
          navigate("/admin/signin");
        }}
      >
        Admin Signin
      </ButtonComponent>
      <ButtonComponent
        className=" border-2 p-2 w-fit h-fit font-poppins rounded-[8px] "
        onClick={() => {
          navigate("/users/signin");
        }}
      >
        Users Signin
      </ButtonComponent>
      <ButtonComponent
        className=" border-2 p-2 w-fit h-fit font-poppins rounded-[8px] "
        onClick={() => {
          navigate("/customer/signin");
        }}
      >
        Customer Signin
      </ButtonComponent>
    </div>
  );
};

export default Home;
