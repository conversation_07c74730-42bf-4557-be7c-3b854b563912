import React from "react";

// Template ( all dashboard )
import Dashboard<PERSON>eaderTemplate from "../../Components/DashBoardTemplate/DashboardHeaderTemplate";

// Layout
import TaskManagementLayout from "../../Components/TaskManagementComponents/TaskManagementLayout";
import { Outlet } from "react-router-dom";

const TaskManagementPage = () => {
     return (
          <div className=" w-full h-full  flex flex-col ">
               <DashboardHeaderTemplate heading="Task Management" />
               <div className=" flex-1 overflow-auto ">
                    <TaskManagementLayout />
               </div>
               <Outlet />
          </div>
     );
};

export default TaskManagementPage;
