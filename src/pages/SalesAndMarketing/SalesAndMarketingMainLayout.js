import React from "react";
import DashboardHeaderTemplate from "../../Components/DashBoardTemplate/DashboardHeaderTemplate";
import SalesAndMarketingLayout from "../../Components/SalesAndMarketingComponents/SalesAndMarketingLayout";

import SalesAndMarketingAddLeadLayout from "../../Components/SalesAndMarketingComponents/SalesAndMarketing_AddLead/SalesAndMarketingAddLeadLayout";
import ModelComponents from "../../BasicUIElements/ModelComponents";

import { useSearchParams } from "react-router-dom";
import { routes } from "../../Config/routesConfig";
import SalesAndMarketingEditLayout2 from "../../Components/SalesAndMarketingComponents/SalesAndMarketing_EditLead/SalesAndMarketingEditLayout2";

const SalesAndMarketingMainLayout = () => {
  const [searchParams] = useSearchParams();
  const page = searchParams.get("page");

  return (
    <div className=" w-full h-full  flex flex-col ">
      <DashboardHeaderTemplate heading="Sales" />
      <div className=" flex-1 overflow-hidden">
        <SalesAndMarketingLayout />
      </div>

      {page === routes?.salesAndMarketingAddLead?.searchParams && (
        <ModelComponents>
          <SalesAndMarketingAddLeadLayout />
        </ModelComponents>
      )}
      {page === routes?.salesAndMarketingEditLead?.searchParams && (
        <ModelComponents>
          <SalesAndMarketingEditLayout2 />
        </ModelComponents>
      )}
      {/* {mainMode === salesAndMarketingPageViewMode.view ? (
        <></>
      ) : (
        mainMode === salesAndMarketingPageViewMode.delete && (
          <ModelComponents>
            <DeleteOrderManagementPage />
          </ModelComponents>
        )
      )} */}
    </div>
  );
};

export default SalesAndMarketingMainLayout;
