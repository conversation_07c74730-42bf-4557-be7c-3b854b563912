import React from "react";
import DashboardHeaderTemplate from "../../Components/DashBoardTemplate/DashboardHeaderTemplate";
import PurchaseLayout from "../../Components/PurchaseComponents/PurchaseLayout";
import { useSearchParams } from "react-router-dom";
import { routes } from "../../Config/routesConfig";
import PurchaseAddLayout from "../../Components/PurchaseComponents/PurchaseAdd/PurchaseAddLayout";
import PurchaseEditLayout from "../../Components/PurchaseComponents/PurchaseEdit/PurchaseEditLayout";
import ModelComponents from "../../BasicUIElements/ModelComponents";

const PurchasePage = () => {
  const [searchParams] = useSearchParams();
  const page = searchParams.get("page");
  return (
    <div className=" w-full h-full  flex flex-col ">
      <DashboardHeaderTemplate heading="Purchase" />
      <div className=" flex-1 overflow-hidden ">
        <PurchaseLayout />
      </div>

      {page === routes?.purchaseAdd?.searchParams && (
        <ModelComponents>
          <PurchaseAddLayout />
        </ModelComponents>
      )}
      {page === routes?.purchaseEdit?.searchParams && (
        <ModelComponents>
          <PurchaseEditLayout />
        </ModelComponents>
      )}
    </div>
  );
};

export default PurchasePage;
