import React from "react";
import DashboardHeaderTemplate from "../../Components/DashBoardTemplate/DashboardHeaderTemplate";
import CustomerLayout from "../../Components/CustomerComponents/CustomerLayout";
import { useSelector } from "react-redux";
import { customerPageViewMode } from "../../Store/CustomerSlices/CustomerSlice";
import CustomerEditPage from "./CustomerEditPage";

const CustomerPage = () => {
     const { ui } = useSelector((state) => {
          return state.customer;
     });

     return (
          <div className=" w-full h-full  flex flex-col ">
               <DashboardHeaderTemplate heading="Customers" />
               <div className=" flex-1 overflow-auto ">
                    <CustomerLayout />
               </div>

               {ui.pageMode === customerPageViewMode.edit && (
                    <CustomerEditPage />
               )}
          </div>
     );
};

export default CustomerPage;
