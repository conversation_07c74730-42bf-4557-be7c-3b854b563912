import React from "react";
import { Outlet, useSearchParams } from "react-router-dom";
import DashboardHeaderTemplate from "../../Components/DashBoardTemplate/DashboardHeaderTemplate";
import TicketingLayout from "../../Components/TicketingComponents/TicketingLayout";
import { routes } from "../../Config/routesConfig";
import TicketingAddLayout from "../../Components/TicketingComponents/TicketingAdd/TicketingAddLayout";
import TicketingEditLayout from "../../Components/TicketingComponents/TicketingEdit/TicketingEditLayout";
import ModelComponents from "../../BasicUIElements/ModelComponents";

const TicketingPage = () => {
  const [searchParams] = useSearchParams();
  const page = searchParams.get("page");
  return (
    <div className=" w-full h-full  flex flex-col ">
      <DashboardHeaderTemplate heading="Tickets" />
      <div className=" flex-1 overflow-auto ">
        <TicketingLayout />
      </div>

      <Outlet />

      {page === routes?.ticketingadd?.searchParams && (
        <ModelComponents>
          <TicketingAddLayout />
        </ModelComponents>
      )}
      {page === routes?.ticketingedit?.searchParams && (
        <ModelComponents>
          <TicketingEditLayout />
        </ModelComponents>
      )}
    </div>
  );
};

export default TicketingPage;
