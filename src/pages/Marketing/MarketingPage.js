import React from "react";
import DashboardHeaderTemplate from "../../Components/DashBoardTemplate/DashboardHeaderTemplate";
import MarketingLayout from "../../Components/MarketingComponents/MarketingLayout";
import { Outlet, useSearchParams } from "react-router-dom";
import { routes } from "../../Config/routesConfig";
import MarketingAdd from "./MarketingAdd";
import ModelComponents from "../../BasicUIElements/ModelComponents";
import MarketingEditLayout from "../../Components/MarketingComponents/MarketingAdd/MarketingEditLayout";

const MarketingPage = () => {
  const [searchParams] = useSearchParams();

  const page = searchParams.get("page");

  return (
    <div className=" w-full h-full  flex flex-col ">
      <DashboardHeaderTemplate heading="Marketing" />
      <div className=" flex-1 overflow-hidden">
        <MarketingLayout />
      </div>

      {page === routes?.marketingAdd.searchParams && <MarketingAdd />}
      {page === routes?.salesAndMarketingEditLead?.searchParams && (
        <ModelComponents>
          <MarketingEditLayout />
        </ModelComponents>
      )}
    </div>
  );
};

export default MarketingPage;
