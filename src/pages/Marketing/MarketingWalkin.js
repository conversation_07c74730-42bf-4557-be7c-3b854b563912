import React from "react";
import DashboardHeaderTemplate from "../../Components/DashBoardTemplate/DashboardHeaderTemplate";
import MarketingWalkinLayout from "../../Components/MarketingWalkinComponents/MarketingWalkinLayout";
import { useSearchParams } from "react-router-dom";
import ModelComponents from "../../BasicUIElements/ModelComponents";
import { routes } from "../../Config/routesConfig";
import MarketingWalkinEditLayout from "../../Components/MarketingWalkinComponents/MarketingWalkinEdit/MarketingWalkinEditLayout";
import MarketingWalkinAddLayout from "../../Components/MarketingWalkinComponents/MarketingWalkinAdd/MarketingWalkinAddLayout";

const MarketingWalkin = () => {
  const [searchParams] = useSearchParams();

  const page = searchParams.get("page");
  return (
    <div className=" w-full h-full  flex flex-col ">
      <DashboardHeaderTemplate heading="Marketing" />{" "}
      <div className=" flex-1 overflow-hidden">
        <MarketingWalkinLayout />
      </div>
      {page === routes?.marketingWalkinAdd.searchParams && (
        <ModelComponents>
          <MarketingWalkinAddLayout />
        </ModelComponents>
      )}
      {page === routes?.marketingWalkinEdit?.searchParams && (
        <ModelComponents>
          <MarketingWalkinEditLayout />
        </ModelComponents>
      )}
    </div>
  );
};

export default MarketingWalkin;
