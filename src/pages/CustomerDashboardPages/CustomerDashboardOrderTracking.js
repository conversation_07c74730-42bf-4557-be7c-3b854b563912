import React, { useEffect, useState } from "react";
import CustomerDashboardTrackingLayout from "../../Components/CustomerDashboardTracking.js/CustomerDashboardTrackingLayout";
import DashboardHeaderTemplate from "../../Components/DashBoardTemplate/DashboardHeaderTemplate";
import { useSearchParams } from "react-router-dom";
import { routes } from "../../Config/routesConfig";
import { useSelector } from "react-redux";
import { API_ENDPOINTS, api_headers, BaseURL } from "../../API/APIConfig";
import { GetAPI } from "../../API/GetAPI";

const CustomerDashboardOrderTracking = () => {
  let [parmas] = useSearchParams();

  let billId = parmas.get(routes?.customerDashboardTracking?.searchParams);

  const [state, setState] = useState({
    orders: [],
    orderSummary: [{ title: "asdff", qty: "3e", amount: "asdfasd" }],
    orderTotal: { title: "2500 ", amount: "sadfsadf" },
  });

  const { auth } = useSelector((state) => {
    return state.master;
  });

  useEffect(() => {
    const fetch = async () => {
      if (!auth?.id || !billId) {
        return;
      }
      let response = await fetch_bill_data_function(auth?.id, billId);

      if (response) {
        let total = response?.total;
        let orders = response?.customer_tracking?.orders;

        // Total order
        let totalObj = { title: "Total", amount: total };
        setState((state) => {
          return { ...state, orderTotal: totalObj };
        });

        let array = [];
        let totalList = [];

        for (const item of orders) {
          // Date functions
          let obj = {
            product: item?.product_details,
            quantity: item?.quantity,
            total: item?.order_total,
            orderDate: item?.order_date,
            status: item?.status,
          };
          array.push(obj);

          // Order summary
          let tot = {
            title: item?.product_details,
            amount: item?.order_total,
            quantity: item?.quantity,
          };
          totalList?.push(tot);
        }

        // order summay and total -> state update
        setState((state) => {
          return {
            ...state,
            orderTotal: totalObj,
            orderSummary: totalList,
            orders: array,
          };
        });
      }
    };
    fetch();
  }, [billId, auth]);

  return (
    <div className=" w-full h-full  flex flex-col  ">
      <DashboardHeaderTemplate heading="Your Orders Tracking" />
      <div className=" flex-1 overflow-auto">
        <CustomerDashboardTrackingLayout content={state} />
      </div>
    </div>
  );
};

export default CustomerDashboardOrderTracking;

const fetch_bill_data_function = async (cusId, billId) => {
  let url = new URL(
    BaseURL.mainURl + API_ENDPOINTS?.tracking + "/" + cusId + "/" + billId
  );

  let headers = { ...api_headers };

  let response = await GetAPI(url, headers);

  if (response?.ok) {
    let responsedata = await response?.json();
    return responsedata;
  }

  return null;
};
