import React from "react";
import { useDispatch } from "react-redux";
import SelectComponent from "../../BasicUIElements/SelectComponent";
import DatePickerComponents from "../../Components/Utils/DatePickerComponents";

const DateFilterTemplateComponent = ({
  fetch = () => {},
  updateSlice = () => {},
  dateObj = [],
  customDate = "",
  date = {},
}) => {
  const dispatch = useDispatch();

  const updateAnalyticsObj = (obj) => {
    dispatch(
      updateSlice({
        [obj.key]: obj.value,
      })
    );

    fetch(null, { ...date, [obj.key]: obj.value });
    // fetchAnalytics({ startDate, endDate, filter });
  };
  return (
    <div className=" w-full flex flex-row justify-between pe-[2rem] pt-[8px] mb-[2rem]">
      <div></div>
      <div className={` w-fit h-fit flex flex-col gap-[0.5rem]  `}>
        <SelectComponent
          optionarr={dateObj}
          value={date.filter}
          className=" w-[200px] h-[40px]"
          updateFunction={(e) => {
            updateAnalyticsObj({ key: "filter", value: e });
          }}
        />

        <div
          className={` w-[200px] h-[45px] flex-shrink-0 flex flex-row  justify-between gap-[0.25rem] ${
            customDate === date.filter.content ? " flex" : " hidden"
          } `}
        >
          <DatePickerComponents
            title="Start Date"
            className=" relative  border-2 w-[90px] h-[52px] rounded-[8px] bg-[#F6F7FB] flex flex-row justify-center gap-[1rem]"
            value={date.startDate}
            isLogoPresent={false}
            onChange={(e) => {
              updateAnalyticsObj({
                key: "startDate",
                value: new Date(e.target.value),
              });
            }}
          />
          <DatePickerComponents
            title="End Date"
            className=" relative  border-2 w-[90px] h-[52px] rounded-[8px] bg-[#F6F7FB] flex flex-row justify-center gap-[1rem]"
            value={date.endDate}
            isLogoPresent={false}
            onChange={(e) => {
              updateAnalyticsObj({
                key: "endDate",
                value: new Date(e.target.value),
              });
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default DateFilterTemplateComponent;
