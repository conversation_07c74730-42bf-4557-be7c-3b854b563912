import React from "react";

let list_text = (list = []) => {
  if (list?.length === 0) return "-";

  if (list?.length === 1) return list[0];

  let str = list[0];

  for (let i = 1; i < list?.length; i++) {
    str = str + ", " + list[i];
  }

  return str;
};

const TableList_array_template = ({ list = [], className = "" }) => {
  let str = list_text(list);
  return (
    <div
      className={`${className} h-full flex flex-row flex-wrap overflow-auto pt-1 pb-1 font-[400] font-inter text-[14px] text-[#6E7079]`}
    >
      {str}
    </div>
  );
};

export default TableList_array_template;
