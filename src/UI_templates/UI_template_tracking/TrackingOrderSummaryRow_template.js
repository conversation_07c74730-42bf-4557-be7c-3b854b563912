import React from "react";

const TrackingOrderSummaryRow_template = ({
  content = { title: "", qty: "", amount: "" },
  isQuantity = true,
}) => {
  return (
    <div className=" max-w-[450px] w-full justify-between flex flex-row  md:text-[18px] text-[14px]">
      <div className=" flex flex-row gap-[1rem]">
        <div className=" w-[180px] h-fit  font-[400] font-poppins  text-[#475467] ">
          {content?.title}
        </div>

        {isQuantity && (
          <div className=" w-[75px] h-fit font-[400] font-poppins  text-[#475467]  ">
            X{content?.quantity}
          </div>
        )}
      </div>

      <div className=" font-[700] font-poppins text-[#475467]  ">
        {content?.amount}
      </div>
    </div>
  );
};

export default TrackingOrderSummaryRow_template;
