import React from "react";
import TrackingDivisor_Template from "./TrackingDivisor_Template";
import TrackingOrderSummaryRow_template from "./TrackingOrderSummaryRow_template";

const TrackingOrderSummary_template = ({ content }) => {
  // content = {
  //     totalList : [{ title: "", qty: "", amount: "" }]
  //     total = { title: "", amount: "" }
  // }
  return (
    <div className=" w-full h-fit mb-[3rem] md:ps-8 ps-4">
      <h1 className=" text-[#344054] font-poppins md:text-[18px] text-[14px] font-[500] mb-4">
        Order Summary
      </h1>

      <div className=" w-full flex flex-col gap-[1rem]">
        {content?.totalList?.map((item) => {
          return <TrackingOrderSummaryRow_template content={item} />;
        })}

        <TrackingDivisor_Template />

        <TrackingOrderSummaryRow_template
          content={content?.total}
          isQuantity={false}
        />
      </div>
    </div>
  );
};

export default TrackingOrderSummary_template;
