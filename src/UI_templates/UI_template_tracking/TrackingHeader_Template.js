import React from "react";
import { Link } from "react-router-dom";
import ButtonComponent from "../../BasicUIElements/ButtonComponent";

const TrackingHeader_Template = ({ content = {} }) => {
  // content = { billId : id of billid , customerId : customerid  }
  return (
    <div className=" w-full h-fit md:pt-[2.5rem] pt-[1.25rem] ">
      <div className=" w-full h-fit flex md:flex-row flex-col justify-between md:items-center items-start md:gap-0 gap-[1rem] md:ps-4 md:pe-4 ps-0 pe-0">
        <h1 className=" text-[#344054] font-poppins md:text-[18px] text-[14px] font-[600]">
          Order ID: {content?.billId}
        </h1>
        <div className=" flex flex-row gap-[0.5rem] md:text-[18px] text-[14px]">
          <h2 className=" font-poppins font-[400] text-[#344054]">
            Customer ID
          </h2>
          <p>:</p>
          <Link className=" text-[#019BA2] underline-offset-4">
            {content?.customerId}
          </Link>
        </div>
        <div>
          <ButtonComponent className=" w-[145px] h-[55px] rounded-[8px] font-[600] font-inter md:text-[20px] text-[16px] text-[#667085] border-2 border-[#667085]">
            Invoice
          </ButtonComponent>
        </div>
      </div>

      <div className=" w-full mt-[2rem] md:ps-4 flex flex-col gap-[1rem]">
        <h1 className=" text-[#344054] font-poppins text-[18px] font-[500]">
          Delivery Address
        </h1>

        {/* <div className=" flex flex-col gap-[0.5rem]">
                       <p className=" font-[400] font-inter text-[18px] text-[#667085]">
                            No. 15 Henry Street, Yaba,
                       </p>
                       <p className=" font-[400] font-inter text-[18px] text-[#667085]">
                            Bengaluru 56002
                       </p>
                  </div> */}
      </div>
    </div>
  );
};

export default TrackingHeader_Template;
