import React from "react";

const countLineMark = (array) => {
  let count = [];

  for (let i = 1; i < array.length; i++) {
    if (array[i]?.isCompleted) {
      count.push(true);
    } else {
      return count;
    }
  }

  return count;
};

const TrackingOrderDate_Template = ({ content = [] }) => {
  let countMark = countLineMark(content);
  let hh = 100 / (content.length - 1);
  let lineLength = hh;

  return (
    <div className="w-full h-fit flex flex-row justify-center ">
      <div className="sm:w-[80%] w-[100%] flex flex-row justify-between relative">
        <div className=" w-full flex flex-row justify-between z-[11]">
          {/* Date  */}
          {content?.map((item) => {
            const title = item?.title;
            const date = item?.date;
            let isCompleted = item?.isCompleted;
            return (
              <div className=" flex flex-col justify-center items-center gap-[1rem]">
                <h1
                  className={`font-inter font-[500]  ${
                    isCompleted ? "text-[#12B76A]" : "text-[#D0D5DD]"
                  } lg:text-[20px] md:text-[16px] sm:text-[10px] text-[8px]`}
                >
                  {title}
                </h1>
                <div
                  className={` w-[30px] h-[30px] rounded-[50%] ${
                    isCompleted ? " bg-[#12B76A]" : "bg-[#D0D5DD]"
                  } `}
                ></div>
                <h1 className=" font-inter font-[400] lg:text-[20px] md:text-[16px] sm:text-[10px] text-[8px] text-[#667085]">
                  {date}
                </h1>
              </div>
            );
          })}
        </div>

        <div className=" w-[87%] h-[10px] flex flex-row justify-start absolute  top-[50%] left-[50%] -translate-x-[50%] bg-[#D0D5DD] -translate-y-[50%] z-[10]">
          {countMark?.map((item) => {
            return (
              <div
                style={{
                  width: lineLength + "%",
                }}
                className={`  h-full bg-[#12B76A]`}
              ></div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
export default TrackingOrderDate_Template;
