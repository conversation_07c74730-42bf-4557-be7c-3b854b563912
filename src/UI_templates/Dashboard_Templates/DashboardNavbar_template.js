import React from "react";
import UseRouterAuthentication from "../../Hooks/UseRouterAuthentication";
import { routes } from "../../Config/routesConfig";
import { NavLink } from "react-router-dom";

// subpage array obj
// { title: "Walkin", path: routes.purchaseAnalytics.directLink }

const DashboardNavbar_template = ({
  listLink = routes?.purchase?.directLink,
  insightsLink = routes.purchaseAnalytics.directLink,
  dashboard = "purchase",
  subpages = [],
}) => {
  const { routeAuthentication } = UseRouterAuthentication();

  return (
    <div className=" w-full h-[44px] border-b-2 border-[#EBEBEB]">
      <div className=" w-fit h-[44px] flex flex-row justify-start flex-shrink-0 ">
        <NavLink
          to={listLink}
          end
          className={({ isActive }) =>
            `w-[94px] h-full font-poppins font-[400] text-[16px] flex flex-row justify-center items-center flex-shrink-0 ${
              isActive
                ? "  text-[#00BD94] border-b-[3px] border-[#00BD94] "
                : "text-[#212529] border-b-[3px] border-[#212529]"
            } `
          }
        >
          List
        </NavLink>
        {routeAuthentication(dashboard, "insights") && (
          <NavLink
            to={insightsLink}
            className={({ isActive }) =>
              `w-[94px] h-full font-poppins font-[400] text-[16px] flex flex-row justify-center items-center flex-shrink-0 ${
                isActive
                  ? "  text-[#00BD94] border-b-[3px] border-[#00BD94] "
                  : "text-[#212529] border-b-[3px] border-[#212529]"
              } `
            }
          >
            Insights
          </NavLink>
        )}
        {subpages.map((page) => {
          return (
            <NavLink
              to={page.path}
              className={({ isActive }) =>
                `w-[94px] h-full font-poppins font-[400] text-[16px] flex flex-row justify-center items-center flex-shrink-0 ${
                  isActive
                    ? "  text-[#00BD94] border-b-[3px] border-[#00BD94] "
                    : "text-[#212529] border-b-[3px] border-[#212529]"
                } `
              }
            >
              {page.title}
            </NavLink>
          );
        })}
      </div>
    </div>
  );
};

export default DashboardNavbar_template;
