export const BaseURL = {
  mainURl: "http://**************:3000/api",
  // mainURl: "http://localhost:4000/api",
};

export const API_ENDPOINTS = {
  adminSignin: "/admin/login",
  adminSignup: "/admin/create",
  adminCreateUser: "/admin/create",
  userSignin: "/user/login",
  userSignup: "/user/create",
  userDelete: "/admin/user",
  customerSignin: "/login",
  customerSignup: "/createAccount",
  customerResetPassword: "/restpass",
  resetPassword: "/user/reset-password",

  fetchuserlist: "/admin/users",
  fetchuserlistWithoutAdminToken: "/admin/fetchusers",

  userEditAccess: "/admin/access",

  updateadminaccess: "/admin/access",
  fetchuseraccess: "/user/accesses",

  serviceComplaintAddComplaint: "/user/addcomplaint",
  serviceComplaintListing: "/user/listcomplaints",
  serviceComplaintListingWithFilter: "/user/listcomplaintswithfilter",
  serviceComplaintEdit: "/user/complaint",
  serviceComplaintsHeaderCount: "/user/header",
  serviceComplaintAddFormBuilder: "/user/servicecomplaintsaddformbuilder",
  serviceComplaintsEditFormBuilder: "/user/servicecomplaintseditformbuilder",

  // salesAndMarketingAnalytics: "/leads/salesanalyticswithfilter",
  // salesHeaderCount: "/leads/salesheader",

  salesAddLead: "/saleslead/add", // Sales Dashboard
  salesAndMarketingAnalytics: "/saleslead/salesleadanalytics", // Sales Dashboad
  salesLeadEdit: "/saleslead/update", // Sales dashboard
  salesHeaderCount: "/saleslead/salesleadheader", // sales Dashboard
  salesAndMarketingListFilter: "/saleslead/listfilter", // sales Dashboard
  salesLeadAddFormBuilder: "/saleslead/salesleadaddformbuilder", // Only Sales dashboard
  salesleadEditFormBuilder: "/saleslead/salesleadeditformbuilder", // sales dashboard
  salesleadstableheaderdata: "/saleslead/salesleadtableheaderdata", // Only Sales Dashboard

  salesAndMarketingListing: "/leads/getlead", // Marketing dashboard
  salesAndMarketingAddALead: "/leads/add", // Marketing dashboard
  salesAndMarketingHeaderData: "/leads/header", // Marketing dashboad
  MarketingAnalytics: "/leads/salesanalytics", // Marketing dashboard
  salesAndMarketingAnalyticsFilter: "/leads/salesanalyticswithfilter", // Marketing dashboard
  MarketingListFilter: "/leads/filter", // marketing dashboard
  salesAndMarketingEditLead: "/leads/update", // Marketing dashboard
  salesAndMarketingEdit: "/leads/update", // Marketing dashboard
  leadAddFormBuilder: "/leads/addformbuilder", // Marketing Dashboard
  salesEditFormBuilder: "/leads/editformbuilder", // Marketing dashboard
  salestableheaderData: "/leads/tableheaderdata", // Marketing Dashboard
  getLeadById: "/leads/getleadbyid/", // Added for getting lead by ID

  marketing_walkin_filter_list: "/walkin/listfilter", // Marketing Walkin Dashboard
  marketing_walkin_header_data: "/walkin/header", // Marketing Walkin Dashboard
  marketing_walkin_Add: "/walkin/add", // Marketing Walkin Dashboard
  marketing_walkin_Edit: "/walkin/edit", // Marketing Walkin Dashboard
  marketing_walkin_AddFormBuilder: "", // Marketing Walkin Dashboard
  marketing_walkin_EditFormBuilder: "/walkin/editFormBuilder", // Marketing Walkin Dashboard
  marketing_walkin_LoaderTableheader: "", // Marketing Walkin Dashboard
  marketing_waling_sent_message: "/walkin/wati/walkin", // Marketing walkin dashboard - send message button api
  marketing_walkin_history: "/walkin/revisit", // Marketing walkin dashboard - view history button api
  marketing_walkin_whatsapp_blast: "/walkin/whatsappmag", // Marketing walkin dashboard - WhatsApp blast data

  orderHeadeCount: "/order-purchase/order/header_count", // Order Header count is receiving in order list itself. no need of this end point
  orderListing: "/order-purchase/orders",
  orderFilterList: "/order-purchase/order_filter",
  orderAnalytics: "/aRoutes/order_Analytics",
  orderBrandVsValue: "/order-purchase/brand_value",

  purchaseHeaderCount: "/order-purchase/purchase/header_count",
  purchaseListing: "/order-purchase/purchase-list",
  purchaseFilterList: "/order-purchase/purchase_filter",
  purchaseAnalytics: "/leads/purchaseanalytics",
  purchaseAnalyticsFilter: "/leads/purchaseanalyticswithfilter",
  purchaseAnalyticsBrandVsSize: "/leads/brandvssize",
  purchaseEdit: "/order-purchase/edit",

  // Builder
  purchaseAddFormBuilder: "/order-purchase/purchaseaddformbuilder",
  purchaseEditFormBuilder: "/order-purchase/purchaseeditformbuilder",

  ticketList: "/ticket/list",
  ticketAdd: "/ticket/add",
  ticketListWithFilter: "/ticket/listwithfilter",
  ticketEdit: "/ticket/edit",
  ticketHeaderCount: "/ticket/summary",
  // builder
  ticketsAddFormBuilder: "/ticket/addformbuilder",
  ticketsEditFormBuilder: "/ticket/editformbuilder",

  customerList: "/list",
  customerEdit: "/edit",
  singleCustomerData: "/single_page",
  tracking: "/trackings",

  taskManagementList: "/task/list",
  taskManagementCreate: "/task/create",
  taskManagementUpdate: "/task/update",
  taskManagementFilter: "/task/filter",
  taskManagementAddFormBuilder: "/task/addformbuilder",

  networkingList: "/network/list",
  networkingadd: "/network/add",
  networkingAddFormBuilder: "/network/addFormBuilder",
};

export const defaultHeaders = {
  "Content-Type": "application/json",
};

export const api_headers = {
  "Content-Type": "application/json",
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": ["POST", "GET", "OPTIONS", "DELETE", "PUT"],
  "Access-Control-Allow-Headers": [
    "append",
    "delete",
    "entries",
    "foreach",
    "get",
    "has",
    "keys",
    "set",
    "values",
    "Authorization",
  ],

  redirect: "follow",
};
