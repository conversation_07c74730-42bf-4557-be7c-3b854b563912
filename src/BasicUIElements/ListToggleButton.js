import React from "react";
import ButtonComponent from "./ButtonComponent";

const ListToggleButton = ({ value = false, onClick = () => {} }) => {
     return (
          <ButtonComponent
               className=" w-fit h-fit cursor-pointer "
               onClick={onClick}
               isLoading={false}
          >
               {value ? (
                    <svg
                         width="25"
                         height="25"
                         viewBox="0 0 25 25"
                         fill="none"
                         xmlns="http://www.w3.org/2000/svg"
                    >
                         <path
                              d="M9.62342 10.3766C9.12132 9.87447 8.30725 9.87447 7.80515 10.3766C7.30305 10.8787 7.30305 11.6927 7.80515 12.1949L12.0909 16.4806C12.593 16.9827 13.407 16.9827 13.9091 16.4806L18.1949 12.1949C18.697 11.6927 18.697 10.8787 18.1949 10.3766C17.6927 9.87447 16.8787 9.87447 16.3766 10.3766L13 13.7532L9.62342 10.3766Z"
                              fill="#CFD3D4"
                         />
                         <path
                              fill-rule="evenodd"
                              clip-rule="evenodd"
                              d="M25 13C25 19.6274 19.6274 25 13 25C6.37258 25 1 19.6274 1 13C1 6.37258 6.37258 1 13 1C19.6274 1 25 6.37258 25 13ZM22.4286 13C22.4286 18.2073 18.2073 22.4286 13 22.4286C7.79274 22.4286 3.57143 18.2073 3.57143 13C3.57143 7.79274 7.79274 3.57143 13 3.57143C18.2073 3.57143 22.4286 7.79274 22.4286 13Z"
                              fill="#CFD3D4"
                         />
                    </svg>
               ) : (
                    <svg
                         width="25"
                         height="25"
                         viewBox="0 0 25 25"
                         fill="none"
                         xmlns="http://www.w3.org/2000/svg"
                    >
                         <path
                              d="M10.3766 16.3766C9.87447 16.8787 9.87447 17.6927 10.3766 18.1949C10.8787 18.697 11.6927 18.697 12.1949 18.1949L16.4806 13.9091C16.9827 13.407 16.9827 12.593 16.4806 12.0909L12.1949 7.80515C11.6927 7.30305 10.8787 7.30305 10.3766 7.80515C9.87447 8.30725 9.87447 9.12132 10.3766 9.62342L13.7532 13L10.3766 16.3766Z"
                              fill="#CFD3D4"
                         />
                         <path
                              fill-rule="evenodd"
                              clip-rule="evenodd"
                              d="M13 1C19.6274 1 25 6.37258 25 13C25 19.6274 19.6274 25 13 25C6.37258 25 1 19.6274 1 13C1 6.37258 6.37258 1 13 1ZM13 3.57143C18.2073 3.57143 22.4286 7.79274 22.4286 13C22.4286 18.2073 18.2073 22.4286 13 22.4286C7.79274 22.4286 3.57143 18.2073 3.57143 13C3.57143 7.79274 7.79274 3.57143 13 3.57143Z"
                              fill="#CFD3D4"
                         />
                    </svg>
               )}
          </ButtonComponent>
     );
};

export default ListToggleButton;
