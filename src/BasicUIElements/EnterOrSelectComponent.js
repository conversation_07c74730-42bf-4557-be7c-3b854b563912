import React, { useState } from "react";
import LeftArrowSVG from "../Assest/SVG/LeftArrowSVG";
import ButtonComponent from "./ButtonComponent";

const OptionComponent = ({ click = () => {}, opt }) => {
     const clickHandler = () => {
          click(opt);
     };

     return (
          <>
               <ButtonComponent
                    onClick={clickHandler}
                    isLoading={false}
                    className=" w-full h-[40px] border-0 truncate border-[#000] flex flex-col justify-center text-[12px] ps-2 hover:bg-gray-300   "
               >
                    {opt?.content}
               </ButtonComponent>
          </>
     );
};

const EnterOrSelectComponent = ({
     className = "",
     optionarr = [],
     textClassName = "",
     updateFunction = () => {},
     value = { id: "", content: "" },
}) => {
     // model display none to block
     const [isModelOpen, setIsModeOpen] = useState(false);
     const changeModel = () =>
          setIsModeOpen((state) => {
               return !state;
          });

     const changeHandler = (e) => {
          clickHandler({ id: e.target.value, content: e.target.value });
     };

     const clickHandler = (event) => {
          updateFunction(event);
     };

  

     return (
          <ButtonComponent
               onClick={changeModel}
               className=" w-[375px] h-[52px] border-2 rounded-[8px] relative"
          >
               <input
                    className=" w-full h-full text-[16px] font-poppins p-3 "
                    type="text"
                    placeholder="Customer Id"
                    onChange={changeHandler}
                    value={value.content}
               />

               <div
                    style={{
                         rotate: "270deg",
                    }}
                    className="w-fit h-fit absolute top-1/2 translate-x-1/2 right-3"
               >
                    <div
                         className={` w-fit h-full  ${
                              isModelOpen ? "rotate-180" : "rotate-0"
                         } `}
                    >
                         <LeftArrowSVG />
                    </div>
               </div>

               {isModelOpen && (
                    <div
                         className={
                              " absolute w-[100%] h-fit  z-[200000] max-h-[200px] border-2  top-[105%] left-0 overflow-auto  shadow-selectOptionShadow  bg-[#fff] rounded-[5px] " +
                              textClassName
                         }
                    >
                         {optionarr.map((opt) => {
                              return (
                                   <OptionComponent
                                        click={clickHandler.bind(opt.id)}
                                        opt={opt}
                                   />
                              );
                         })}
                    </div>
               )}
          </ButtonComponent>
     );
};

export default EnterOrSelectComponent;
