import React from "react";

import searchicon from "../Assest/Utils/search.png";

const SearchBar = ({
     className,
     value,
     onChange = () => {},
     isLogoAtFront = false,
}) => {
     return (
          <div className={className}>
               <input
                    type="text"
                    placeholder="Search"
                    className={` text-greyText text-[12px] placeholder:text-[12px] ps-[10%] w-[100%] h-full placeholder:text-greyText rounded-[5px] border-2  border-[#DDD] ${
                         isLogoAtFront ? "ps-[15%]" : "ps-[2%]"
                    }`}
                    value={value}
                    onChange={onChange}
               />
               <div
                    className={` absolute  w-[10%]  h-full flex flex-row justify-center items-center ${
                         isLogoAtFront
                              ? "top-[0px] left-1"
                              : " right-[4px] top-[0px]"
                    } `}
               >
                    <div className=" w-[100%] h-[100%] ">
                         <img
                              alt="search bar"
                              src={searchicon}
                              className=" w-full h-full object-contain"
                         />
                    </div>
               </div>
          </div>
     );
};

export default SearchBar;
