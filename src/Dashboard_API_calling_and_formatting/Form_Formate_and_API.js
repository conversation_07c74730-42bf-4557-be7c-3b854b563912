import { API_ENDPOINTS, api_headers, BaseURL } from "../API/APIConfig";
import { GetAPI } from "../API/GetAPI";

//Date formater
function formatDate(date) {
  const day = String(date?.getDate()).padStart(2, "0"); // Get day and add leading zero if needed
  const month = String(date?.getMonth() + 1).padStart(2, "0"); // Get month and add leading zero (months are 0-indexed)
  const year = date?.getFullYear(); // Get full year

  return `${day}-${month}-${year}`;
}

function parseDate(dateString) {
  // Handle undefined or null values
  if (!dateString) {
    return new Date();
  }
  
  // Split the string into day, month, and year
  const [day, month, year] = dateString.split("-");

  // Create and return a new Date object (Note: Months are 0-indexed in JavaScript Date)
  return new Date(year, month - 1, day);
}

export const formEnginee_Formatee_Structure_and_State = (f) => {
  let structure = [];
  for (let item of f) {
    if (item?.type === "singleselect") {
      let obj = { ...item };

      let options = [];
      if (obj?.options === "userList" || obj?.options === "customerList") {
        options = obj?.options;
      } else {
        options = obj?.options?.map((i) => {
          return {
            id: i,
            content: i,
          };
        });
      }

      obj = {
        ...obj,
        defaultValue: { id: item?.defaultValue, content: item?.defaultValue },
        options,
      };

      structure.push(obj);
    } else if (item?.type === "multipleselect") {
      let obj = item;

      let options = [];
      if (obj?.options === "userList" || obj?.options === "customerList") {
        options = obj?.options;
      } else {
        options = obj?.options?.map((i) => {
          return {
            id: i,
            content: i,
          };
        });
      }

      obj = {
        ...obj,
        defaultValue: item?.defaultValue?.map((i) => {
          return {
            id: i,
            content: i,
          };
        }),
        options,
      };

      structure.push(obj);
    } else if (item?.type === "selectortype") {
      let obj = item;

      let options = [];
      if (obj?.options === "userList" || obj?.options === "customerList") {
        options = obj?.options;
      } else {
        options = obj?.options?.map((i) => {
          return {
            id: i,
            content: i,
          };
        });
      }

      obj = {
        ...obj,
        defaultValue: { id: item?.defaultValue, content: item?.defaultValue },
        options,
      };

      structure.push(obj);
    } else {
      structure.push(item);
    }
  }

  let state = {};
  structure.map((cur) => {
    state = {
      ...state,
      [cur?.key]: {
        value: cur?.defaultValue,
        valid: true,
        // type: cur?.type,
        // change: cur?.change,
      },
    };
  });

  return { structure, state };
};

// Edit form
export const formEnginee_edit_Formatee_Structure_and_State = (f) => {
  let structure = [];
  for (let item of f) {
    if (item?.type === "singleselect") {
      let obj = { ...item };

      let options = [];
      if (obj?.options === "userList" || obj?.options === "customerList") {
        options = obj?.options;
      } else {
        options = obj?.options?.map((i) => {
          return {
            id: i,
            content: i,
          };
        });
      }

      obj = {
        ...obj,
        defaultValue: { id: item?.defaultValue, content: item?.defaultValue },
        options,
      };

      structure.push(obj);
    } else if (item?.type === "multipleselect") {
      let obj = item;

      let options = [];
      if (obj?.options === "userList" || obj?.options === "customerList") {
        options = obj?.options;
      } else {
        options = obj?.options?.map((i) => {
          return {
            id: i,
            content: i,
          };
        });
      }

      obj = {
        ...obj,
        defaultValue: item?.defaultValue?.map((i) => {
          return {
            id: i,
            content: i,
          };
        }),
        options,
      };

      structure.push(obj);
    } else if (item?.type === "selectortype") {
      let obj = item;

      let options = [];
      if (obj?.options === "userList" || obj?.options === "customerList") {
        options = obj?.options;
      } else {
        options = obj?.options?.map((i) => {
          return {
            id: i,
            content: i,
          };
        });
      }

      obj = {
        ...obj,
        defaultValue: { id: item?.defaultValue, content: item?.defaultValue },
        options,
      };

      structure.push(obj);
    } else if (item?.type === "date") {
      let obj = item;
      if (obj?.format === "format") {
        obj = { ...obj, defaultValue: parseDate(obj?.defaultValue) };
      } else {
        obj = { ...obj, defaultValue: new Date(obj?.defaultValue) };
      }
      structure.push(obj);
    } else if (item?.type === "datetime") {
      let obj = item;
      if (obj?.format === "format") {
        obj = { ...obj, defaultValue: parseDate(obj?.defaultValue) };
      } else {
        obj = { ...obj, defaultValue: new Date(obj?.defaultValue) };
      }
      structure.push(obj);
    } else if (item?.type === "multiDate") {
      // For multiDate type, we handle arrays of dates
      let obj = item;
      // Ensure defaultValue is always an array
      if (!obj?.defaultValue || !Array.isArray(obj?.defaultValue)) {
        obj = { ...obj, defaultValue: [] };
      }
      structure.push(obj);
    } else {
      structure.push(item);
    }
  }

  let state = {};
  structure.map((cur) => {
    state = {
      ...state,
      [cur?.key]: { value: cur?.defaultValue, valid: true },
    };
  });

  return { structure, state };
};

// Validator
export const vaildator_Form_builder = (structure, state) => {
  let validate_Obj = {};
  let isFormValid = true;
  for (let current of structure) {
    // text
    if (
      current?.type === "text" ||
      current?.type === "email" ||
      current?.type === "number"
    ) {
      let obj = { ...state[current?.key] };

      // Validation
      if (state[current?.key].value === "") {
        // if the input is empty
        if (current?.required) {
          isFormValid = false;
        }
        obj = { ...obj, valid: false };
      } else {
        obj = { ...obj, valid: true };
      }

      validate_Obj = { ...validate_Obj, [current?.key]: obj };
    }
    // Single Select
    else if (current?.type === "singleselect") {
      let obj = { ...state[current?.key] };

      // Validation
      if (current?.placeholder === obj?.value?.content) {
        // if the selected one is placeholder one
        if (current?.required) {
          isFormValid = false;
        }
        obj = { ...obj, valid: false };
      } else {
        obj = { ...obj, valid: true };
      }
      validate_Obj = { ...validate_Obj, [current?.key]: obj };
    }
    // multiple select
    else if (current?.type === "multipleselect") {
      let obj = { ...state[current?.key] };

      // Validation
      if (obj?.value?.length === 0) {
        // If the value array is empty
        if (current?.required) {
          isFormValid = false;
        }
        obj = { ...obj, valid: false };
      } else {
        obj = { ...obj, valid: true };
      }
      validate_Obj = { ...validate_Obj, [current?.key]: obj };
    }
    // select or type
    else if (current?.type === "selectortype") {
      let obj = { ...state[current?.key] };

      // Validation
      if (current?.placeholder === obj?.value?.content) {
        // if the selected one is placeholder one
        if (current?.required) {
          isFormValid = false;
        }
        obj = { ...obj, valid: false };
      } else {
        obj = { ...obj, valid: true };
      }

      validate_Obj = { ...validate_Obj, [current?.key]: obj };
    }
    // date
    else if (current?.type === "date") {
      let obj = { ...state[current?.key] };

      validate_Obj = { ...validate_Obj, [current?.key]: obj };
    }
    // multiDate
    else if (current?.type === "multiDate") {
      let obj = { ...state[current?.key] };
      
      // Always consider multiDate valid - it's optional
      obj.valid = true;

      validate_Obj = { ...validate_Obj, [current?.key]: obj };
    } else {
      let obj = { ...state[current?.key] };

      validate_Obj = { ...validate_Obj, [current?.key]: obj };
    }
  }

  return { validate_Obj, isFormValid };
};

// Data Parser
export const formData_parser = (structure, state) => {
  let parser_obj = {};

  for (let current of structure) {
    // text
    if (
      current?.type === "text" ||
      current?.type === "email" ||
      current?.type === "number"
    ) {
      parser_obj = {
        ...parser_obj,
        [current?.key]: state[current?.key]?.value,
      };
    } else if (current?.type === "singleselect") {
      if (current?.defaultValue?.id !== state[current?.key]?.value?.id) {
        parser_obj = {
          ...parser_obj,
          [current?.key]: state[current?.key]?.value?.id,
        };
      }
    } else if (current?.type === "multipleselect") {
      parser_obj = {
        ...parser_obj,
        [current?.key]: state[current?.key]?.value?.map((item) => {
          return item?.id;
        }),
      };
    } else if (current?.type === "selectortype") {
      if (current?.defaultValue?.id !== state[current?.key]?.value?.id) {
        parser_obj = {
          ...parser_obj,
          [current?.key]: state[current?.key]?.value?.id,
        };
      }
    } else if (current?.type === "date") {
      if (current?.format === "date") {
        // Date obj
        parser_obj = {
          ...parser_obj,
          [current?.key]: new Date(state[current?.key]?.value),
        };
      } else if (current?.format === "format") {
        // Date -format -> DD-MM-YYYY
        parser_obj = {
          ...parser_obj,
          [current?.key]: formatDate(new Date(state[current?.key]?.value)),
        };
      } else {
        parser_obj = {
          ...parser_obj,
          [current?.key]: state[current?.key]?.value,
        };
      }
    } else if (current?.type === "datetime") {
      if (current?.format === "date") {
        // Date obj
        parser_obj = {
          ...parser_obj,
          [current?.key]: new Date(state[current?.key]?.value),
        };
      } else if (current?.format === "format") {
        // Date -format -> DD-MM-YYYY
        parser_obj = {
          ...parser_obj,
          [current?.key]: formatDate(new Date(state[current?.key]?.value)),
        };
      } else {
        parser_obj = {
          ...parser_obj,
          [current?.key]: state[current?.key]?.value,
        };
      }
    } else if (current?.type === "multiDate") {
      // For multiDate, pass the array directly
      parser_obj = {
        ...parser_obj,
        [current?.key]: state[current?.key]?.value || [],
      };
    } else {
      parser_obj = {
        ...parser_obj,
        [current?.key]: state[current?.key]?.value,
      };
    }
  }

  return { parser_obj };
};
