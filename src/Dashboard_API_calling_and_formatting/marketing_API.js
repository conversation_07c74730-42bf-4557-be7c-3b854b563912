import { API_ENDPOINTS, api_headers, BaseURL } from "../API/APIConfig";
import { GetAPI } from "../API/GetAPI";

export const formatted_marketing = (list = []) => {
  if (!list) {
    return [];
  }

  let array = [];

  for (let i = 0; i < list?.length; i++) {
    let indiv = list[i];
    let obj = {
      id: indiv._id,
      name: indiv?.name,
      email: indiv?.email,
      location: indiv?.location,
      LOCATION: indiv?.location, // Add uppercase version for consistency
      fieldVisitDate: indiv?.field_visit_date,
      mobilephone: indiv?.phone,
      leadStatus: indiv?.lead_status,
      customerId: indiv?.customer_id,
      lastWalkinStatus: indiv?.last_visisted,
      walkinStatus: indiv?.walkin_status,
      siteState: indiv?.site_state,
      salesRep: indiv?.salesRep,

      archName: indiv?.arch_name,
      archNumber: indiv?.arch_number,
      engineerName: indiv?.engineer_name,
      engineerNumber: indiv?.engineer_number,
      keyPersonName: indiv?.key_person_name,
      keyPersonNumber: indiv?.key_person_number,

      concern: indiv?.concern,
      brand: indiv?.brand,

      purpose: indiv?.purpose,
      reference: indiv?.reference,
      siteLocation: indiv?.site_location,
      noOfBathRoom: indiv?.no_of_bathrooms,

      leadType: indiv?.lead_type,
      leadSource: indiv?.lead_source,
      contactNo1: indiv?.CONTACT_NO_1,
      contactNo2: indiv?.CONTACT_NO_2,
      contactNo3: indiv?.CONTACT_NO_3,
      contactNo4: indiv?.CONTACT_NO_4,
      leadToCompany: indiv?.LEAD_TO_COMPANY,
      leadSalesPerson: indiv?.LEAD_SALES_PERSON,
      city: indiv?.CITY,
      area: indiv?.AREA,
      landmark: indiv?.LANDMARK,
      archFirmName: indiv?.ARCH_FIRM_NAME,
      plumberName: indiv?.PLUMBER_NAME,
      plumberNumber: indiv?.PLUMBER_NUMBER,
      siteStage: indiv?.SITE_STAGE,
      areaRequirement: indiv?.AREA_REQUIREMENT,
      brandToPromote: indiv?.BRAND_TO_PROMOTE,
      leadPersonRemarks: indiv?.LEAD_PERSON_REMARKS,
      followUpDate1: indiv?.FOLLOW_UP_DATE_1,
      followUpDate2: indiv?.FOLLOW_UP_DATE_2,
      telecallingRemarks: indiv?.telecallingRemarks,
      revisitDate: indiv?.RE_VISIT_DATE,
    };
    array.push(obj);
    obj = {};
  }

  return array;
};

// FETCHING SALES LIST
export const fetch_marketing_list = async (auth) => {
  let url = new URL(BaseURL.mainURl + API_ENDPOINTS.salesAndMarketingListing);
  url.searchParams.set("limit", 500);

  url.searchParams.set("userId", auth.id);

  let headers = {
    ...api_headers,
    Authorization: auth.token,
  };

  let response = await GetAPI(url, headers);

  if (response?.ok) {
    let data = await response?.json();
    return data;
  } else {
    return null;
  }
};
