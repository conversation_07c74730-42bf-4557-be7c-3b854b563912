import { API_ENDPOINTS, api_headers, BaseURL } from "../API/APIConfig";
import { GetAPI } from "../API/GetAPI";

// ADD FORM Builder
export const fetch_serviceComplaint_add_form_enginee = async () => {
  let url = new URL(
    BaseURL?.mainURl + API_ENDPOINTS?.serviceComplaintAddFormBuilder
  );

  let headers = { ...api_headers };

  let response = await GetAPI(url, headers);

  if (response?.ok) {
    let responseData = await response?.json();

    return responseData?.structure;
  }

  return [
    {
      type: "singleselect",
      defaultValue: "Type of Request",
      key: "type_of_request",
      options: ["General", "Order Related", "Hello world"],
      placeholder: "Type of Request",
    },

    {
      type: "text",
      key: "order_id",
      defaultValue: "",
      placeholder: "Order Id",
    },

    {
      type: "selectortype",
      defaultValue: "Customer Id",
      key: "customer_id",
      options: "customerList",
      placeholder: "Customer Id",
    },
    {
      type: "date",
      key: "order_date",
      defaultValue: new Date(),
      title: "Order Date",
      format: "format",
    },
    {
      type: "text",
      key: "product_details",
      defaultValue: "",
      placeholder: "Product Details",
    },
    {
      type: "text",
      key: "batch_no",
      defaultValue: "",
      placeholder: "Batch No",
    },
    {
      type: "text",
      key: "quantity",
      defaultValue: "",
      placeholder: "Quantity",
    },
    {
      type: "text",
      key: "complaint",
      defaultValue: "",
      placeholder: "Complaint",
    },

    {
      type: "singleselect",
      defaultValue: "Attending Person",
      key: "attending_person",
      options: "userList",
      placeholder: "Attending Person",
    },
    {
      type: "singleselect",
      defaultValue: "Complaint Status",
      key: "complaint_status",
      options: ["Resolved", "In Progress", "New"],
      placeholder: "Complaint Status",
    },
  ];
};
