import { API_ENDPOINTS, api_headers, BaseURL } from "../API/APIConfig";
import { GetAPI } from "../API/GetAPI";

// PURCHASE -> FETCH (GET) HEADER COUNT
export const api_purchase_headercount = async () => {
     const url = new URL(BaseURL.mainURl + API_ENDPOINTS.purchaseHeaderCount);

     let header = {
          ...api_headers,
     };

     const response = await GetAPI(url, header);

     if (response?.ok) {
          const responseData = await response.json();

          let data = [
               { title: "All Products", count: responseData?.totalCount },
               { title: "Available", count: responseData?.Available },
               { title: "Against Order", count: responseData?.Against_Order },
               { title: "B", count: responseData?.B },
               { title: "C", count: responseData?.C },
               {
                    title: "Purchase Order",
                    count: responseData?.purchaseOrderCountResult[0]
                         .purchaseOrderCount,
               },
          ];
          return data;
     } else {
          return [];
     }
};
