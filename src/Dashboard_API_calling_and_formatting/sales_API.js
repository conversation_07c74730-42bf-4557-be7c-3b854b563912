import { API_ENDPOINTS, api_headers, BaseURL } from "../API/APIConfig";
import { GetAPI } from "../API/GetAPI";
import { PostAPI } from "../API/PostAPI";
import { LeadStatus_Qualified } from "../Config/SalesAndMarketingTableConfig";

export const formatted_sales = (list = []) => {
  if (!list) {
    return [];
  }

  let array = [];

  for (let i = 0; i < list?.length; i++) {
    let indiv = list[i];
    let obj = {
      id: indiv._id,
      name: indiv?.name,
      email: indiv?.email,
      location: indiv?.location,
      fieldVisitDate: indiv?.field_visit_date,
      mobilephone: indiv?.phone,
      leadStatus: indiv?.lead_status,
      customerId: indiv?.customer_id,
      lastWalkinStatus: indiv?.last_visisted,
      walkinStatus: indiv?.walkin_status,
      siteState: indiv?.site_state,
      salesRep: indiv?.salesRep,

      archName: indiv?.arch_name,
      archNumber: indiv?.arch_number,
      engineerName: indiv?.engineer_name,
      engineerNumber: indiv?.engineer_number,
      keyPersonName: indiv?.key_person_name,
      keyPersonNumber: indiv?.key_person_number,

      concern: indiv?.concern,
      brand: indiv?.brand,

      purpose: indiv?.purpose,
      reference: indiv?.reference,
      siteLocation: indiv?.site_location,
      noOfBathRoom: indiv?.no_of_bathrooms,

      leadType: indiv?.lead_type,
      leadSource: indiv?.lead_source,
      contactNo1: indiv?.CONTACT_NO_1,
      contactNo2: indiv?.CONTACT_NO_2,
      contactNo3: indiv?.CONTACT_NO_3,
      contactNo4: indiv?.CONTACT_NO_4,
      leadToCompany: indiv?.LEAD_TO_COMPANY,
      leadSalesPerson: indiv?.LEAD_SALES_PERSON,
      city: indiv?.CITY,
      area: indiv?.AREA,
      landmark: indiv?.LANDMARK,
      archFirmName: indiv?.ARCH_FIRM_NAME,
      plumberName: indiv?.PLUMBER_NAME,
      plumberNumber: indiv?.PLUMBER_NUMBER,
      siteStage: indiv?.SITE_STAGE,
      areaRequirement: indiv?.AREA_REQUIREMENT,
      brandToPromote: indiv?.BRAND_TO_PROMOTE,
      leadPersonRemarks: indiv?.LEAD_PERSON_REMARKS,
      followUpDate1: indiv?.FOLLOW_UP_DATE_1,
      followUpDate2: indiv?.FOLLOW_UP_DATE_2,
      telecallingRemarks: indiv?.telecallingRemarks,
      revisitDate: indiv?.RE_VISIT_DATE,
    };

    if (obj?.leadStatus === LeadStatus_Qualified?.id) {
      array.push(obj);
    }
    obj = {};
  }

  return array;
};

// FETCHING SALES LIST
export const fetch_sales_list = async (auth) => {
  let url = new URL(BaseURL.mainURl + API_ENDPOINTS.salesAndMarketingListing);
  url.searchParams.set("limit", 500);

  url.searchParams.set("userId", auth.id);

  let headers = {
    ...api_headers,
    Authorization: auth.token,
  };

  let response = await GetAPI(url, headers);

  if (response?.ok) {
    let data = await response?.json();
    return data;
  } else {
    return null;
  }
};

export const fetch_sales_list_with_filter = async (filter, auth) => {
  let url = new URL(
    BaseURL?.mainURl + API_ENDPOINTS?.salesAndMarketingListFilter
  );

  url.searchParams.set("userId", auth?.id);

  let headers = { ...api_headers };

  let body = filter;

  let response = await PostAPI(url, body, headers);

  if (response?.ok) {
    let data = await response?.json();
    return data;
  }

  return null;
};

// ADD FORM Builder
export const fetch_sales_add_form_enginee = async () => {
  let url = new URL(BaseURL?.mainURl + API_ENDPOINTS?.salesLeadAddFormBuilder);

  let headers = { ...api_headers };

  let response = await GetAPI(url, headers);

  if (response?.ok) {
    let responseData = await response?.json();
    // console.log(responseData);
    return responseData?.structure;
  }
  // return salesandmarketing_addForm;

  return [];
};

export const fetch_marketing_header_count = async (auth = {}) => {
  if (!auth?.id) {
    return;
  }
  let url = new URL(
    BaseURL?.mainURl + API_ENDPOINTS?.salesAndMarketingHeaderData
  );

  url.searchParams.set("userId", auth?.id);

  let header = {
    ...api_headers,
  };

  let response = await GetAPI(url, header);

  if (response?.ok) {
    let responseDate = await response?.json();

    return responseDate;
  }

  return null;
};

//edit form
export const fetch_sales_edit_form_enginee = async (id) => {
  let url = new URL(BaseURL?.mainURl + API_ENDPOINTS?.salesEditFormBuilder);

  let headers = { ...api_headers };

  let body = {
    id: id,
  };

  let response = await PostAPI(url, body, headers);

  if (response?.ok) {
    let responseData = await response?.json();

    return responseData?.structure;
  }
  // return salesandmarketing_addForm;

  return [];
};

//edit form
export const fetch_sales_lead_edit_form_enginee = async (id) => {
  let url = new URL(BaseURL?.mainURl + API_ENDPOINTS?.salesleadEditFormBuilder);

  let headers = { ...api_headers };

  let body = {
    id: id,
  };

  let response = await PostAPI(url, body, headers);

  if (response?.ok) {
    let responseData = await response?.json();

    return responseData?.structure;
  }
  // return salesandmarketing_addForm;

  return [];
};
