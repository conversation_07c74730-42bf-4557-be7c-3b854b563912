import { API_ENDPOINTS, api_headers, BaseURL } from "../API/APIConfig";
import { GetAPI } from "../API/GetAPI";

//FETCH HEADER DATA
export const fetch_order_headercount_and_list = async () => {
  let url = new URL(BaseURL.mainURl + API_ENDPOINTS.orderListing);

  let header = {
    ...api_headers,
  };

  const response = await GetAPI(url, header);

  if (response?.ok) {
    let responseData = await response?.json();

    // Header Data

    let headerData = [
      { title: "Total", count: responseData?.Total },
      { title: "Full Orders", count: responseData?.full_orders },
      { title: "Partial Orders", count: responseData?.partial_orders },
      {
        title: "No of line item",
        count: responseData?.No_of_line_item,
      },
      {
        title: "Value of the line item",
        count: responseData?.value_of_the_line_item,
      },
      { title: "Available", count: responseData?.Available },
      { title: "Unavailable", count: responseData?.Unavailable },
      { title: "A", count: responseData?.A },
      { title: "AO", count: responseData?.AO },
    ];

    // let headerData = responseData?.headerData;

    // LIST
    let list = [];

    return {
      headerData,
      list,
    };
  } else {
  }
};
