import { API_ENDPOINTS, api_headers, BaseURL } from "../API/APIConfig";
import { GetAPI } from "../API/GetAPI";

export const fetch_taskManagement_add_form = async () => {
  let url = new URL(
    BaseURL?.mainURl + API_ENDPOINTS?.taskManagementAddFormBuilder
  );

  let headers = { ...api_headers };

  let response = await GetAPI(url, headers);

  if (response?.ok) {
    let responseData = await response?.json();

    return responseData?.structure;
  }

  return [
    {
      type: "text",
      required: true,
      key: "Task_Description",
      defaultValue: "",
      placeholder: "Task Description",
    },
    {
      type: "singleselect",
      defaultValue: "Project",
      placeholder: "Project",
      required: true,
      key: "Project",
      options: [
        "Dceramic",
        "DNSP",
        "Dharan Bath Fitting",
        "Dharan Bath Care",
        "Dharan Rock",
      ],
    },
    {
      type: "singleselect",
      defaultValue: "Department",
      placeholder: "Department",
      required: true,
      key: "Department",
      options: ["purchase", "sales", "order"],
    },

    {
      type: "multipleselect",
      defaultValue: [],
      required: true,
      key: "Assignee",
      options: "userList",
      title: "Assignee",
    },
    {
      type: "date",
      defaultValue: new Date(),
      required: true,
      key: "Dead_line_date",
      title: "Due Date",
      format: "date",
    },
  ];
};
