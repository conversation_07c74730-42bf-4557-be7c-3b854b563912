import React from "react";
import IndivHeaderDataEnginee from "./IndivHeaderDataEnginee";

const HeaderDataEnginee = ({ headerData = [], eachPerLine = 6 }) => {
  return (
    <div
      className={` w-full h-fit gap-y-[1rem] grid  `}
      style={{
        gridTemplateColumns: `repeat(${eachPerLine}, minmax(0, 1fr))`,
      }}
    >
      {headerData?.map((item) => {
        return (
          <IndivHeaderDataEnginee
            heading={item?.title}
            stat={item?.count ? item?.count : "0"}
          />
        );
      })}
      <div></div>
    </div>
  );
};

export default HeaderDataEnginee;
