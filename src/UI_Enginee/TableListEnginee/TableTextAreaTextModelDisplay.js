import React, { useEffect, useRef, useState } from "react";
import ButtonComponent from "../../BasicUIElements/ButtonComponent";
import ModelComponents from "../../BasicUIElements/ModelComponents";
import ModelHeaderAndRoute from "../../Components/Utils/UtilsComponents/ModelHeaderAndRoute";
import { HiXMark } from "react-icons/hi2";

const TableTextAreaTextModelDisplay = ({
  children = "",
  size = "",
  title = "",
  className = " font-[400] font-inter text-[14px] text-[#6E7079]",
}) => {
  const [isHovered, setIsHovered] = useState();
  const [isModelOpen, setIsModelOpen] = useState();
  const textareaRef = useRef(null);

  const closeModal = () => {
    setIsModelOpen(false);
  };

  const adjustHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = "auto"; // Reset height to calculate new height
      textarea.style.height = `${textarea.scrollHeight}px`; // Set new height
    }
  };

  return (
    <>
      <div
        onMouseEnter={() => {
          setIsHovered(true);
        }}
        onMouseLeave={() => {
          setIsHovered(false);
        }}
        className={
          size +
          " relative font-inter text-[14px] text-[#6E7079] pt-1 pb-1 pe-3"
        }
      >
        <div className=" h-full overflow-hidden ">
          <p className={className}>{children}</p>
        </div>

        {isHovered && (
          <ButtonComponent
            onClick={() => {
              setIsModelOpen(true);
              setTimeout(() => {
                adjustHeight();
              }, 1);
            }}
            className=" absolute right-0 top-3 hover:bg-gray-300 duration-300  bg-gray-200 ps-2 pe-2 pt-1 pb-1 rounded-[3px] text-gray-500 bg-opacity-75 font-inter text-[8px]   "
          >
            OPEN
          </ButtonComponent>
        )}
      </div>

      {isModelOpen && (
        <ModelComponents>
          <div className=" w-[424px] max-h-[95vh] h-fit overflow-y-auto flex flex-col gap-[0rem] bg-[#fff] rounded-[6px] p-4 z-[150]">
            <div
              className="w-full h-fit flex flex-row justify-between ps-4 pe-4"
              onClick={closeModal}
            >
              <h1 className="  font-poppins font-[400] text-[16px]">{title}</h1>
              <ButtonComponent
                onClick={closeModal}
                className=" w-[24px] h-[24px] rounded-[6px] bg-[#DBF0F2] flex flex-row justify-center items-center"
              >
                <HiXMark size={"20"} />
              </ButtonComponent>
            </div>
            <div className=" w-full h-fit flex flex-col items-center gap-[1rem] pt-[1rem]">
              <textarea
                value={children}
                ref={textareaRef}
                className=" w-[90%] overflow-auto focus:outline-none text-[12px] font-poppins"
              ></textarea>
            </div>
            <div className=" w-full h-fit">
              <div className=" w-full h-fit flex flex-row justify-center gap-[1rem] mt-[1rem]">
                <ButtonComponent
                  onClick={closeModal}
                  className=" w-[90%] h-[42px] rounded-[6px] border-[#00BD94] hover:text-[16px] duration-200 border-[1px] bg-clip-text text-transparent bg-mainLinearGradient md:text-[14px] text-[12px] font-inter font-[500] flex flex-row justify-center items-center"
                >
                  Close
                </ButtonComponent>
              </div>
            </div>
          </div>
        </ModelComponents>
      )}
    </>
  );
};

export default TableTextAreaTextModelDisplay;
