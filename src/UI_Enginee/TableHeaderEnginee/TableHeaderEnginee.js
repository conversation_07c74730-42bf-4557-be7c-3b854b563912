import React from "react";
import TableHeaderEngineeDisplay from "./TableHeaderEngineeDisplay";
import TableHeaderEngineeFilterWithSearch from "./TableHeaderEngineeFilterWithSearch";

const TableHeaderEnginee = ({
  table = [],
  state = {},
  updateState = () => {},
}) => {
  return (
    <div className="w-fit h-[52px] flex flex-row sticky top-0 bg-white z-[1000]">
      {table.map((item) => {
        return (
          <div key={item.key} className="w-fit h-[52px] border-b-2 flex flex-row">
            {item?.headertype === "display" && (
              <TableHeaderEngineeDisplay className={item?.className}>
                {item?.title}
              </TableHeaderEngineeDisplay>
            )}

            {item?.headertype === "filter" && (
              <TableHeaderEngineeFilterWithSearch
                content={item}
                state={state[item?.key]}
                updateState={updateState}
              />
            )}
            {item?.headertype === "filterwithsearch" && (
              <TableHeaderEngineeFilterWithSearch
                content={item}
                state={state[item?.key]}
                updateState={updateState}
              />
            )}
          </div>
        );
      })}
    </div>
  );
};

export default TableHeaderEnginee;

export const table_header_state_update = (tableheader, existing_state) => {
  let state = {};
  for (let item of tableheader) {
    if (item?.headertype === "filter") {
      if (existing_state[item?.key]) {
        state[item?.key] = existing_state[item?.key];
      } else {
        state[item?.key] = [];
      }
    }
  }

  return { state: state };
};

export const parser_table_filter_data = (tableheader, filterstate) => {
  let state = {};
  let isFilter = false;
  for (let item of tableheader) {
    if (
      item?.headertype === "filter" ||
      item?.headertype === "filterwithsearch"
    ) {
      if (filterstate[item?.key] && filterstate[item?.key]?.length !== 0) {
        state[item?.key] = filterstate[item?.key]?.map((item) => item?.id);
      }

      if (state[item?.key]?.length > 0) {
        isFilter = true;
      }
    }
  }

  return {
    state,
    isFilter,
  };
};
