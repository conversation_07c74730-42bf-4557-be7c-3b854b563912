import React, { useState } from "react";
import sort from "../../Assest/Utils/sort.png";
import ButtonComponent from "../../BasicUIElements/ButtonComponent";
import { TiTick } from "react-icons/ti";
import UseUserData from "../../Hooks/UseUserData";
import UseCustomerData from "../../Hooks/UseCustomerData";

const OptionComponent = ({
  text,
  isSelected = () => {},
  update = () => {},
}) => {
  let isSelect = isSelected(text);
  const onclick = () => {
    update(text, isSelect);
  };

  return (
    <ButtonComponent
      onClick={onclick}
      className=" w-full h-[40px] flex flex-row ps-4 pe-4 items-center border-b-2 hover:bg-gray-300 duration-150 overflow-auto "
    >
      <div className=" w-[25px] h-full flex flex-row justify-center flex-shrink-0 items-center ">
        {isSelect && <TiTick size={20} />}
      </div>
      <div className=" flex-1 ">
        <div className=" w-full h-full flex flex-row ps-3  text-[#2B2F32] font-inter text-[12px] truncate">
          {text?.content}
        </div>
      </div>
    </ButtonComponent>
  );
};

const TableHeaderEngineeFilter = ({
  content = {},
  state = [],
  updateState = () => {},
}) => {
  const [isModelOpen, setIsModeOpen] = useState(false);

  const isSelected = (currentObj) => {
    for (let item of state) {
      if (item?.id === currentObj?.id) {
        return true;
      }
    }
    return false;
  };

  const update = (currentObj, isSelect) => {
    let array = [];
    if (isSelect) {
      array = state.filter((item) => item?.id !== currentObj?.id);
    } else {
      array = [...state, currentObj];
    }
    updateState(content?.key, array);
    setIsModeOpen((state) => {
      return !state;
    });
  };

  // Options
  const { userList } = UseUserData();
  const customerList = UseCustomerData().customerList;
  let options = content?.options;
  if (content?.options === "userList") {
    options = userList;
  } else if (content?.options === "customerList") {
    options = customerList;
  }

  return (
    <div
      className={`${content?.className} flex flex-row justify-start items-center`}
    >
      {/* Filter Component */}
      <ButtonComponent
        onClick={() => {
          setIsModeOpen((state) => {
            return !state;
          });
        }}
        className=" relative w-[95%] h-[35px] border-2 rounded-[8px] flex flex-row items-center justify-between ps-2 pe-2"
      >
        <h1 className="font-inter font-[400] text-[14px] text-[#2C2D33]">
          {content?.title}
        </h1>

        <div className=" w-[24px] h-[24px] flex flex-row justify-center items-center ">
          <img
            src={sort}
            className=" w-full h-full object-contain"
            alt="logo in table header"
          />
        </div>

        {/* Modal */}
        {isModelOpen && (
          <div className=" z-[500] absolute w-full h-fit max-h-[200px] left-0 top-full border-2 bg-white overflow-y-auto">
            {options?.map((current) => {
              return (
                <OptionComponent
                  text={current}
                  isSelected={isSelected}
                  update={update}
                />
              );
            })}
          </div>
        )}
      </ButtonComponent>
    </div>
  );
};

export default TableHeaderEngineeFilter;
