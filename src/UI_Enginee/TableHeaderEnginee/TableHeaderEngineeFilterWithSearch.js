import React, { useRef, useState } from "react";
import sort from "../../Assest/Utils/sort.png";
import ButtonComponent from "../../BasicUIElements/ButtonComponent";
import { TiTick } from "react-icons/ti";
import UseUserData from "../../Hooks/UseUserData";
import UseCustomerData from "../../Hooks/UseCustomerData";
import LeftArrowSVG from "../../Assest/SVG/LeftArrowSVG";
import { current } from "@reduxjs/toolkit";

function isSubstring(str1, str2) {
  // Check if str1 is present in str2
  return str2
    ?.toString()
    ?.toLowerCase()
    ?.includes(str1?.toString()?.toLowerCase());
}

const OptionComponent = ({
  text,
  isSelected = () => {},
  update = () => {},
}) => {
  let isSelect = isSelected(text);
  const onclick = () => {
    update(text, isSelect);
  };

  return (
    <ButtonComponent
      onClick={onclick}
      className=" w-full h-[40px] flex flex-row ps-4 pe-4 items-center border-b-2 hover:bg-gray-300 duration-150 overflow-auto "
    >
      <div className=" w-[25px] h-full flex flex-row justify-center flex-shrink-0 items-center ">
        {isSelect && <TiTick size={20} />}
      </div>
      <div className=" flex-1 ">
        <div className=" w-full h-full flex flex-row ps-3  text-[#2B2F32] font-inter text-[12px] truncate">
          {text?.content}
        </div>
      </div>
    </ButtonComponent>
  );
};

const TableHeaderEngineeFilterWithSearch = ({
  content = {},
  state = [],
  updateState = () => {},
}) => {
  const inputRef = useRef();
  const [inputValue, setInputValue] = useState(content?.title);
  const [isModelOpen, setIsModeOpen] = useState(false);

  const isSelected = (currentObj) => {
    for (let item of state) {
      if (item?.id === currentObj?.id) {
        return true;
      }
    }
    return false;
  };

  const update = (currentObj, isSelect) => {
    let array = [];
    if (isSelect) {
      array = state.filter((item) => item?.id !== currentObj?.id);
    } else {
      array = [...state, currentObj];
    }
    updateState(content?.key, array);
    setIsModeOpen((state) => {
      return !state;
    });
  };

  // Options
  const { userList } = UseUserData();
  const customerList = UseCustomerData().customerList;
  let options = content?.options;
  if (content?.options === "userList") {
    options = userList;
    options = options.filter((item) => {
      return isSubstring(inputValue, item?.content);
    });
    if (options.length === 0) {
      options = userList;
    }
  } else if (content?.options === "customerList") {
    options = customerList;
    options = options.filter((item) => {
      return isSubstring(inputValue, item?.content);
    });
    if (options.length === 0) {
      options = customerList;
    }
  } else {
    options = options.filter((item) => {
      return isSubstring(inputValue, item?.content);
    });
    if (options.length === 0) {
      options = content?.options;
    }
  }

  // change handler
  const changeHandler = (e) => {
    setInputValue(e.target.value);
    setIsModeOpen((state) => {
      return true;
    });
  };

  // handleFocus
  const handleFocus = () => {
    if (inputRef.current) {
      inputRef.current.select();
    }
    setIsModeOpen((state) => {
      return true;
    });
  };

  const onblur = () => {
    // setInputValue(content?.title);
  };

  return (
    <div
      className={`${content?.className} flex flex-row justify-start items-center`}
    >
      {/* Filter Component */}
      <ButtonComponent className=" relative w-[95%] h-[35px] border-2 rounded-[8px] flex flex-row items-center justify-between ">
        {/* Input Text */}
        <input
          className=" w-full h-full font-inter font-[400] rounded-[8px] text-[14px] text-[#2C2D33] ps-2 "
          type="text"
          ref={inputRef}
          onFocus={handleFocus}
          onChange={changeHandler}
          onBlur={onblur}
          placeholder={content?.title}
          value={inputValue}
        />

        {/* Left */}
        <ButtonComponent
          onClick={() => {
            setIsModeOpen((state) => {
              return !state;
            });
          }}
          className="w-fit h-fit absolute top-1/2  -translate-y-1/2 right-3"
        >
          <div
            style={{
              rotate: "270deg",
            }}
            className={` w-fit h-full  ${
              isModelOpen ? "rotate-180" : "rotate-0"
            } `}
          >
            <LeftArrowSVG />
          </div>
        </ButtonComponent>

        {/* Modal */}
        {isModelOpen && (
          <div className=" z-[500] absolute w-full h-fit max-h-[200px] left-0 top-full border-2 bg-white overflow-y-auto">
            {options?.map((current) => {
              return (
                <OptionComponent
                  text={current}
                  isSelected={isSelected}
                  update={update}
                />
              );
            })}
          </div>
        )}
      </ButtonComponent>
    </div>
  );
};

export default TableHeaderEngineeFilterWithSearch;
