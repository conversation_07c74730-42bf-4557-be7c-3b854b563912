import React, { useRef, useState } from "react";
import ButtonComponent from "../../BasicUIElements/ButtonComponent";
import LeftArrowSVG from "../../Assest/SVG/LeftArrowSVG";
import UseUserData from "../../Hooks/UseUserData";
import UseCustomerData from "../../Hooks/UseCustomerData";
import MandatoryStarFormEnginee from "./UtilsComponents/MandatoryStarFormEnginee";

function isSubstring(str1, str2) {
  // Check if str1 is present in str2
  return str2
    ?.toString()
    ?.toLowerCase()
    ?.includes(str1?.toString()?.toLowerCase());
}

const SingleSelectFormEnginee = ({
  state = {},
  formData = {},
  updateFunction = () => {},
}) => {
  const inputRef = useRef(null);
  const [inputValue, setInputValue] = useState("");
  // model display none to block
  const [isModelOpen, setIsModeOpen] = useState(false);

  const update = (updateValue) => {
    if (updateValue?.id === state?.value?.id) {
      return;
    }
    updateFunction(formData?.key, updateValue, formData);
    setInputValue("");
    setIsModeOpen((state) => {
      return false;
    });
  };

  const { userList, getUsernameWithid } = UseUserData();
  const customerList = UseCustomerData().customerList;
  let options = formData?.options;
  if (formData?.options === "userList") {
    options = userList;
    options = options.filter((item) => {
      return isSubstring(inputValue, item?.content);
    });
    if (options.length === 0) {
      options = userList;
    }
  } else if (formData?.options === "customerList") {
    options = customerList;
    options = options.filter((item) => {
      return isSubstring(inputValue, item?.content);
    });
    if (options.length === 0) {
      options = customerList;
    }
  } else {
    options = options.filter((item) => {
      return isSubstring(inputValue, item?.content);
    });
    if (options.length === 0) {
      options = formData?.options;
    }
  }

  // console.log(formData, formData?.required);

  const handleFocus = () => {
    if (inputRef.current) {
      inputRef.current.select();
    }
  };

  // change handler
  const changeHandler = (e) => {
    setInputValue(e.target.value);
    setIsModeOpen(true);
  };

  return (
    <div className=" w-[95%] flex flex-col items-center relative">
      {formData?.name && (
        <div className=" w-full h-fit text-[10px] font-poppins text-[black] ">
          {formData?.name}
        </div>
      )}
      <ButtonComponent
        // onClick={() => {
        //   setIsModeOpen((state) => {
        //     return !state;
        //   });
        // }}
        className=" relative w-full h-[52px] border-[rgb(110,80,80)]  rounded-[6px] border border-opacity-25  ps-5 pe-5 flex flex-row justify-between"
      >
        {/* Text  */}
        <ButtonComponent
          onClick={() => {
            setIsModeOpen((state) => {
              setTimeout((state) => {
                if (!state) {
                  inputRef?.current?.focus();
                }
              }, 1);
              return !state;
            });
          }}
          className=" flex-1  h-full"
        >
          <h1 className=" w-full h-full flex flex-row justify-start items-center text-[16px] font-poppins text-[#53545C] ">
            {getUsernameWithid(state.value?.content)}
          </h1>
        </ButtonComponent>

        {/* Left  */}
        <ButtonComponent
          onClick={() => {
            setIsModeOpen((state) => {
              setTimeout((state) => {
                if (!state) {
                  inputRef?.current?.focus();
                }
              }, 1);
              return !state;
            });
          }}
          className="w-fit h-full flex flex-col justify-center"
        >
          <div
            className={` w-fit h-fit  ${
              isModelOpen ? "rotate-90" : " rotate-[270deg]"
            } `}
          >
            <LeftArrowSVG />
          </div>
        </ButtonComponent>

        {/* Modal */}
        {isModelOpen && (
          <div className=" z-[500] absolute w-full h-fit bg-white max-h-[200px] left-0 top-full border-2 overflow-y-auto rounded-b-2xl">
            <div className=" w-full h-[50px] flex flex-row justify-center items-center">
              <div className=" w-[95%] h-[35px]">
                <input
                  className=" w-full h-full text-[16px] font-poppins p-3 border-2 border-gray-300 rounded-[8px]"
                  type="text"
                  placeholder={"Search..."}
                  ref={inputRef}
                  onFocus={handleFocus}
                  onChange={changeHandler}
                  value={inputValue}
                />
              </div>
            </div>
            {options.map((current) => {
              return (
                <ButtonComponent
                  onClick={() => {
                    update(current);
                  }}
                  className=" w-full h-[30px] flex flex-row justify-start items-center  font-poppins ps-5 bg-white text-[12px] text-opacity-90 font-semibold hover:bg-gray-300  duration-150"
                >
                  {current?.content}
                </ButtonComponent>
              );
            })}
          </div>
        )}
      </ButtonComponent>

      {!state?.valid && formData?.required && (
        <div className=" w-full h-fit ps-2">
          <p className="text-[10px] text-[#AB1917] font-[500] font-poppins">
            *select any option
          </p>
        </div>
      )}

      {/* mandatory */}
      {formData?.required && <MandatoryStarFormEnginee />}
    </div>
  );
};

export default SingleSelectFormEnginee;
