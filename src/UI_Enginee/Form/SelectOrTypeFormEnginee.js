import React, { useRef, useState } from "react";
import ButtonComponent from "../../BasicUIElements/ButtonComponent";
import LeftArrowSVG from "../../Assest/SVG/LeftArrowSVG";
import UseUserData from "../../Hooks/UseUserData";
import UseCustomerData from "../../Hooks/UseCustomerData";
import MandatoryStarFormEnginee from "./UtilsComponents/MandatoryStarFormEnginee";

function isSubstring(str1, str2) {
  // Check if str1 is present in str2
  return str2
    ?.toString()
    ?.toLowerCase()
    ?.includes(str1?.toString()?.toLowerCase());
}

const SelectOrTypeFormEnginee = ({
  updateFunction = () => {},
  state = {},
  formData = { options: "customerList" },
}) => {
  const inputRef = useRef(null);
  //Options Data
  const userList = UseUserData().userList;
  const customerList = UseCustomerData().customerList;

  // Integrating The options
  let options = formData?.options;
  if (formData?.options === "userList") {
    options = userList;
    options = options.filter((item) => {
      return isSubstring(state?.value?.content, item?.content);
    });
    // if (options.length === 0) {
    //   options = userList;
    // }
  } else if (formData?.options === "customerList") {
    options = customerList;
    options = options.filter((item) => {
      return isSubstring(state?.value?.content, item?.content);
    });
    // if (options.length === 0) {
    //   options = customerList;
    // }
  } else {
    if (state?.value?.id !== formData?.placeholder) {
      options = options.filter((item) => {
        return isSubstring(state?.value?.content, item?.content);
      });
      // if (options.length === 0) {
      //   options = formData?.options;
      // }
    }
  }

  // model display none to block
  const [isModelOpen, setIsModeOpen] = useState(false);
  const changeModel = () =>
    setIsModeOpen((state) => {
      return !state;
    });

  const changeHandler = (e) => {
    updateFunction(formData.key, {
      id: e.target.value,
      content: e.target.value,
    });
    setIsModeOpen(true);
  };

  const clickHandler = (event) => {
    updateFunction(formData?.key, event, formData);
  };

  const handleFocus = () => {
    if (inputRef.current) {
      inputRef.current.select();
    }
  };

  return (
    <div className=" w-[95%] flex flex-col items-center relative">
      {formData?.name && (
        <div className=" w-full h-fit text-[10px] font-poppins text-[black] ">
          {formData?.name}
        </div>
      )}
      <div className=" w-full h-fit ps-2">
        <p className=" text-[9px]">
          Pick from the list, or if others, enter your answer
        </p>
      </div>
      <ButtonComponent
        onClick={changeModel}
        className=" w-full h-[52px] border-2 rounded-[8px] relative"
      >
        {/* Input Text */}
        <input
          className=" w-full h-full text-[16px] font-poppins p-3 rounded-[8px]"
          type="text"
          placeholder={formData?.placeholder}
          ref={inputRef}
          onFocus={handleFocus}
          onChange={changeHandler}
          value={state?.value?.content}
        />

        {/* Left */}
        <div
          style={{
            rotate: "270deg",
          }}
          className="w-fit h-fit absolute top-1/2 translate-x-1/2 right-3"
        >
          <div
            className={` w-fit h-full  ${
              isModelOpen ? "rotate-180" : "rotate-0"
            } `}
          >
            <LeftArrowSVG />
          </div>
        </div>

        {/* Modal */}
        {isModelOpen && (
          <div
            className={
              " absolute w-[100%] h-fit  z-[500] max-h-[170px] border-2  top-[105%] left-0 overflow-auto  shadow-selectOptionShadow  bg-[#fff] rounded-[5px] "
            }
          >
            {/* Options */}
            {options?.map((opt) => {
              return (
                <ButtonComponent
                  onClick={() => {
                    clickHandler(opt);
                  }}
                  isLoading={false}
                  className=" w-full h-[30px] border-0 truncate border-[#000] flex flex-col justify-center font-poppins ps-5 bg-white text-[12px] text-opacity-90 font-semibold hover:bg-gray-300  duration-150 "
                >
                  {opt?.content}
                </ButtonComponent>
              );
            })}
          </div>
        )}
      </ButtonComponent>

      {!state?.valid && formData?.required && (
        <div className=" w-full h-fit ps-2">
          <p className="text-[10px] text-[#AB1917] font-[500] font-poppins">
            *select any option or enter the text
          </p>
        </div>
      )}

      {/* mandatory */}
      {formData?.required && <MandatoryStarFormEnginee />}
    </div>
  );
};

export default SelectOrTypeFormEnginee;
