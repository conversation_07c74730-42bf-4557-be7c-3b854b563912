import React, { useRef, useState } from "react";
import ButtonComponent from "../../BasicUIElements/ButtonComponent";
import { FaXmark } from "react-icons/fa6";
import MandatoryStarFormEnginee from "./UtilsComponents/MandatoryStarFormEnginee";
import { FaPlus } from "react-icons/fa";

const DateDisplay = ({ date, onRemove, index }) => {
  const formatDate = (date) => {
    const d = new Date(date);
    let dateText = d.getDate();
    if (dateText < 10) {
      dateText = "0" + dateText;
    }
    let monthText = d.getMonth() + 1;
    if (monthText < 10) {
      monthText = "0" + monthText;
    }
    let year = d.getFullYear();
    return `${dateText}/${monthText}/${year}`;
  };

  return (
    <div className="w-full flex justify-between items-center p-2 mb-2 bg-gray-50 border rounded-md">
      <div className="flex items-center">
        <div className="text-sm font-medium text-gray-700">Visit {index + 1}:</div>
        <div className="ml-2 text-sm">{formatDate(date.date)}</div>
      </div>
      <ButtonComponent
        onClick={() => onRemove(date.date)}
        className="w-6 h-6 flex items-center justify-center rounded-full hover:bg-gray-200"
      >
        <FaXmark className="text-gray-500" />
      </ButtonComponent>
    </div>
  );
};

const MultiDatePickerSimpleFormEnginee = ({
  state = {},
  formData = {},
  updateFunction = () => {},
}) => {
  const dateInputRef = useRef();

  const dateElementClick = () => {
    dateInputRef.current.showPicker();
  };

  const handleDateSelect = (e) => {
    const newDateStr = e.target.value;
    const currentDates = state.value || [];
    
    // Check if date already exists
    const exists = currentDates.some(
      (dateObj) => dateObj.date === newDateStr
    );
    
    if (!exists) {
      const newDateObj = { date: newDateStr };
      const updatedDates = [...currentDates, newDateObj];
      updateFunction(formData?.key, updatedDates, formData);
    }
  };

  const handleRemoveDate = (dateToRemove) => {
    const currentDates = state.value || [];
    const updatedDates = currentDates.filter(
      (dateObj) => dateObj.date !== dateToRemove
    );
    updateFunction(formData?.key, updatedDates, formData);
  };

  return (
    <div className="w-[95%] flex flex-col items-center relative">
      {formData?.name && (
        <div className="w-full h-fit text-[10px] font-poppins text-[black]">
          {formData?.name}
        </div>
      )}

      <div className="w-full border-2 rounded-[6px] p-3 bg-[#F6F7FB]">
        <div className="flex justify-between items-center mb-3">
          <h1 className="font-inter font-[400] text-[12px] text-[#5E6366]">
            {formData?.title}
          </h1>
          <ButtonComponent
            onClick={dateElementClick}
            className="flex items-center justify-center w-8 h-8 rounded-full bg-green-100 hover:bg-green-200"
          >
            <FaPlus className="text-green-600" />
          </ButtonComponent>
        </div>

        {/* Display selected dates without remarks */}
        <div className="w-full">
          {(state.value || []).length > 0 ? (
            state.value.map((dateObj, index) => (
              <DateDisplay 
                key={dateObj.date}
                date={dateObj}
                index={index}
                onRemove={handleRemoveDate}
              />
            ))
          ) : (
            <div className="text-gray-500 text-sm">No revisit dates selected</div>
          )}
        </div>

        <input
          ref={dateInputRef}
          type="date"
          onChange={handleDateSelect}
          className="absolute opacity-0"
        />
      </div>

      {!state?.valid && formData?.required && (
        <div className="w-full h-fit ps-2">
          <p className="text-[10px] text-[#AB1917] font-[500] font-poppins">
            *select at least one date
          </p>
        </div>
      )}

      {/* mandatory */}
      {formData?.required && <MandatoryStarFormEnginee />}
    </div>
  );
};

export default MultiDatePickerSimpleFormEnginee;
