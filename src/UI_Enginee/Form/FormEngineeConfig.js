export const vaildator_Form_function = (structure, state) => {
  let validate_Obj = {};
  let isFormValid = true;
  for (let current of structure) {
    // text
    if (
      current?.type === "text" ||
      current?.type === "email" ||
      current?.type === "number"
    ) {
      let obj = { ...state[current?.key] };

      // Validation
      if (state[current?.key].value === "" && current?.required) {
        // if the input is empty
        isFormValid = false;
        obj = { ...obj, valid: false };
      } else {
        obj = { ...obj, valid: true };
      }

      validate_Obj = { ...validate_Obj, [current?.key]: obj };
    }
    // text area
    else if (current?.type === "textarea") {
      let obj = { ...state[current?.key] };

      // Validation
      if (state[current?.key].value === "" && current?.required) {
        // if the input is empty
        isFormValid = false;
        obj = { ...obj, valid: false };
      } else {
        obj = { ...obj, valid: true };
      }

      validate_Obj = { ...validate_Obj, [current?.key]: obj };
    }
    // Single Select
    else if (current?.type === "singleselect") {
      let obj = { ...state[current?.key] };

      // Validation
      if (current?.placeholder === obj?.value?.content && current?.required) {
        // if the selected one is placeholder one
        isFormValid = false;
        obj = { ...obj, valid: false };
      } else {
        obj = { ...obj, valid: true };
      }
      validate_Obj = { ...validate_Obj, [current?.key]: obj };
    }
    // multiple select
    else if (current?.type === "multipleselect") {
      let obj = { ...state[current?.key] };

      // Validation
      if (obj?.value?.length === 0 && current?.required) {
        // If the value array is empty
        isFormValid = false;
        obj = { ...obj, valid: false };
      } else {
        obj = { ...obj, valid: true };
      }
      validate_Obj = { ...validate_Obj, [current?.key]: obj };
    }
    // select or type
    else if (current?.type === "selectortype") {
      let obj = { ...state[current?.key] };

      // Validation
      if (current?.placeholder === obj?.value?.content && current?.required) {
        // if the selected one is placeholder one
        isFormValid = false;
        obj = { ...obj, valid: false };
      } else {
        obj = { ...obj, valid: true };
      }

      validate_Obj = { ...validate_Obj, [current?.key]: obj };
    }
    // date
    else if (current?.type === "date") {
      let obj = { ...state[current?.key] };

      validate_Obj = { ...validate_Obj, [current?.key]: obj };
    }
    // date time
    else if (current?.type === "datetime") {
      let obj = { ...state[current?.key] };

      validate_Obj = { ...validate_Obj, [current?.key]: obj };
    }
    // multiDate
    else if (current?.type === "multiDate") {
      let obj = { ...state[current?.key] };
      
      // Always valid, even if empty
      obj = { ...obj, valid: true };
      
      validate_Obj = { ...validate_Obj, [current?.key]: obj };
    } else {
      let obj = { ...state[current?.key] };

      validate_Obj = { ...validate_Obj, [current?.key]: obj };
    }
  }

  return { validate_Obj, isFormValid };
};

// Change other Input Fields

export const change_other_fields = (value, formData) => {
  let state = {};
  let structure = {};

  for (let current of formData?.change) {
    // --------------------
    if (formData?.type === "singleselect") {
      if (value?.id === current?.value) {
        // --------------------------------------
        for (let item of current?.effect) {
          state = {
            ...state,
            [item?.key]: {
              ...item?.state,
            },
          };
          structure = {
            ...structure,
            [item?.key]: {
              ...item?.structure,
            },
          };
        }
        // --------------------------------------
      }
      // --------------------
    } else if (formData?.type === "multiselect") {
    } else if (formData.type === "date") {
    } else if (formData.type === "datetime") {
    } else {
    }
    //----------------------------
  }

  let changeState = {};
  let changeStructure = {};

  // State
  for (let current_obj in state) {
    // --------------------
    if (formData?.type === "singleselect") {
      // --------------------
      if (state[current_obj]?.value) {
        // -------------------------------------
        changeState = {
          ...changeState,
          [current_obj]: {
            ...state[changeState],
            value: {
              id: state[current_obj]?.value,
              content: state[current_obj]?.value,
            },
          },
        };
        // --------------------------------------
      }
      // --------------------
    } else if (formData?.type === "multiselect") {
    } else if (formData.type === "date") {
    } else {
    }
    //----------------------------
  }

  // Strucute
  for (let current_obj in structure) {
    // --------------------
    if (formData?.type === "singleselect") {
      // --------------------
      if (structure[current_obj]?.options?.length > 0) {
        // // -------------------------------------

        let options = structure[current_obj]?.options?.map((item) => {
          return { id: item, content: item };
        });

        changeStructure = {
          ...changeStructure,
          [current_obj]: {
            options: options,
          },
        };
        // // --------------------------------------
      }
      // --------------------
    } else if (formData?.type === "multiselect") {
    } else if (formData.type === "date") {
    } else {
    }
    //----------------------------
  }

  return { state: changeState, structure: changeStructure };
};
