import React, { useRef, useState } from "react";
import ButtonComponent from "../../BasicUIElements/ButtonComponent";
import LeftArrowSVG from "../../Assest/SVG/LeftArrowSVG";
import { FaXmark } from "react-icons/fa6";
import CheckBoxComponent from "../../BasicUIElements/CheckBoxComponent";
import UseUserData from "../../Hooks/UseUserData";
import UseCustomerData from "../../Hooks/UseCustomerData";
import MandatoryStarFormEnginee from "./UtilsComponents/MandatoryStarFormEnginee";

function isSubstring(str1, str2) {
  // Check if str1 is present in str2
  return str2
    ?.toString()
    ?.toLowerCase()
    ?.includes(str1?.toString()?.toLowerCase());
}

const SelectedComponent = ({ value = "", update = () => {} }) => {
  const { getUsernameWithid } = UseUserData();
  return (
    <div className=" w-fit h-[30px] flex flex-row items-center rounded-[8px] gap-[1rem] ps-1 pe-1 border-2 text-[#5E6366] text-[16px] z-[100] ">
      <div> {getUsernameWithid(value?.content)}</div>
      <div className=" w-[20px] h-full flex flex-col justify-center ">
        <ButtonComponent
          onClick={() => {
            update(value, true);
          }}
          className=" w-[18px] h-[18px] rounded-[9px] flex flex-row justify-center items-center  border-[#ABAFB1]"
        >
          <FaXmark />
        </ButtonComponent>
      </div>
    </div>
  );
};

const OptionComponent = ({
  text,
  isSelected = () => {},
  update = () => {},
}) => {
  let isSelect = isSelected(text);
  const onclick = () => {
    update(text, isSelect);
  };
  return (
    <ButtonComponent
      onClick={onclick}
      className=" w-full h-[40px] flex flex-row ps-4 pe-4 items-center border-b-2 hover:bg-gray-300 duration-150 "
    >
      <CheckBoxComponent value={isSelect} />
      <div className=" flex-1 ">
        <div className=" w-full h-full flex flex-row ps-3  text-[#2B2F32] font-inter text-[16px]">
          {text?.content}
        </div>
      </div>
    </ButtonComponent>
  );
};

const MultiSelectFormEnginee = ({
  state = {},
  formData = {},
  updateFunction = () => {},
}) => {
  const inputRef = useRef(null);
  const [inputValue, setInputValue] = useState("");
  //Options Data
  const { userList } = UseUserData();
  const customerList = UseCustomerData().customerList;

  // Integrating The options
  let options = formData?.options;
  if (formData?.options === "userList") {
    options = userList;
    options = options.filter((item) => {
      return isSubstring(inputValue, item?.content);
    });
    if (options.length === 0) {
      options = userList;
    }
  } else if (formData?.options === "customerList") {
    options = customerList;
    options = options.filter((item) => {
      return isSubstring(inputValue, item?.content);
    });
    if (options.length === 0) {
      options = customerList;
    }
  } else {
    options = options.filter((item) => {
      return isSubstring(inputValue, item?.content);
    });
    if (options.length === 0) {
      options = formData?.options;
    }
  }

  const [isModelOpen, setIsModeOpen] = useState(false);

  const isSelected = (text) => {
    for (let item of state.value) {
      if (item?.id === text?.id) {
        return true;
      }
    }
    return false;
  };

  const update = (text, isSelect) => {
    setInputValue("");
    let array = state.value;
    if (isSelect) {
      array = array.filter((item) => text?.id !== item?.id);
    } else {
      array = [...array, text];
    }
    updateFunction(formData?.key, array, formData);

    setIsModeOpen((state) => {
      return !state;
    });
  };

  const handleFocus = () => {
    if (inputRef.current) {
      inputRef.current.select();
    }
  };

  // change handler
  const changeHandler = (e) => {
    setInputValue(e.target.value);
  };

  return (
    <div className=" w-[95%] flex flex-col items-center relative">
      {formData?.name && (
        <div className=" w-full h-fit text-[10px] font-poppins text-[black] ">
          {formData?.name}
        </div>
      )}
      <ButtonComponent className=" relative w-full min-h-[52px] border-2 h-fit rounded-[6px] ps-5 pe-5 flex flex-row justify-between ">
        {/* TEXT */}

        <ButtonComponent
          onClick={() => {
            setIsModeOpen((state) => {
              setTimeout((state) => {
                if (!state) {
                  inputRef?.current?.focus();
                }
              }, 1);
              return !state;
            });
          }}
          className=" flex-1 mt-[12.5px]"
        >
          {state.value?.length > 0 ? (
            <div className=" w-full flex flex-row justify-start gap-[1rem]  flex-wrap h-fit pb-[0.5rem]">
              {state.value?.map((item) => {
                return <SelectedComponent value={item} update={update} />;
              })}
            </div>
          ) : (
            <h1 className="text-[16px] mt-[4px] font-poppins flex flex-row text-[#53545C] ">
              {formData?.title}
            </h1>
          )}
        </ButtonComponent>

        {/* Left  */}
        <ButtonComponent
          onClick={() => {
            setIsModeOpen((state) => {
              setTimeout((state) => {
                if (!state) {
                  inputRef?.current?.focus();
                }
              }, 1);
              return !state;
            });
          }}
          className="w-fit h-[52px] flex flex-col justify-center ps-2"
        >
          <div
            className={` w-fit h-fit  ${
              isModelOpen ? "rotate-90" : " rotate-[270deg]"
            } `}
          >
            <LeftArrowSVG />
          </div>
        </ButtonComponent>

        {/* Modal */}
        {isModelOpen && (
          <div className=" z-[500] absolute w-full h-fit max-h-[200px] left-0 top-full border-2 bg-white overflow-y-auto">
            <div className=" w-full h-[50px] flex flex-row justify-center items-center">
              <div className=" w-[95%] h-[35px]">
                <input
                  className=" w-full h-full text-[16px] font-poppins p-3 border-2 border-gray-300 rounded-[8px]"
                  type="text"
                  placeholder={"Search..."}
                  ref={inputRef}
                  onFocus={handleFocus}
                  onChange={changeHandler}
                  value={inputValue}
                />
              </div>
            </div>
            {options?.map((current) => {
              return (
                <OptionComponent
                  text={current}
                  isSelected={isSelected}
                  update={update}
                />
              );
            })}
          </div>
        )}
      </ButtonComponent>
      {!state?.valid && formData?.required && (
        <div className=" w-full h-fit ps-2">
          <p className="text-[10px] text-[#AB1917] font-[500] font-poppins">
            *select atleast one option
          </p>
        </div>
      )}

      {/* mandatory */}
      {formData?.required && <MandatoryStarFormEnginee />}
    </div>
  );
};

export default MultiSelectFormEnginee;
