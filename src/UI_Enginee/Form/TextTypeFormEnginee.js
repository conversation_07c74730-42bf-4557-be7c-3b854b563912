import React from "react";
import MandatoryStarFormEnginee from "./UtilsComponents/MandatoryStarFormEnginee";

const TextTypeFormEnginee = ({
  state = {},
  formData = {},
  updateFunction = () => {},
}) => {
  return (
    <div className=" w-[95%] flex flex-col items-center relative">
      {formData?.name && (
        <div className=" w-full h-fit text-[10px] font-poppins text-[black] ">
          {formData?.name}
        </div>
      )}

      <input
        value={state?.value}
        type={formData?.type}
        placeholder={formData?.placeholder}
        onChange={(event) => {
          updateFunction(formData?.key, event.target.value, formData);
        }}
        className=" w-full h-[52px] rounded-[6px] ps-5  border border-[#D8E1F2] text-[16px] font-poppins text-[#53545C] placeholder:font-poppins placeholder:text-[16px] placeholder:font-[400] placeholder:text-[#ABAFB1]"
      />

      {!state?.valid && formData?.required && (
        <div className=" w-full h-fit ps-2">
          <p className="text-[10px] text-[#AB1917] font-[500] font-poppins">
            *This field is required
          </p>
        </div>
      )}

      {/* mandatory */}
      {formData?.required && <MandatoryStarFormEnginee />}
    </div>
  );
};

export default TextTypeFormEnginee;
