import { useDispatch, useSelector } from "react-redux";
import { SalesAndMarketingSliceActions } from "../Store/SalesAndMarketingSlice/SalesAndMarketingSlice";

import { parser_table_filter_data } from "../UI_Enginee/TableHeaderEnginee/TableHeaderEnginee";
import { GetAPI } from "../API/GetAPI";
import { API_ENDPOINTS, api_headers, BaseURL } from "../API/APIConfig";
import { getStartAndEndDate } from "../Config/DateFilterConfig";
import { marketing_headerdata_thunk } from "../Store/MarketingSlice/MarketingSlices";
import { PostAPI } from "../API/PostAPI";

let headerdata = {
  OWNER_NAME: {
    headertype: "display",
  },
  CONTACT_NO_1: {
    headertype: "display",
  },
  CONTACT_NO_2: {
    headertype: "display",
  },
  CONTACT_NO_3: {
    headertype: "display",
  },
  CONTACT_NO_4: {
    headertype: "display",
  },

  DATE_OF_VISIT: {
    headertype: "display",
  },

  VISITED_BY: {
    headertype: "filterwithsearch",
    options: "userList",
  },

  LEAD_STATUS: {
    headertype: "filter",
    options: [
      "NEW LEAD",
      "QUOTE SENT",
      "IN FOLLOW",
      "DROPPED",
      "WALK IN",
      "FIELD VISIT",
    ],
  },
  LEAD_TO_COMPANY: {
    headertype: "filter",
    options: ["DNSP", "DBF", "DC", "DUS"],
  },

  LEAD_SALES_PERSON: {
    headertype: "filterwithsearch",
    options: "userList",
  },

  LEAD_TYPE: {
    headertype: "filter",
    options: [
      "INDIVIDUAL RESIDENT",
      "RESIDENT",
      "APARTMENT",
      "BUNGALOW",
      "COMMERCIAL",
      "INDIVIDUAL",
      "PROJECT",
    ],
  },

  CITY: {
    headertype: "filter",
    options: [
      "Ambur",
      "Arakkonam",
      "Ariyalur",
      "Chengalpattu",
      "Chennai",
      "Cheyyar",
      "Coimbatore",
      "Cuddalore",
      "Dharmapuri",
      "Dindigul",
      "Erode",
      "Gudiyatham",
      "Kanchipuram",
      "Karaikudi",
      "Karur",
      "Kodaikanal",
      "Madurai",
      "Mayiladuthurai",
      "Mettur",
      "Nagapattinam",
      "Nagercoil",
      "Namakkal",
      "Palani",
      "Perambalur",
      "Pollachi",
      "Pudukkottai",
      "Rajapalayam",
      "Ramanathapuram",
      "Salem",
      "Sivagangai",
      "Sivakasi",
      "Tenkasi",
      "Thanjavur",
      "Thiruvarur",
      "Thoothukudi",
      "Tiruchirappalli",
      "Tirunelveli",
      "Tiruppur",
      "Tiruvannamalai",
      "Udhagamandalam (Ooty)",
      "Vellore",
      "Villupuram",
      "Virudhunagar",
    ],
  },

  AREA: {
    headertype: "display",
  },

  LANDMARK: {
    headertype: "display",
  },
  engineer_name: {
    headertype: "display",
  },

  engineer_number: {
    headertype: "display",
  },

  ARCH_FIRM_NAME: {
    headertype: "display",
  },

  ARCH_NAME: {
    headertype: "display",
  },

  ARCH_NUMBER: {
    headertype: "display",
  },

  PLUMBER_NAME: {
    headertype: "display",
  },
  PLUMBER_NUMBER: {
    headertype: "display",
  },
  KEY_PERSON_NAME: {
    headertype: "display",
  },

  KEY_PERSON_NUMBER: {
    headertype: "display",
  },
  BRAND_TO_PROMOTE: {
    headertype: "filter",
    options: [
      "HANSGROHE",
      "GROHE",
      "JAGUAR",
      "TOTO",
      "DURAVIT",
      "KOHLER",
      "GEBERIT",
      "KAJARIA",
      "SOMANY",
      "NEXION",
      "QUTONE",
      "RAK",
      "HINDWARE",
      "VIEGA",
      "SCHELL",
    ],
  },
  No_of_Bathrooms: {
    headertype: "filter",
    options: [1, 2, 3, 4],
  },

  Tiles_Requirement: {
    headertype: "display",
  },
  LEAD_PERSON_REMARKS: {
    headertype: "display",
  },

  FOLLOW_UP_DATE_1: {
    headertype: "display",
  },
  FOLLOW_UP_DATE_2: {
    headertype: "display",
  },
  TELECALLING_REMARKS: {
    headertype: "display",
  },
  RE_VISIT_DATE: {
    headertype: "display",
  },
};

export let dateObj = [
  {
    id: "Last year",
    content: "Last year",
  },
  {
    id: "Last Month",
    content: "Last Month",
  },
  {
    id: "Last Week",
    content: "Last Week",
  },
  {
    id: "Custom Date",
    content: "Custom Date",
  },
  {
    id: "Till now",
    content: "Till now",
  },
];

const UseSalesAndMarketing = () => {
  const dispatch = useDispatch();
  const { filter, table, analytics } = useSelector((state) => {
    return state.salesAndMarketing;
  });

  const master = useSelector((state) => {
    return state.master;
  });

  let auth = master?.auth;

  const fetch_list = (updated_filter_state = null, updated_date = null) => {
    if (!auth?.id) {
      return;
    }

    let filterObj = parser_table_filter_data(table?.header, filter.state);
    let filterState = filterObj?.state;
    let isFilter = filterObj?.isFilter;

    let customDate = "Custom Date";
    let startDate = filter?.date?.startDate;
    let endDate = filter?.date?.endDate;
    let filter_options = filter?.date?.filter?.content;

    if (updated_filter_state) {
      let filterState_parser = parser_table_filter_data(
        table?.header,
        updated_filter_state
      );
      filterState = filterState_parser?.state;
      isFilter = filterState_parser?.isFilter;
    }

    if (updated_date) {
      startDate = updated_date?.startDate;
      endDate = updated_date?.endDate;
      filter_options = updated_date?.filter?.content;
    }

    if (filter_options === "Last year") {
      let [startD, endD] = getStartAndEndDate(365);
      startDate = new Date(startD);
      endDate = new Date(endD);
    } else if (filter_options === "Last Month") {
      let [startD, endD] = getStartAndEndDate(30);
      startDate = new Date(startD);
      endDate = new Date(endD);
    } else if (filter_options === "Last Week") {
      let [startD, endD] = getStartAndEndDate(7);
      startDate = new Date(startD);
      endDate = new Date(endD);
    } else if (filter_options === "Till now") {
      let [startD, endD] = getStartAndEndDate(3650 + 3650);
      startDate = new Date(startD);
      endDate = new Date(endD);
    } else if (filter_options === "Custom Date") {
    }

    // console.log("----------------------------");
    // console.log(filterState, isFilter);
    // console.log(startDate, "---", endDate, "---", filter_options, filter);

    if (isFilter) {
      dispatch(
        sales_and_marketing_filter_list(filterState, [startDate, endDate], auth)
      );
    } else {
      dispatch(sales_and_marketing_filter_list({}, [startDate, endDate], auth));
    }

    dispatch(sales_header_count(auth));
    dispatch(marketing_headerdata_thunk(auth));
  };

  const fetchList = () => {
    if (!auth?.id) {
      return;
    }
    fetch_list();

    // let filterObj = parser_table_filter_data(table.header, filter.state);

    // if (filterObj?.isFilter) {
    //   dispatch(sales_and_marketing_filter_list(filterObj?.state, auth));
    // } else {
    //   dispatch(sales_and_marketing_list_thunk(auth));
    // }
    // dispatch(sales_header_count(auth));
  };

  const fetchWithNewFilter = (filterObj) => {
    if (!auth?.id) {
      return;
    }

    window.alert(
      "Bug in the sales dashboard filter. Please connect with developer "
    );
    // dispatch(sales_and_marketing_filter_list(filterObj, auth));
  };

  const fetchAnalytics = (obj = null) => {
    if (!auth?.id) {
      return;
    }

    if (obj) {
      let customDate = "Custom Date";
      let startDate = obj?.startDate;
      let endDate = obj?.endDate;
      let filter = obj?.filter?.content;

      if (filter === "Last year") {
        let [startD, endD] = getStartAndEndDate(365);
        startDate = new Date(startD);
        endDate = new Date(endD);
      } else if (filter === "Last Month") {
        let [startD, endD] = getStartAndEndDate(30);
        startDate = new Date(startD);
        endDate = new Date(endD);
      } else if (filter === "Last Week") {
        let [startD, endD] = getStartAndEndDate(7);
        startDate = new Date(startD);
        endDate = new Date(endD);
      } else if (filter === "Till now") {
        let [startD, endD] = getStartAndEndDate(3650 + 3650);
        startDate = new Date(startD);
        endDate = new Date(endD);
      } else if (filter === customDate) {
      }

      dispatch(fetch_analytics_thunk(startDate, endDate));
    } else {
      let customDate = "Custom Date";
      let startDate = analytics?.startDate;
      let endDate = analytics?.endDate;
      let filter = analytics?.filter?.content;

      if (filter === "Last year") {
        let [startD, endD] = getStartAndEndDate(365);
        startDate = new Date(startD);
        endDate = new Date(endD);
      } else if (filter === "Last Month") {
        let [startD, endD] = getStartAndEndDate(30);
        startDate = new Date(startD);
        endDate = new Date(endD);
      } else if (filter === "Last Week") {
        let [startD, endD] = getStartAndEndDate(7);
        startDate = new Date(startD);
        endDate = new Date(endD);
      } else if (filter === "Till now") {
        let [startD, endD] = getStartAndEndDate(3650 + 3650);
        startDate = new Date(startD);
        endDate = new Date(endD);
      } else if (filter === customDate) {
      }

      dispatch(fetch_analytics_thunk(startDate, endDate));
    }
  };

  // table header data load
  const loadTableHeaderData = () => {
    dispatch(loadTableHeaderData_thunk(table?.header));
  };

  return {
    fetchList,
    fetchAnalytics,
    fetchWithNewFilter,
    loadTableHeaderData,
    fetch_list,
  };
};

export default UseSalesAndMarketing;

// List thunk
const sales_and_marketing_list_thunk = (auth) => {
  return async (dispatch) => {
    let url = new URL(BaseURL.mainURl + API_ENDPOINTS.salesAndMarketingListing);
    url.searchParams.set("limit", 500);

    url.searchParams.set("userId", auth.id);

    let headers = { ...api_headers };

    let response = await GetAPI(url, headers);

    if (response?.ok) {
      let responsedata = await response?.json();

      let array = [
        ...responsedata?.data?.assignedBy,
        ...responsedata?.data?.assignedTo,
      ];

      dispatch(
        SalesAndMarketingSliceActions?.updateSalesAndMarketingList(array)
      );
    }
  };
};

// Header Data
export const sales_header_count = (auth) => {
  return async (dispatch) => {
    let url = new URL(BaseURL?.mainURl + API_ENDPOINTS?.salesHeaderCount);

    url.searchParams.set("userId", auth?.id);

    let headers = { ...api_headers };

    let response = await GetAPI(url, headers);

    if (response?.ok) {
      let responsedata = await response?.json();
      dispatch(
        SalesAndMarketingSliceActions?.updateHeaderDataSales({
          Leads: responsedata["Leads Vs Closure"]?.Leads,
          Closure: responsedata["Leads Vs Closure"]?.Closure,
          Achieved: responsedata["Target Vs Achieved"]?.Target,
          Target: responsedata["Target Vs Achieved"]?.Achieved,
        })
      );
    }
  };
};

function formatDateToYYYYMMDD(dateObj) {
  if (!(dateObj instanceof Date)) {
    // throw new Error("Invalid input: Expected a Date object");
    return;
  }
  dateObj = new Date(dateObj);
  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, "0"); // Months are 0-based
  const day = String(dateObj.getDate()).padStart(2, "0");

  return `${year}-${month}-${day}`;
}

// Thunk FIlterl
export const sales_and_marketing_filter_list = (
  filterObj,
  [startDate, endDate],
  auth
) => {
  return async (dispatch) => {
    let url = new URL(
      BaseURL?.mainURl + API_ENDPOINTS?.salesAndMarketingListFilter
    );

    // url.searchParams.set("userId", auth?.id);

    url =
      url.href +
      "?startDate=" +
      formatDateToYYYYMMDD(new Date(startDate)) +
      "&tillDate=" +
      formatDateToYYYYMMDD(new Date(endDate)) +
      "&userId=" +
      auth?.id;

    let headers = { ...api_headers };

    let body = filterObj;

    console.log(url, body);

    let response = await PostAPI(url, body, headers);

    if (response?.ok) {
      let data = await response?.json();
      let array = [...data?.data?.assignedBy, ...data?.data?.assignedTo];

      if (data) {
        dispatch(
          SalesAndMarketingSliceActions?.updateSalesAndMarketingList(array)
        );
      } else {
        dispatch(
          SalesAndMarketingSliceActions?.updateSalesAndMarketingList([])
        );
      }
    }
  };
};

// Algo of header data mapping with fetched header data
export const headerData_mapping_with_header_algo = (
  header,
  fetchedHeaderData
) => {
  let array = [];
  for (let item of header) {
    // copying current obj
    let current = { ...item };

    // initialing the current key _fetched header data
    let currentKey_fetchedHeaderdata = {};
    if (current?.key in fetchedHeaderData) {
      currentKey_fetchedHeaderdata = fetchedHeaderData[current?.key];
    }

    // ----------------------

    current.headertype = currentKey_fetchedHeaderdata?.headertype
      ? currentKey_fetchedHeaderdata?.headertype
      : current?.headertype;

    if (
      currentKey_fetchedHeaderdata?.headertype === "filter" ||
      currentKey_fetchedHeaderdata?.headertype === "filterwithsearch"
    ) {
      // updating options based on fetched data
      if (
        currentKey_fetchedHeaderdata?.options === "userList" ||
        currentKey_fetchedHeaderdata?.options === "customerList"
      ) {
        current.options = currentKey_fetchedHeaderdata?.options;
      } else if (Array?.isArray(currentKey_fetchedHeaderdata?.options)) {
        current.options = currentKey_fetchedHeaderdata?.options?.map((item) => {
          return { id: item, content: item };
        });
      }
    }

    array.push(current);
    current = {};
    currentKey_fetchedHeaderdata = {};
  }

  return {
    loadedHeaderData: array,
  };
};

//load table header data thunk
const loadTableHeaderData_thunk = (header) => {
  return async (dispatch) => {
    let headerdata_fetched = headerdata;

    let url = new URL(
      BaseURL?.mainURl + API_ENDPOINTS?.salesleadstableheaderdata
    );
    let headers = { ...api_headers };
    let response = await GetAPI(url, headers);

    if (response?.ok) {
      let responseData = await response?.json();
      let tableheaderdata = responseData?.tableheaderdata;
      let data = headerData_mapping_with_header_algo(header, tableheaderdata);
      dispatch(
        SalesAndMarketingSliceActions?.updateTableHeader(data?.loadedHeaderData)
      );
    } else {
      let data = headerData_mapping_with_header_algo(
        header,
        headerdata_fetched
      );
      dispatch(
        SalesAndMarketingSliceActions?.updateTableHeader(data?.loadedHeaderData)
      );
    }
  };
};

//Analytics
function formatDateToCustomString(dateObj) {
  // Array of day and month names
  const days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  dateObj = new Date(dateObj);
  // Get day of the week, month, day of the month, and year
  const dayOfWeek = days[dateObj.getDay()];
  const month = months[dateObj.getMonth()];
  const dayOfMonth = String(dateObj.getDate()).padStart(2, "0"); // Add leading zero if needed
  const year = dateObj.getFullYear();

  // Return the formatted string
  return `${dayOfWeek}+${month}+${dayOfMonth}+${year}`;
}

const fetch_analytics_thunk = (startDate, endDate) => {
  return async (dispatch) => {
    let url = new URL(
      BaseURL?.mainURl + API_ENDPOINTS?.salesAndMarketingAnalytics
    );

    url =
      url.href +
      "?startDate=" +
      formatDateToCustomString(new Date(startDate)) +
      "&tillDate=" +
      formatDateToCustomString(new Date(endDate));

    let headers = { ...api_headers };

    let response = await GetAPI(url, headers);

    if (response?.ok) {
      let responseData = await response?.json();

      let targetData = {
        targetAchieved: responseData?.targetData?.totalAchieved,
        pendingTargets: responseData?.targetData?.totalPending,
      };

      let daysLeadClosure =
        responseData?.daysLeadClosure?.length > 0
          ? responseData?.daysLeadClosure[0]?.averageLeadDuration?.toFixed(2)
          : 0;

      let obj = {
        salesPulse: responseData.salesPulse,
        leadSource: responseData?.leadSource,
        leadClosure: {
          leads: responseData?.leadClosure?.map((item) => {
            return item?.leads;
          }),
          closures: responseData?.leadClosure?.map((item) => {
            return item?.closures;
          }),
          date: responseData?.leadClosure?.map((item) => {
            return item?.date;
          }),
        },
        Walk_in_Status: responseData?.companyCount?.Walk_in_Status,

        top5: {
          city: responseData?.top5?.map((item) => {
            return item?._id;
          }),
          count: responseData?.top5?.map((item) => {
            return item?.count;
          }),
        },

        salesTrend: {
          sales: responseData?.salesTrend?.map((item) => {
            return item?._id;
          }),
          count: responseData?.salesTrend?.map((item) => {
            return item?.count;
          }),
        },

        averageOrderValue: responseData?.averageOrderValue,

        targetData: targetData,

        AOVvsDay: {
          date: responseData?.AOVvsDay?.map((item) => item?.date),
          data: responseData?.AOVvsDay?.map((item) => item?.AOV),
        },

        topSalesPerson: {
          name: responseData?.topSalesPerson?.map((item) => item?._id),
          totalQuantitySold: responseData?.topSalesPerson?.map(
            (item) => item?.totalQuantitySold
          ),
          totalOrderValue: responseData?.topSalesPerson?.map(
            (item) => item?.totalOrderValue
          ),
        },

        topSellingProducts: {
          name: responseData?.topSellingProducts?.map((item) => item?._id),
          totalQuantity: responseData?.topSellingProducts?.map(
            (item) => item?.totalQuantity
          ),
        },

        daysLeadClosure: daysLeadClosure,

        TargetsVsAchieved: {
          date: responseData?.TargetsVsAchieved?.map((item) => item?.date),
          POTENTIAL: responseData?.TargetsVsAchieved?.map(
            (item) => item?.POTENTIAL
          ),
          NEW: responseData?.TargetsVsAchieved?.map((item) => item?.NEW),
        },
      };

      dispatch(SalesAndMarketingSliceActions?.updateAnalyticsContent(obj));
    }
  };
};
