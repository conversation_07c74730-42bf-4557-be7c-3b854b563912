import { useDispatch, useSelector } from "react-redux";
import {
  marketing_headerdata_thunk,
  marketingSliceActions,
} from "../Store/MarketingSlice/MarketingSlices";

import { parser_table_filter_data } from "../UI_Enginee/TableHeaderEnginee/TableHeaderEnginee";
import { salesAndMarketingTableConfig } from "../Config/SalesAndMarketingTableConfig";
import { fetch_sales_list_with_filter } from "../Dashboard_API_calling_and_formatting/sales_API";
import { GetAPI } from "../API/GetAPI";
import { API_ENDPOINTS, api_headers, BaseURL } from "../API/APIConfig";
import { getStartAndEndDate } from "../Config/DateFilterConfig";
import {
  headerData_mapping_with_header_algo,
  sales_header_count,
} from "./UseSalesAndMarketing";
import { PostAPI } from "../API/PostAPI";

export let headerdata = {
  OWNER_NAME: {
    headertype: "display",
  },
  CONTACT_NO_1: {
    headertype: "display",
  },
  CONTACT_NO_2: {
    headertype: "display",
  },
  CONTACT_NO_3: {
    headertype: "display",
  },
  CONTACT_NO_4: {
    headertype: "display",
  },

  DATE_OF_VISIT: {
    headertype: "display",
  },

  VISITED_BY: {
    headertype: "filterwithsearch",
    options: "userList",
  },

  LEAD_STATUS: {
    headertype: "filter",
    options: [
      "NEW LEAD",
      "QUOTE SENT",
      "IN FOLLOW",
      "DROPPED",
      "WALK IN",
      "FIELD VISIT",
    ],
  },
  LEAD_TO_COMPANY: {
    headertype: "filter",
    options: ["DNSP", "DBF", "DC", "DUS"],
  },

  LEAD_SALES_PERSON: {
    headertype: "filterwithsearch",
    options: "userList",
  },

  LEAD_TYPE: {
    headertype: "filter",
    options: [
      "INDIVIDUAL RESIDENT",
      "RESIDENT",
      "APARTMENT",
      "BUNGALOW",
      "COMMERCIAL",
      "INDIVIDUAL",
      "PROJECT",
    ],
  },

  CITY: {
    headertype: "filter",
    options: [
      "Ambur",
      "Arakkonam",
      "Ariyalur",
      "Chengalpattu",
      "Chennai",
      "Cheyyar",
      "Coimbatore",
      "Cuddalore",
      "Dharmapuri",
      "Dindigul",
      "Erode",
      "Gudiyatham",
      "Kanchipuram",
      "Karaikudi",
      "Karur",
      "Kodaikanal",
      "Madurai",
      "Mayiladuthurai",
      "Mettur",
      "Nagapattinam",
      "Nagercoil",
      "Namakkal",
      "Palani",
      "Perambalur",
      "Pollachi",
      "Pudukkottai",
      "Rajapalayam",
      "Ramanathapuram",
      "Salem",
      "Sivagangai",
      "Sivakasi",
      "Tenkasi",
      "Thanjavur",
      "Thiruvarur",
      "Thoothukudi",
      "Tiruchirappalli",
      "Tirunelveli",
      "Tiruppur",
      "Tiruvannamalai",
      "Udhagamandalam (Ooty)",
      "Vellore",
      "Villupuram",
      "Virudhunagar",
    ],
  },

  AREA: {
    headertype: "display",
  },

  LANDMARK: {
    headertype: "display",
  },
  location: {
    headertype: "display",
  },
  LOCATION: {
    headertype: "display",
  },
  engineer_name: {
    headertype: "display",
  },

  engineer_number: {
    headertype: "display",
  },

  ARCH_FIRM_NAME: {
    headertype: "display",
  },

  ARCH_NAME: {
    headertype: "display",
  },

  ARCH_NUMBER: {
    headertype: "display",
  },

  PLUMBER_NAME: {
    headertype: "display",
  },
  PLUMBER_NUMBER: {
    headertype: "display",
  },
  KEY_PERSON_NAME: {
    headertype: "display",
  },

  KEY_PERSON_NUMBER: {
    headertype: "display",
  },
  BRAND_TO_PROMOTE: {
    headertype: "filter",
    options: [
      "HANSGROHE",
      "GROHE",
      "JAGUAR",
      "TOTO",
      "DURAVIT",
      "KOHLER",
      "GEBERIT",
      "KAJARIA",
      "SOMANY",
      "NEXION",
      "QUTONE",
      "RAK",
      "HINDWARE",
      "VIEGA",
      "SCHELL",
    ],
  },
  No_of_Bathrooms: {
    headertype: "filter",
    options: [1, 2, 3, 4],
  },

  Tiles_Requirement: {
    headertype: "display",
  },
  LEAD_PERSON_REMARKS: {
    headertype: "display",
  },

  FOLLOW_UP_DATE_1: {
    headertype: "display",
  },
  FOLLOW_UP_DATE_2: {
    headertype: "display",
  },
  TELECALLING_REMARKS: {
    headertype: "display",
  },
  RE_VISIT_DATE: {
    headertype: "display",
  },
  RE_VISIT_REMARKS: {
    headertype: "display",
  },
};

export let dateObj = [
  {
    id: "Last year",
    content: "Last year",
  },
  {
    id: "Last Month",
    content: "Last Month",
  },
  {
    id: "Last Week",
    content: "Last Week",
  },
  {
    id: "Custom Date",
    content: "Custom Date",
  },
  {
    id: "Till now",
    content: "Till now",
  },
];

const UseMarketing = () => {
  const dispatch = useDispatch();
  const { filter, analytics, table } = useSelector((state) => {
    return state.marketing;
  });

  const master = useSelector((state) => {
    return state.master;
  });
  let auth = master?.auth;

  const fetch_list = (updated_filter_state = null, updated_date = null) => {
    if (!auth?.id) {
      return;
    }

    let filterObj = parser_table_filter_data(
      salesAndMarketingTableConfig,
      filter.state
    );
    let filterState = filterObj?.state;
    let isFilter = filterObj?.isFilter;

    let customDate = "Custom Date";
    let startDate = filter?.date?.startDate;
    let endDate = filter?.date?.endDate;
    let filter_options = filter?.date?.filter?.content;

    if (updated_filter_state) {
      let filterState_parser = parser_table_filter_data(
        salesAndMarketingTableConfig,
        updated_filter_state
      );
      filterState = filterState_parser?.state;
      isFilter = filterState_parser?.isFilter;
    }

    if (updated_date) {
      startDate = updated_date?.startDate;
      endDate = updated_date?.endDate;
      filter_options = updated_date?.filter?.content;
    }

    if (filter_options === "Last year") {
      let [startD, endD] = getStartAndEndDate(365);
      startDate = new Date(startD);
      endDate = new Date(endD);
    } else if (filter_options === "Last Month") {
      let [startD, endD] = getStartAndEndDate(30);
      startDate = new Date(startD);
      endDate = new Date(endD);
    } else if (filter_options === "Last Week") {
      let [startD, endD] = getStartAndEndDate(7);
      startDate = new Date(startD);
      endDate = new Date(endD);
    } else if (filter_options === "Till now") {
      let [startD, endD] = getStartAndEndDate(3650 + 3650);
      startDate = new Date(startD);
      endDate = new Date(endD);
    } else if (filter_options === "Custom Date") {
    }

    // console.log("----------------------------");
    // console.log(filterState, isFilter);
    // console.log(startDate, "---", endDate, "---", filter_options);

    if (isFilter) {
      dispatch(marketing_filter_list(filterState, [startDate, endDate], auth));
    } else {
      dispatch(marketing_filter_list({}, [startDate, endDate], auth));
    }

    dispatch(marketing_headerdata_thunk(auth));
    dispatch(sales_header_count(auth));
  };

  const fetchList = () => {
    if (!auth?.id) {
      return;
    }

    fetch_list();
  };

  const fetchWithNewFilter = (filterObj) => {
    // Filter with filter Obj

    window.alert(
      "Bug in the marketing dashboard filter. Please connect with developer "
    );
    // dispatch(marketing_filter_list(filterObj, auth));
  };

  // Analytics
  const fetchAnalytics = (obj = null) => {
    if (!auth?.id) {
      return;
    }

    if (obj) {
      let customDate = "Custom Date";
      let startDate = obj?.startDate;
      let endDate = obj?.endDate;
      let filter = obj?.filter?.content;

      if (filter === "Last year") {
        let [startD, endD] = getStartAndEndDate(365);
        startDate = new Date(startD);
        endDate = new Date(endD);
      } else if (filter === "Last Month") {
        let [startD, endD] = getStartAndEndDate(30);
        startDate = new Date(startD);
        endDate = new Date(endD);
      } else if (filter === "Last Week") {
        let [startD, endD] = getStartAndEndDate(7);
        startDate = new Date(startD);
        endDate = new Date(endD);
      } else if (filter === "Till now") {
        let [startD, endD] = getStartAndEndDate(3650 + 3650);
        startDate = new Date(startD);
        endDate = new Date(endD);
      } else if (filter === customDate) {
      }

      dispatch(fetch_analytics_thunk(startDate, endDate));
    } else {
      let customDate = "Custom Date";
      let startDate = analytics?.startDate;
      let endDate = analytics?.endDate;
      let filter = analytics?.filter?.content;

      if (filter === "Last year") {
        let [startD, endD] = getStartAndEndDate(365);
        startDate = new Date(startD);
        endDate = new Date(endD);
      } else if (filter === "Last Month") {
        let [startD, endD] = getStartAndEndDate(30);
        startDate = new Date(startD);
        endDate = new Date(endD);
      } else if (filter === "Last Week") {
        let [startD, endD] = getStartAndEndDate(7);
        startDate = new Date(startD);
        endDate = new Date(endD);
      } else if (filter === "Till now") {
        let [startD, endD] = getStartAndEndDate(3650 + 3650);
        startDate = new Date(startD);
        endDate = new Date(endD);
      } else if (filter === customDate) {
      }

      dispatch(fetch_analytics_thunk(startDate, endDate));
    }
  };

  // table header data load
  const loadTableHeaderData = () => {
    dispatch(marketing_loadTableHeaderData_thunk(table?.header));
  };

  return {
    loadTableHeaderData,
    fetch_list,
    fetchList,
    fetchWithNewFilter,
    fetchAnalytics,
  };
};

export default UseMarketing;

// ADD FORM Builder
export const fetch_marketing_add_form_enginee = async () => {
  let url = new URL(BaseURL?.mainURl + API_ENDPOINTS?.leadAddFormBuilder);

  let headers = { ...api_headers };

  let response = await GetAPI(url, headers);

  if (response?.ok) {
    let responseData = await response?.json();
    // Apply our RE_VISIT_DATE modification to the API response
    if (responseData?.structure) {
      let structure = responseData.structure;
      // Remove the individual revisit date fields
      structure = structure.filter(field =>
        !['RE_VISIT_DATE_1', 'RE_VISIT_DATE_2', 'RE_VISIT_DATE_3', 'RE_VISIT_DATE_4', 'RE_VISIT_REMARKS'].includes(field.key)
      );
      // Add our multiDate field
      structure.push({
        type: "multiDate",
        key: "RE_VISIT_DATE",
        title: "Revisit Date",
        defaultValue: [],
        format: "format",
      });

      // Add location field
      structure.push({
        type: "text",
        key: "location",
        placeholder: "Location (e.g., Kovai)",
        defaultValue: "",
      });

      return structure;
    }
    return responseData?.structure;
  }

  // Fall back to our local configuration that already has the multiDate field
  const { salesandmarketing_addForm } = require('../Config/SalesAndMarketingTableConfig');
  return salesandmarketing_addForm;
};

// EDIT FORM Builder
export const fetch_marketing_edit_form_enginee = async (id) => {
  // Use the salesEditFormBuilder endpoint with POST method instead of getLeadById
  let url = new URL(BaseURL?.mainURl + API_ENDPOINTS?.salesEditFormBuilder);

  let headers = { ...api_headers };

  console.log("Fetching marketing edit form with URL:", url.toString());
  console.log("Using headers:", headers);
  console.log("For ID:", id);

  try {
    // Use PostAPI instead of GetAPI since we need to send the ID in the body
    let body = { id: id };
    console.log("Request body:", body);

    // Import PostAPI here to avoid circular dependencies
    const { PostAPI } = require('../API/PostAPI');
    let response = await PostAPI(url, body, headers);

    console.log("Response status:", response?.status);

    if (response?.ok) {
      let responseData = await response?.json();
      console.log("Raw response data:", responseData);

      // Check if we have a structure in the response
      if (responseData?.structure && Array.isArray(responseData.structure)) {
        console.log("Original structure:", responseData.structure);
        let structure = responseData.structure;

        // Get the lead data to populate the form
        // Try to fetch the lead data using getLeadById if available
        try {
          const getLeadUrl = new URL(BaseURL?.mainURl + API_ENDPOINTS?.getLeadById + id);
          const leadResponse = await GetAPI(getLeadUrl, headers);

          if (leadResponse?.ok) {
            const leadData = await leadResponse.json();
            console.log("Lead data:", leadData);

            if (leadData?.data) {
              const data = leadData.data;

              // Create RE_VISIT_DATE array from individual fields if they exist
              const revisitDates = [];
              if (data.RE_VISIT_DATE_1) revisitDates.push({ date: data.RE_VISIT_DATE_1, remark: data.RE_VISIT_REMARKS || "" });
              if (data.RE_VISIT_DATE_2) revisitDates.push({ date: data.RE_VISIT_DATE_2, remark: "" });
              if (data.RE_VISIT_DATE_3) revisitDates.push({ date: data.RE_VISIT_DATE_3, remark: "" });
              if (data.RE_VISIT_DATE_4) revisitDates.push({ date: data.RE_VISIT_DATE_4, remark: "" });

              if (revisitDates.length > 0) {
                data.RE_VISIT_DATE = revisitDates;
              } else if (data.RE_VISIT_DATE && Array.isArray(data.RE_VISIT_DATE)) {
                // Check if existing RE_VISIT_DATE is already in the correct format
                if (data.RE_VISIT_DATE.length > 0 && typeof data.RE_VISIT_DATE[0] === 'string') {
                  // Convert old format (array of strings) to new format (array of objects)
                  data.RE_VISIT_DATE = data.RE_VISIT_DATE.map((date, index) => ({
                    date,
                    remark: index === 0 && data.RE_VISIT_REMARKS ? data.RE_VISIT_REMARKS : ""
                  }));
                }
              } else {
                data.RE_VISIT_DATE = [];
              }

              // Remove individual revisit date fields from structure
              structure = structure.filter(field =>
                !['RE_VISIT_DATE_1', 'RE_VISIT_DATE_2', 'RE_VISIT_DATE_3', 'RE_VISIT_DATE_4', 'RE_VISIT_REMARKS'].includes(field.key)
              );

              // Add our multiDate field to structure
              structure.push({
                type: "multiDate",
                key: "RE_VISIT_DATE",
                title: "Revisit Date",
                defaultValue: data.RE_VISIT_DATE || [],
                format: "format",
              });

              // Add location field to structure
              structure.push({
                type: "text",
                key: "location",
                placeholder: "Location (e.g., Kovai)",
                defaultValue: data.location || "",
              });

              // Update default values in structure based on lead data
              structure = structure.map(field => {
                if (data[field.key] !== undefined) {
                  return { ...field, defaultValue: data[field.key] };
                }
                return field;
              });

              responseData.data = data;
            }
          }
        } catch (error) {
          console.error("Error fetching lead data:", error);
        }

        responseData.structure = structure;
        console.log("Modified structure:", structure);
        return responseData;
      } else {
        console.log("No valid structure found in response data");

        // If no structure is found, let's create a fallback structure
        const { salesandmarketing_addForm } = require('../Config/SalesAndMarketingTableConfig');
        console.log("Using fallback structure from config");

        // Create a response with the fallback structure
        return {
          structure: salesandmarketing_addForm,
          data: {}
        };
      }
    } else {
      console.error("API response not OK:", response);
      // Try to get error details if available
      try {
        const errorData = await response.json();
        console.error("Error details:", errorData);
      } catch (e) {
        console.error("Could not parse error response");
      }
    }
  } catch (error) {
    console.error("Exception in fetch_marketing_edit_form_enginee:", error);
  }

  // As a last resort, use the add form structure as a fallback
  console.log("Using add form as fallback");
  try {
    const addFormResult = await fetch_marketing_add_form_enginee();
    return { structure: addFormResult || [] };
  } catch (error) {
    console.error("Error fetching add form as fallback:", error);
  }

  // Ultimate fallback to empty object with empty structure array
  console.log("Returning empty fallback object");
  return { structure: [] };
};

//load table header data thunk
const marketing_loadTableHeaderData_thunk = (header) => {
  return async (dispatch) => {
    let headerdata_fetched = headerdata;
    let url = new URL(BaseURL?.mainURl + API_ENDPOINTS?.salestableheaderData);
    let headers = { ...api_headers };
    let response = await GetAPI(url, headers);
    if (response?.ok) {
      let responseData = await response?.json();
      let tableheaderdata = responseData?.tableheaderdata;
      let data = headerData_mapping_with_header_algo(header, tableheaderdata);
      dispatch(
        marketingSliceActions?.updateTableHeader(data?.loadedHeaderData)
      );
    } else {
      let data = headerData_mapping_with_header_algo(
        header,
        headerdata_fetched
      );
      dispatch(
        marketingSliceActions?.updateTableHeader(data?.loadedHeaderData)
      );
    }
  };
};

// List thunk
const marketing_list_thunk = (auth) => {
  return async (dispatch) => {
    let url = new URL(BaseURL.mainURl + API_ENDPOINTS.salesAndMarketingListing);
    url.searchParams.set("limit", 500);

    url.searchParams.set("userId", auth.id);

    let headers = { ...api_headers };

    let response = await GetAPI(url, headers);

    if (response?.ok) {
      let responsedata = await response?.json();

      let array = [
        ...responsedata?.data?.assignedBy,
        ...responsedata?.data?.assignedTo,
      ];

      dispatch(marketingSliceActions.updateList(array));
    }
  };
};

function formatDateToYYYYMMDD(dateObj) {
  if (!(dateObj instanceof Date)) {
    // throw new Error("Invalid input: Expected a Date object");
    return;
  }
  dateObj = new Date(dateObj);
  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, "0"); // Months are 0-based
  const day = String(dateObj.getDate()).padStart(2, "0");

  return `${year}-${month}-${day}`;
}

export const marketing_filter_list = (
  filterObj,
  [startDate, endDate],
  auth
) => {
  return async (dispatch) => {
    let url = new URL(BaseURL?.mainURl + API_ENDPOINTS?.MarketingListFilter);

    url =
      url.href +
      "?startDate=" +
      formatDateToYYYYMMDD(new Date(startDate)) +
      "&tillDate=" +
      formatDateToYYYYMMDD(new Date(endDate)) +
      "&userId=" +
      auth?.id;

    let headers = { ...api_headers };

    let body = filterObj;

    console.log(url, body);

    let response = await PostAPI(url, body, headers);
    // let response = await fetch_sales_list_with_filter(filterObj, auth);

    if (response?.ok) {
      let data = await response?.json();
      let array = [...data?.data?.assignedBy, ...data?.data?.assignedTo];

      if (response) {
        dispatch(marketingSliceActions.updateList(array));
      } else {
        dispatch(marketingSliceActions.updateList([]));
      }
    }
  };
};

function formatDateToCustomString(dateObj) {
  // Array of day and month names
  const days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  dateObj = new Date(dateObj);
  // Get day of the week, month, day of the month, and year
  const dayOfWeek = days[dateObj.getDay()];
  const month = months[dateObj.getMonth()];
  const dayOfMonth = String(dateObj.getDate()).padStart(2, "0"); // Add leading zero if needed
  const year = dateObj.getFullYear();

  // Return the formatted string
  return `${dayOfWeek}+${month}+${dayOfMonth}+${year}`;
}

const fetch_analytics_thunk = (startDate, endDate) => {
  return async (dispatch) => {
    let url = new URL(
      BaseURL?.mainURl + API_ENDPOINTS?.salesAndMarketingAnalyticsFilter
    );

    url =
      url.href +
      "?startDate=" +
      formatDateToCustomString(new Date(startDate)) +
      "&tillDate=" +
      formatDateToCustomString(new Date(endDate));

    let headers = { ...api_headers };

    let response = await GetAPI(url, headers);

    if (response?.ok) {
      let responseData = await response?.json();

      let targetData = {
        targetAchieved: responseData?.targetData?.totalAchieved,
        pendingTargets: responseData?.targetData?.totalPending,
      };

      let daysLeadClosure =
        responseData?.daysLeadClosure?.length > 0
          ? responseData?.daysLeadClosure[0]?.averageLeadDuration?.toFixed(2)
          : 0;

      let obj = {
        salesPulse: responseData.salesPulse,
        leadSource: responseData?.leadSource,
        leadClosure: {
          leads: responseData?.leadClosure?.map((item) => {
            return item?.leads;
          }),
          closures: responseData?.leadClosure?.map((item) => {
            return item?.closures;
          }),
          date: responseData?.leadClosure?.map((item) => {
            return item?.date;
          }),
        },
        Walk_in_Status: responseData?.companyCount?.Walk_in_Status,

        top5: {
          city: responseData?.top5?.map((item) => {
            return item?._id;
          }),
          count: responseData?.top5?.map((item) => {
            return item?.count;
          }),
        },

        salesTrend: {
          sales: responseData?.salesTrend?.map((item) => {
            return item?._id;
          }),
          count: responseData?.salesTrend?.map((item) => {
            return item?.count;
          }),
        },

        averageOrderValue: responseData?.averageOrderValue,

        targetData: targetData,

        AOVvsDay: {
          date: responseData?.AOVvsDay?.map((item) => item?.date),
          data: responseData?.AOVvsDay?.map((item) => item?.AOV),
        },

        topSalesPerson: {
          name: responseData?.topSalesPerson?.map((item) => item?._id),
          totalQuantitySold: responseData?.topSalesPerson?.map(
            (item) => item?.totalQuantitySold
          ),
          totalOrderValue: responseData?.topSalesPerson?.map(
            (item) => item?.totalOrderValue
          ),
        },

        topSellingProducts: {
          name: responseData?.topSellingProducts?.map((item) => item?._id),
          totalQuantity: responseData?.topSellingProducts?.map(
            (item) => item?.totalQuantity
          ),
        },

        daysLeadClosure: daysLeadClosure,

        TargetsVsAchieved: {
          date: responseData?.TargetsVsAchieved?.map((item) => item?.date),
          IN_FOLLOW: responseData?.TargetsVsAchieved?.map(
            (item) => item?.IN_FOLLOW
          ),
          QUOTE_SENT: responseData?.TargetsVsAchieved?.map(
            (item) => item?.QUOTE_SENT
          ),
        },

        ShowLeadsquoteengineers: {
          newLeads: responseData?.ShowLeadsquoteengineers?.map(
            (item) => item?.newLeads
          ),
          quoteSent: responseData?.ShowLeadsquoteengineers?.map(
            (item) => item?.quoteSent
          ),
          engineer: responseData?.ShowLeadsquoteengineers?.map(
            (item) => item?.engineer
          ),
        },

        ShowLeadsquoteARCH_NAME: {
          newLeads: responseData?.ShowLeadsquoteARCH_NAME?.map(
            (item) => item?.newLeads
          ),
          quoteSent: responseData?.ShowLeadsquoteARCH_NAME?.map(
            (item) => item?.quoteSent
          ),
          architect: responseData?.ShowLeadsquoteARCH_NAME?.map(
            (item) => item?.architect
          ),
        },

        // ShowLeadsquoteBrands: {
        //   newLeads: responseData?.ShowLeadsquoteBrands?.map(
        //     (item) => item?.newLeads
        //   ),
        //   quoteSent: responseData?.ShowLeadsquoteBrands?.map(
        //     (item) => item?.quoteSent
        //   ),
        // },

        ShowLeadsquoteARCH_NAME: {
          newLeads: responseData?.ShowLeadsquoteARCH_NAME?.map((item) => {
            return item?.newLeads ? item?.newLeads : "";
          }),
          quoteSent: responseData?.ShowLeadsquoteARCH_NAME?.map(
            (item) => item?.quoteSent
          ),
          architect: responseData?.ShowLeadsquoteARCH_NAME?.map(
            (item) => item?.architect
          ),
        },

        ShowLeadsquoteBrand: {
          brand: responseData?.ShowLeadsquoteBrand?.map(
            (item) => item?.brandname
          ),
          newLeads: responseData?.ShowLeadsquoteBrand?.map(
            (item) => item?.newLeads
          ),
          quoteSent: responseData?.ShowLeadsquoteBrand?.map(
            (item) => item?.quoteSent
          ),
        },

        Leadsquoteresidentindividual: {
          // INDIVIDUALRESIDENT: {
          //   newLead:
          //     responseData?.Leadsquoteresidentindividual?.[
          //       "INDIVIDUAL RESIDENT"
          //     ]?.newlead,
          //   quoteSent:
          //     responseData?.Leadsquoteresidentindividual?.[
          //       "INDIVIDUAL RESIDENT"
          //     ]?.quotesent,
          // },
          RESIDENT: {
            newLead:
              responseData?.Leadsquoteresidentindividual?.RESIDENT?.newlead,
            quoteSent:
              responseData?.Leadsquoteresidentindividual?.RESIDENT?.quotesent,
          },

          APARTMENT: {
            newLead:
              responseData?.Leadsquoteresidentindividual?.APARTMENT?.newlead,
            quoteSent:
              responseData?.Leadsquoteresidentindividual?.APARTMENT?.quotesent,
          },

          HOSPITALS: {
            newLead:
              responseData?.Leadsquoteresidentindividual?.HOSPITALS?.newlead,
            quoteSent:
              responseData?.Leadsquoteresidentindividual?.HOSPITALS?.quotesent,
          },

          COMMERCIAL: {
            newLead:
              responseData?.Leadsquoteresidentindividual?.COMMERCIAL?.newlead,
            quoteSent:
              responseData?.Leadsquoteresidentindividual?.COMMERCIAL?.quotesent,
          },

          INDUSTRIES: {
            newLead:
              responseData?.Leadsquoteresidentindividual?.INDUSTRIES?.newlead,
            quoteSent:
              responseData?.Leadsquoteresidentindividual?.INDUSTRIES?.quotesent,
          },
        },
      };

      dispatch(marketingSliceActions?.updateAnalyticsContent(obj));
    }
  };
};
