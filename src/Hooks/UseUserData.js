// Hooks
import { useDispatch, useSelector } from "react-redux";

//Thunk
import { updateUserListThunkMaster } from "../Store/MasterSlice/MasterSlice";

// This Hook is to handle the user data
// easily get the user list

const UseUserData = () => {
  const dispatch = useDispatch();
  const { ui } = useSelector((state) => {
    return state.master;
  });

  let userList = ui.userList;

  let fetchUserList = () => {
    // Function to fetch the user list
    dispatch(updateUserListThunkMaster());
  };

  const getUserList = () => {
    // Get user list
    return userList;
  };

  const getUsernameWithid = (id = "") => {
    // Get the Username with the user Id
    for (let i = 0; i < userList.length; i++) {
      if (userList[i].id === id) {
        return userList[i].content;
      }
    }
    return id;
  };
  const stringOfUsernameWithIdArray = (list = []) => {
    // Get the string of usernames with user id list
    let string = "";
    for (let i = 0; i < list?.length; i++) {
      string = string + " " + getUsernameWithid(list[i]) + ",";
    }
    return string;
  };

  // Form List update
  const updateUserNameInForms = (array) => {
    let return_array = [];


    for (let item of array) {
      for (let i of userList) {
        if (i?.id === item?.id) {
          return_array.push(i);
        }
      }
    }

    if (return_array?.length === 0) {
      return array;
    } else {
      return return_array;
    }
  };

  return {
    userList,
    getUserList,
    fetchUserList,
    getUsernameWithid,
    stringOfUsernameWithIdArray,
    updateUserNameInForms,
  };
};

export default UseUserData;
