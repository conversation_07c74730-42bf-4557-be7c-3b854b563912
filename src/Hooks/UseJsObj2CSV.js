import { useSelector } from "react-redux";
import UseUserData from "./UseUserData";

// Get date as string
function getCurrentDateTime() {
  let today = new Date();

  let day = String(today.getDate()).padStart(2, "0"); // Get day and pad with 0 if necessary
  let month = String(today.getMonth() + 1).padStart(2, "0"); // Get month and pad with 0
  let year = today.getFullYear(); // Get full year

  let hours = String(today.getHours()).padStart(2, "0"); // Get hours and pad with 0
  let minutes = String(today.getMinutes()).padStart(2, "0"); // Get minutes and pad with 0
  let seconds = String(today.getSeconds()).padStart(2, "0"); // Get seconds and pad with 0

  return `${year}_${month}_${day}_${hours}_${minutes}_${seconds}`;
}

// Data parser
//Date formater
function formatDate(date) {
  const day = String(date?.getDate()).padStart(2, "0"); // Get day and add leading zero if needed
  const month = String(date?.getMonth() + 1).padStart(2, "0"); // Get month and add leading zero (months are 0-indexed)
  const year = date?.getFullYear(); // Get full year

  return `${day}-${month}-${year}`;
}

// Function to convert JS object to CSV format
const convertToCSV = (data) => {
  if (!data || data.length === 0) {
    console.warn("No data provided for CSV conversion");
    return "";
  }

  const csvRows = [];

  // Get the headers
  const headers = Object.keys(data[0]);
  //csvRows.push(headers.join(",")); // Join headers with commas

  // Loop over the data
  for (const row of data) {
    const values = headers.map((header) => {
      const value = row[header] !== undefined && row[header] !== null ? row[header] : "";
      const escaped = ("" + value).replace(/"/g, '\\"'); // Escape double quotes
      return `"${escaped}"`; // Wrap each value with double quotes for CSV formatting
    });
    csvRows.push(values.join(",")); // Join values with commas
  }

  return csvRows.join("\n"); // Add new line for each row
};

// Function to download the CSV file
const downloadCSV = (data, name = "data.csv") => {
  try {
    if (!data || data.length === 0) {
      console.warn("No data available for download");
      return;
    }

    const csv = convertToCSV(data);
    if (!csv) {
      console.warn("Failed to convert data to CSV format");
      return;
    }

    const blob = new Blob([csv], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);

    const a = document.createElement("a");
    a.setAttribute("hidden", "");
    a.setAttribute("href", url);
    a.setAttribute("download", name);
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);

    // Clean up the URL object
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error("Error downloading CSV file:", error);
  }
};

const UseJsObj2CSV = () => {
  const { getUsernameWithid } = UseUserData();

  // Download sales dashboard table as CSV file
  const salesAndMarketingList = useSelector((state) => {
    return state.salesAndMarketing.salesAndMarketingList;
  });
  const downloadSalesDashboard = () => {
    let array = [];

    const title = {
      id: "ID",
      OWNER_NAME: "Owner Name",

      AssignedBy: "Assigned By",

      CONTACT_NO_1: "Contact Number 1",
      CONTACT_NO_2: "Contact Number 2",
      CONTACT_NO_3: "Contact Number 3",
      CONTACT_NO_4: "Contact Number 4",
      DATE_OF_VISIT: "Date Of Visit",
      VISITED_BY: "Visited By",
      LEAD_STATUS: "Lead Status",
      LEAD_TO_COMPANY: "Lead To Company",
      LEAD_SALES_PERSON: "Lead Sales Person",
      LEAD_TYPE: "Lead Type",
      CITY: "City",
      AREA: "Area",
      LANDMARK: "Landmark",
      engineer_name: "Engineer Name",
      engineer_number: "Engineer Number",
      ARCH_FIRM_NAME: "Arch Firm Name",
      ARCH_NAME: "Architect Name",
      ARCH_NUMBER: "Architect Number",
      PLUMBER_NAME: "Plumber Name",
      PLUMBER_NUMBER: "Plumber Number",
      KEY_PERSON_NAME: "Key Person Name",
      KEY_PERSON_NUMBER: "Key Person Number",
      BRAND_TO_PROMOTE: "Brand To Promote",
      LEAD_PERSON_REMARKS: "Lead Person Remarks",
      FOLLOW_UP_DATE_1: "Follow Up Date 1",
      FOLLOW_UP_DATE_2: "Follow Up Date 2",
      TELECALLING_REMARKS: "Telecalling Remartks",
      RE_VISIT_DATE_1: "Revisit Date 1",
      RE_VISIT_DATE_2: "Revisit Date 2",
      RE_VISIT_DATE_3: "Revisit Date 3",
      RE_VISIT_DATE_4: "Revisit Date 4",
    };

    array.push(title);
    for (let item of salesAndMarketingList) {
      let current = {
        id: item?._id,
        OWNER_NAME: item?.OWNER_NAME,

        AssignedBy: getUsernameWithid(item?.assignedBy),

        CONTACT_NO_1: item?.CONTACT_NO_1,
        CONTACT_NO_2: item?.CONTACT_NO_2,
        CONTACT_NO_3: item?.CONTACT_NO_3,
        CONTACT_NO_4: item?.CONTACT_NO_4,
        DATE_OF_VISIT: formatDate(new Date(item?.DATE_OF_VISIT)),
        VISITED_BY: getUsernameWithid(item?.VISITED_BY),
        LEAD_STATUS: item?.LEAD_STATUS,
        LEAD_TO_COMPANY: item?.LEAD_TO_COMPANY,
        LEAD_SALES_PERSON: getUsernameWithid(item?.LEAD_SALES_PERSON),
        LEAD_TYPE: item?.LEAD_TYPE,
        CITY: item?.CITY,
        AREA: item?.AREA,
        LANDMARK: item?.LANDMARK,
        engineer_name: item?.engineer_name,
        engineer_number: item?.engineer_number,
        ARCH_FIRM_NAME: item?.ARCH_FIRM_NAME,
        ARCH_NAME: item?.ARCH_NAME,
        ARCH_NUMBER: item?.ARCH_NUMBER,
        PLUMBER_NAME: item?.PLUMBER_NAME,
        PLUMBER_NUMBER: item?.PLUMBER_NUMBER,
        KEY_PERSON_NAME: item?.KEY_PERSON_NAME,
        KEY_PERSON_NUMBER: item?.KEY_PERSON_NUMBER,
        BRAND_TO_PROMOTE: item?.BRAND_TO_PROMOTE,
        LEAD_PERSON_REMARKS: item?.LEAD_PERSON_REMARKS,
        FOLLOW_UP_DATE_1: item?.FOLLOW_UP_DATE_1,
        FOLLOW_UP_DATE_2: item?.FOLLOW_UP_DATE_2,
        TELECALLING_REMARKS: item?.TELECALLING_REMARKS,
        RE_VISIT_DATE_1: item?.RE_VISIT_DATE_1,
        RE_VISIT_DATE_2: item?.RE_VISIT_DATE_2,
        RE_VISIT_DATE_3: item?.RE_VISIT_DATE_3,
        RE_VISIT_DATE_4: item?.RE_VISIT_DATE_4,
      };

      array.push(current);
    }

    downloadCSV(array, `salesdashboard_table_${getCurrentDateTime()}.csv`);
  };

  // Download tickets dashboard table to csv file
  const { raisedByList, raisedToList } = useSelector((state) => {
    return state.tickets;
  });

  let ticketlist = [...raisedByList, ...raisedToList];

  const downloadTicketDashboardTable = () => {
    let array = [];

    let title = {
      _id: "ID",
      name: "Name",
      department: "Department",
      description: "Description",
      raisedBy: "Raised By",
      raisedTo: "Raised To",
      status: "Status",
      timeline: "Time line",
      remarks: "Remarks",
      ticketDate: "Expected Resolution Time",
      time_taken: "Time taken",
    };

    array.push(title);

    for (let item of ticketlist) {
      let current = {
        name: item?.name,
        _id: item._id,
        department: item.department,
        description: item.description,
        raisedBy: getUsernameWithid(item.raisedBy),
        raisedTo: item?.raisedTo?.map((i) => getUsernameWithid(i)),
        status: item.status,
        timeline: item.timeline,
        remarks: item.remarks,
        ticketDate: formatDate(new Date(item.createdAt)),
        time_taken: item.timeTaken ? item?.time_taken + " Days" : "-",
      };

      array.push(current);
    }
    downloadCSV(array, `ticketsdashboard_table_${getCurrentDateTime()}.csv`);
  };

  // Marketing json download
  const marketingList = useSelector((state) => {
    return state.marketing?.list;
  });


  const downloadMarketingDashboard = () => {
    let array = [];

    const title = {
      id: "ID",
      OWNER_NAME: "Owner Name",

      AssignedBy: "Assigned By",

      CONTACT_NO_1: "Contact Number 1",
      CONTACT_NO_2: "Contact Number 2",
      CONTACT_NO_3: "Contact Number 3",
      CONTACT_NO_4: "Contact Number 4",
      DATE_OF_VISIT: "Date Of Visit",
      VISITED_BY: "Visited By",
      LEAD_STATUS: "Lead Status",
      LEAD_TO_COMPANY: "Lead To Company",
      LEAD_SALES_PERSON: "Lead Sales Person",
      LEAD_TYPE: "Lead Type",
      CITY: "City",
      AREA: "Area",
      LANDMARK: "Landmark",
      engineer_name: "Engineer Name",
      engineer_number: "Engineer Number",
      ARCH_FIRM_NAME: "Arch Firm Name",
      ARCH_NAME: "Architect Name",
      ARCH_NUMBER: "Architect Number",
      PLUMBER_NAME: "Plumber Name",
      PLUMBER_NUMBER: "Plumber Number",
      KEY_PERSON_NAME: "Key Person Name",
      KEY_PERSON_NUMBER: "Key Person Number",
      BRAND_TO_PROMOTE: "Brand To Promote",
      LEAD_PERSON_REMARKS: "Lead Person Remarks",
      FOLLOW_UP_DATE_1: "Follow Up Date 1",
      FOLLOW_UP_DATE_2: "Follow Up Date 2",
      TELECALLING_REMARKS: "Telecalling Remartks",
      RE_VISIT_DATE_1: "Revisit Date 1",
      RE_VISIT_DATE_2: "Revisit Date 2",
      RE_VISIT_DATE_3: "Revisit Date 3",
      RE_VISIT_DATE_4: "Revisit Date 4",
    };

    array.push(title);
    for (let item of marketingList) {
      let current = {
        id: item?._id,
        OWNER_NAME: item?.OWNER_NAME,
        AssignedBy: getUsernameWithid(item?.assignedBy),

        CONTACT_NO_1: item?.CONTACT_NO_1,
        CONTACT_NO_2: item?.CONTACT_NO_2,
        CONTACT_NO_3: item?.CONTACT_NO_3,
        CONTACT_NO_4: item?.CONTACT_NO_4,
        DATE_OF_VISIT: formatDate(new Date(item?.DATE_OF_VISIT)),
        VISITED_BY: getUsernameWithid(item?.VISITED_BY),
        LEAD_STATUS: item?.LEAD_STATUS,
        LEAD_TO_COMPANY: item?.LEAD_TO_COMPANY,
        LEAD_SALES_PERSON: getUsernameWithid(item?.LEAD_SALES_PERSON),
        LEAD_TYPE: item?.LEAD_TYPE,
        CITY: item?.CITY,
        AREA: item?.AREA,
        LANDMARK: item?.LANDMARK,
        engineer_name: item?.engineer_name,
        engineer_number: item?.engineer_number,
        ARCH_FIRM_NAME: item?.ARCH_FIRM_NAME,
        ARCH_NAME: item?.ARCH_NAME,
        ARCH_NUMBER: item?.ARCH_NUMBER,
        PLUMBER_NAME: item?.PLUMBER_NAME,
        PLUMBER_NUMBER: item?.PLUMBER_NUMBER,
        KEY_PERSON_NAME: item?.KEY_PERSON_NAME,
        KEY_PERSON_NUMBER: item?.KEY_PERSON_NUMBER,
        BRAND_TO_PROMOTE: item?.BRAND_TO_PROMOTE,
        LEAD_PERSON_REMARKS: item?.LEAD_PERSON_REMARKS,
        FOLLOW_UP_DATE_1: item?.FOLLOW_UP_DATE_1,
        FOLLOW_UP_DATE_2: item?.FOLLOW_UP_DATE_2,
        TELECALLING_REMARKS: item?.TELECALLING_REMARKS,
        RE_VISIT_DATE_1: item?.RE_VISIT_DATE_1,
        RE_VISIT_DATE_2: item?.RE_VISIT_DATE_2,
        RE_VISIT_DATE_3: item?.RE_VISIT_DATE_3,
        RE_VISIT_DATE_4: item?.RE_VISIT_DATE_4,
      };

      array.push(current);
    }

    downloadCSV(array, `marketingdashboard_table_${getCurrentDateTime()}.csv`);
  };


  const marketingWalkinList = useSelector((state) => {
    return state.marketingWalkin?.list;
  });

  const downloadMarketingWalkinDashboard = () => {
    let array = [];

    // Check if marketingWalkinList exists and has data
    if (!marketingWalkinList || marketingWalkinList.length === 0) {
      console.warn("No marketing walkin data available for download");
      return;
    }

    const title = {
      id: "ID",
      assignedBy: "Assigned By",
      salutation: "Salutation",
      name: "Name",
      date_of_birth: "Date of Birth",
      mobile_phone: "Mobile Phone",
      current_location: "Current Location",
      architect_details: "Architect Details",
      engineer_details: "Engineer Details",
      building_area: "Building Area",
      no_of_bathrooms: "No of Bathrooms",
      category: "Category",
      status_of_building: "Status of Building",
      customer_visited_following_showroom: "Showroom Visited",
      Category: "Category",
      name_of_the_sales_person: "Sales Person",
      customer_in_time: "Customer In Time",
      customer_out_time: "Customer Out Time",
      remarks: "Remarks",
      how_do_you_know_about_our_showroom: "How do you know about the showroom",
      unit: "Unit",
      walk_in_type: "Walk In Type",
      purpose_of_visit: "Purpose of Visit",
      lead_status: "Lead Status",
      Follow_up_Date_1: "Follow up Date 1",
      Follow_up_Date_2: "Follow up Date 2",
      Follow_up_Date_3: "Follow up Date 3",
      Follow_up_Date_4: "Follow up Date 4",
      message_status: "Message Status",
    }

    array.push(title);

    for (let item of marketingWalkinList) {
      let current = {
        id: item?._id || item?.id || "",
        assignedBy: getUsernameWithid(item?.assignedBy) || "",
        salutation: item?.salutation || "",
        name: item?.name || "",
        date_of_birth: item?.date_of_birth ? formatDate(new Date(item?.date_of_birth)) : "",
        mobile_phone: item?.mobile_phone || "",
        current_location: item?.current_location || "",
        architect_details: item?.architect_details || "",
        engineer_details: item?.engineer_details || "",
        building_area: item?.building_area || "",
        no_of_bathrooms: item?.no_of_bathrooms || "",
        category: item?.category || "",
        status_of_building: item?.status_of_building || "",
        customer_visited_following_showroom: Array.isArray(item?.customer_visited_following_showroom)
          ? item?.customer_visited_following_showroom.join(", ")
          : item?.customer_visited_following_showroom || "",
        Category: item?.Category || "",
        name_of_the_sales_person: Array.isArray(item?.name_of_the_sales_person)
          ? item?.name_of_the_sales_person.map(id => getUsernameWithid(id)).join(", ")
          : getUsernameWithid(item?.name_of_the_sales_person) || "",
        customer_in_time: item?.customer_in_time ? formatDate(new Date(item?.customer_in_time)) : "",
        customer_out_time: item?.customer_out_time ? formatDate(new Date(item?.customer_out_time)) : "",
        remarks: item?.remarks || "",
        how_do_you_know_about_our_showroom: item?.how_do_you_know_about_our_showroom || "",
        unit: item?.unit || "",
        walk_in_type: item?.walk_in_type || "",
        purpose_of_visit: item?.purpose_of_visit || "",
        lead_status: item?.lead_status || "",
        Follow_up_Date_1: item?.Follow_up_Date_1 ? formatDate(new Date(item?.Follow_up_Date_1)) : "",
        Follow_up_Date_2: item?.Follow_up_Date_2 ? formatDate(new Date(item?.Follow_up_Date_2)) : "",
        Follow_up_Date_3: item?.Follow_up_Date_3 ? formatDate(new Date(item?.Follow_up_Date_3)) : "",
        Follow_up_Date_4: item?.Follow_up_Date_4 ? formatDate(new Date(item?.Follow_up_Date_4)) : "",
        message_status: item?.walkin_message_sent ? "Message Sent" : "Message Not Sent",
      }

      array.push(current);
    }

    try {
      downloadCSV(array, `marketingwalkindashboard_table_${getCurrentDateTime()}.csv`);
    } catch (error) {
      console.error("Error downloading marketing walkin dashboard:", error);
    }
  };
  

  // ---------------- Order dashboard -----------------------------

  const orderListing = useSelector((state) => {
    return state.orderManagement.orderListing;
  });

  const downloadOrderDashboard = () => {
    let array = [];

    let title = {
      orderId: "Order Id",
      name: "Name",
      customerId: "Customer Id",
      orderDate: "Order Date",
      productDetails: "Product Details",
      batchNo: "Batch No",
      quantity: "Quantity",
      stockAvailablity: "Stock Availablity",
      unavailableQuantity: "Un Available Quantity",
      // damagedProducts: "Damaged Products",
      ETA: "ETA of Unavailable Stock",
      orderTotal: "Order Total",
      salesPerson: "Sales Person",
      deliveryDate: "Delivery Date",
      orderStatus: "Order Status",
      location: "Location",
      paymentReceived: "Payment Received",
      brand: "Brand",
    };

    array.push(title);

    for (let item of orderListing) {
      let current = {
        orderId: item?.orderId,
        name: item?.name,
        customerId: item?.customerId,
        orderDate: formatDate(new Date(item?.orderDate)),
        productDetails: item?.productDetails,
        batchNo: item?.batchNo,
        quantity: item?.quantity,
        stockAvailablity: item?.stockAvailablity,
        unavailableQuantity: item?.unavailableQuantity,
        // damagedProducts: item?.damagedProducts,
        ETA: item?.ETA,
        orderTotal: item?.orderTotal,
        salesPerson: item?.salesPerson,
        deliveryDate: formatDate(new Date(item?.deliveryDate)),
        orderStatus: item?.orderStatus,
        location: item?.location,
        paymentReceived: item?.paymentReceived,
        brand: item?.brand,
      };

      array.push(current);
    }

    downloadCSV(array, `ordersdashboard_table_${getCurrentDateTime()}.csv`);
  };

  // ----- Customer Dashboard Download
  const { searchedCustomerList } = useSelector((state) => {
    return state.customer;
  });

  const downloadCustomerDashboard = () => {
    let array = [];

    let title = {
      id: "Id",
      customerName: "Customer Name",
      customerSince: "Customer Since",
      email: "Email",
      lastPurchase: "Last Purchase",
      orderCount: "Order Count",
      phoneNumber: "Phone Number",
      totalOrder: "Total Order",
      ifReferred: "If Referred",
      referredCustomerName: "Referred Customer Name",
      referredCustomerId: "Referred Customer Id",
      finalJobLink: "Final Job Link",
    };

    array?.push(title);

    for (let item of searchedCustomerList) {
      let current = {
        id: item.id,
        customerName: item.customerName,
        customerSince: formatDate(new Date(item?.customerSince)),
        email: item?.email,
        lastPurchase: item?.lastPurchase,
        orderCount: item?.orderCount,
        phoneNumber: item?.phoneNumber,
        totalOrder: item?.totalOrder,
        ifReferred: item?.ifReferred,
        referredCustomerName: item?.referredCustomerName,
        referredCustomerId: item?.referredCustomerId,
        finalJobLink: item?.finalJobLink,
      };

      array?.push(current);
    }

    downloadCSV(array, `customersdashboard_table_${getCurrentDateTime()}.csv`);
  };

  //------ Networking Dashboard ---------
  const networkingList = useSelector((item) => {
    return item?.networking?.list;
  });

  const downloadNetworkingDashboard = () => {
    let array = [];

    let title = {
      id: "Id",
      name: "Name",
      email: "Email",
      phoneNumber: "Phone Number",
      location: "Location",
      dateOfBirth: "Date Of Birth",
      relationshipStatus: "Relationalship Status",
      anvisoryDate: "Anvisory Date",
      designation: "Designation",
      employeeId: "Employee Id",
      qualification: "Qualification",
    };

    array?.push(title);

    for (let item of networkingList) {
      let current = {
        id: item?._id,
        name: item?.Name,
        email: item?.Email,
        phoneNumber: item?.Phone_number,
        location: item?.Location,
        dateOfBirth: item?.Date_Of_Birth,
        relationshipStatus: item?.Relationship_Status,
        anvisoryDate: item?.Anvisory_Date,
        designation: item?.Designation,
        employeeId: item?.Employee_id,
        qualification: item?.Qualification,
      };

      array?.push(current);
    }

    console.log(array);

    downloadCSV(array, `networkingdashboard_table_${getCurrentDateTime()}.csv`);
  };

  // -------- service complaints
  const serviceComplaintsList = useSelector((state) => {
    return state.serviceComplaints.serviceComplaintsList;
  });

  let downloadServiceComplaintsDashboard = () => {
    let array = [];

    let title = {
      _id: "ID",
      complaint_date: "Complaints Date",
      raised_by: "Raised By",
      type_of_request: "Type of request",
      order_id: "Order Id",
      customer_id: "Customer Id",
      order_date: "Order Date",
      product_details: "Product Details",
      batch_no: "Batch No",
      quantity: "Quantity",
      complaint: "Complaints",
      attending_person: "Attending Person",
      complaint_status: "Complaint Status",
      time_taken: "Time Taken",
    };

    array.push(title);

    for (let item of serviceComplaintsList) {
      let current = {
        _id: item?._id,
        complaint_date: item?.complaint_date,
        raised_by: getUsernameWithid(item?.raised_by),
        type_of_request: item?.type_of_request,
        order_id: item?.order_id,
        customer_id: item?.customer_id,
        order_date: item?.order_date,
        product_details: item?.product_details,
        batch_no: item?.batch_no,
        quantity: item?.quantity,
        complaint: item?.complaint,
        attending_person: getUsernameWithid(item?.attending_person),
        complaint_status: item?.complaint_status,

        time_taken: item?.time_taken ? item?.time_taken + " Days" : "-",
      };

      array.push(current);
    }

    downloadCSV(
      array,
      `serviceComplaintsdashboard_table_${getCurrentDateTime()}.csv`
    );
  };

  // -------------- Task management Dashboard -----------------------

  const taskmanagement_list = useSelector((state) => {
    return state.taskManagement?.list;
  });

  const downloadTaskmanagementDashboard = () => {
    let array = [];

    let title = {
      id: "Id",
      taskDescription: "Task Description",
      department: "Department",
      project: "Project",
      assignee: "Assingee",
      assignedBy: "Assignee By",
      dueDate: "Dead Line Date",
      status: "Status",
      completedDate: "Completed Date",
      createdAt: "Created At",
    };

    array.push(title);

    for (let item of taskmanagement_list) {
      let current = {
        id: item?.id,
        taskDescription: item?.taskDescription,
        department: item?.department,
        project: item?.project,
        assignee: item?.assignee?.map((i) => getUsernameWithid(i)),
        assignedBy: getUsernameWithid(item?.assignedBy),
        dueDate: formatDate(new Date(item?.dueDate)),
        status: item?.status,
        completedDate: formatDate(new Date(item?.completedDate)),
        createdAt: formatDate(new Date(item?.createdAt)),
      };
      array?.push(current);
    }

    downloadCSV(
      array,
      `taskmanagementdashboard_table_${getCurrentDateTime()}.csv`
    );
  };

  //--------------------- purchase dashboard download

  const purchaseListing = useSelector((state) => {
    return state.purchase.purchaseList;
  });

  const downloadPurchaseDownload = () => {
    let array = [];
    let title = {
      id: "Id",
      productDetails: "Purchase Details",
      batchNo: "Batch No",
      quantity: "Quantity",
      material: "Material",
      division: "Division",
      size: "Size",
      category: "Category",
      subCategory: "Sub Category",
      brand: "Brand",
      grade: "Grade",
      warehouseStatus: "Warehouse Status",
      vehicleNumber: "Vehicle Number",
      Nature_of_Order: "Nature Of Orders",
      unloadingDate: "Unloading Date",
      ETA: "ETA",
      // purchaseOrder: "Purchase Order",
    };

    array.push(title);

    for (let item of purchaseListing) {
      let current = {
        id: item?._id,
        productDetails: item?.productDetails,
        batchNo: item?.batchNo,
        quantity: item?.quantity,
        material: item?.material,
        division: item?.division,
        size: item?.size,
        category: item?.category,
        subCategory: item?.subCategory,
        brand: item?.brand,
        grade: item?.grade,
        warehouseStatus: item?.warehouseStatus,
        vehicleNumber: item?.vehicleNumber,
        natureOfOrder: item?.natureOfOrder,
        unloadingDate: formatDate(new Date(item?.unloadingDate)),
        ETA: formatDate(new Date(item?.ETA)),
        // purchaseOrder: item?.purchaseOrder,
      };

      array?.push(current);
    }

    downloadCSV(array, `purchasedashboard_table_${getCurrentDateTime()}.csv`);
  };

  return {
    downloadSalesDashboard,
    downloadTicketDashboardTable,
    downloadMarketingDashboard,
    downloadMarketingWalkinDashboard,
    downloadOrderDashboard,
    downloadCustomerDashboard,
    downloadNetworkingDashboard,
    downloadServiceComplaintsDashboard,
    downloadTaskmanagementDashboard,
    downloadPurchaseDownload,
  };
};

export default UseJsObj2CSV;
