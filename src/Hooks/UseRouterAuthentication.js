import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
  MasterSliceActions,
  getLoginAuth_LocalStorage,
  update_auth_obj_master_slice_thunk,
} from "../Store/MasterSlice/MasterSlice";
import { routes } from "../Config/routesConfig";

const UseRouterAuthentication = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const master = useSelector((state) => {
    return state.master;
  });

  let auth = master?.auth;
  let access = master?.access;

  const checkAuthentication = () => {
    // return true

    // IS AUTHENTICATION
    if (!auth.isAuth) {
      let localStorageObj = getLoginAuth_LocalStorage();
      if (localStorageObj.email || localStorageObj?.token) {
        dispatch(update_auth_obj_master_slice_thunk(localStorageObj));
        dispatch(MasterSliceActions?.updateAccess(localStorageObj?.access));
        return true;
      } else {
        navigate("/users/signin");
        return false;
      }
    }

    return true;
  };

  const routeAuthentication = (dashboard, spec_access) => {
    if (access?.isSuperUser) {
      return true;
    }

    if (dashboard in access) {
      if (spec_access in access[dashboard]) {
        return access[dashboard][spec_access];
      }
    }

    return false;
  };

  const isDashboardAccessable = (dashboard) => {
    if (access?.isSuperUser) {
      return true;
    }

    if (dashboard in access) {
      if (!access[dashboard]["read"]) {
        /// local storage
        let localStorageObj = getLoginAuth_LocalStorage();
        let access_localstorage = localStorageObj?.access;
        if (!access_localstorage[dashboard]["read"]) {
          navigate(routes?.dashboard?.directLink);
        }
      }
    }
  };

  const isAnalyticsAccessable = (dashboard) => {
    if (access?.isSuperUser) {
      return true;
    }

    if (dashboard in access) {
      if (!access[dashboard]["insights"]) {
        /// local storage
        let localStorageObj = getLoginAuth_LocalStorage();
        let access_localstorage = localStorageObj?.access;
        if (!access_localstorage[dashboard]["insights"]) {
          navigate(routes?.dashboard?.directLink);
        }
      }
    }
  };

  return {
    checkAuthentication,
    routeAuthentication,
    isDashboardAccessable,
    isAnalyticsAccessable,
  };
};

export default UseRouterAuthentication;
