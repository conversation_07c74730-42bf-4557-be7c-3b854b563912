import React from "react";
import { RouterProvider, createBrowserRouter } from "react-router-dom";
import Home from "./pages/Home";
import { routes } from "./Config/routesConfig";
import { Provider } from "react-redux";
import { store } from "./Store";
import Dashboard from "./pages/Dashboard/Dashboard";
import UserManagement from "./pages/UserManagement/UserManagement";
import PurchasePage from "./pages/Purchase/PurchasePage";
import OrderManagementPage from "./pages/OrderManagement/OrderManagementPage";
import CustomerPage from "./pages/Customer/CustomerPage";
import SoloCustomer from "./pages/Customer/SoloCustomer";
import ServiceCompaints from "./pages/ServiceComplaints/ServiceCompaints";
import UserManagementEdit from "./pages/UserManagement/UserManagementEdit";
import UserManagementView from "./pages/UserManagement/UserManagementView";
import UserManagementDelete from "./pages/UserManagement/UserManagementDelete";
import UserManagementInviteUser from "./pages/UserManagement/UserManagementInviteUser";
import SoloCustomerTracking from "./pages/Customer/SoloCustomerTracking";
import ServiceComplaintsAddNewPage from "./pages/ServiceComplaints/ServiceComplaintsAddNewPage";
import OutletComponent from "./pages/OutletComponent";
import SigninAdmin from "./pages/Authentication/AdminAuthentication/SigninAdmin";

import SigninUserLayout from "./Components/Authentication/UserAuth/SigninUserLayout";

import SalesAndMarketingAnalyticsPage from "./pages/SalesAndMarketing/SalesAndMarketingAnalyticsPage";
import SalesAndMarketingMainLayout from "./pages/SalesAndMarketing/SalesAndMarketingMainLayout";
import PurchaseAnalyticsPage from "./pages/Purchase/PurchaseAnalyticsPage";
import OrdemanagementAnalyticsPage from "./pages/OrderManagement/OrdemanagementAnalyticsPage";
import ServiceComplaintsEditPage from "./pages/ServiceComplaints/ServiceComplaintsEditPage";
import TicketingCreate from "./pages/Ticketing/TicketingCreate";
import TicketingPage from "./pages/Ticketing/TicketingPage";
import SigninCustomer from "./pages/Authentication/CustomerAuthentication/SigninCustomer";

import ResetCustomerPasswordPage from "./pages/Authentication/CustomerAuthentication/ResetCustomerPasswordPage";
import TicketingEdit from "./pages/Ticketing/TicketingEdit";
import TaskManagementPage from "./pages/TaskManagement/TaskManagementPage";
import TaskManagementAddPage from "./pages/TaskManagement/TaskManagementAddPage";
import ResetPasswordPage from "./pages/Authentication/ResetPasswordPage";
import MarketingPage from "./pages/Marketing/MarketingPage";
import NetworkingPage from "./pages/Networking/NetworkingPage";
import NetworkingAdd from "./pages/Networking/NetworkingAdd";
import MarketingAdd from "./pages/Marketing/MarketingAdd";
import CustomerDashboard from "./pages/Dashboard/CustomerDashboard";
import CustomerOrderPage from "./pages/CustomerDashboardPages/CustomerOrderPage";
import CustomerDashboardOrderTracking from "./pages/CustomerDashboardPages/CustomerDashboardOrderTracking";
import UserManagementCreateUserPage from "./pages/UserManagement/UserManagementCreateUserPage";
import MarketingAnalytics from "./pages/Marketing/MarketingAnalytics";
import { Toaster } from "react-hot-toast";
import MarketingWalkin from "./pages/Marketing/MarketingWalkin";
import SAPPage from "./pages/SAP/SAPPages";
import SAPAddPage from "./pages/SAP/SAPCreate";
import SAPEditPage from "./pages/SAP/SAPEdit";

const router = createBrowserRouter([
  {
    path: routes.home.directLink,
    element: <Home />,
  },
  {
    path: "admin",
    element: <OutletComponent />,
    children: [
      {
        path: "signin",
        element: <SigninAdmin />,
      },
      // {
      //   path: "signup",
      //   element: <SignupAdmin />,
      // },
    ],
  },
  {
    path: "users",
    element: <OutletComponent />,
    children: [
      {
        path: "signin",
        element: <SigninUserLayout />,
      },
      // {
      //   path: "signup",
      //   element: <SignupUserLayout />,
      // },
    ],
  },
  {
    path: "customer",
    element: <OutletComponent />,
    children: [
      { path: "signin", element: <SigninCustomer /> },
      // { path: "signup", element: <SignupCustomer /> },
      {
        path: routes.customerResetPassword.relativeLink,
        element: <ResetCustomerPasswordPage />,
      },
    ],
  },
  {
    path: routes.resetPassword.directLink,
    element: <ResetPasswordPage />,
  },

  {
    path: routes.dashboard.directLink,
    element: <Dashboard />,
    children: [
      {
        path: routes.userManagement.directLink,
        element: <UserManagement />,
        children: [
          {
            path: routes.userManagementEdit.relativeLink,
            element: <UserManagementEdit />,
          },
          {
            path: routes.userManagementView.relativeLink,
            element: <UserManagementView />,
          },
          {
            path: routes.userManagementDelete.relativeLink,
            element: <UserManagementDelete />,
          },
          {
            path: routes.userManagementInviteUser.relativeLink,
            element: <UserManagementInviteUser />,
          },
          {
            path: routes.userManagementCreateUser.relativeLink,
            element: <UserManagementCreateUserPage />,
          },
        ],
      },
      {
        path: routes.purchase.directLink,
        children: [
          {
            index: true,
            element: <PurchasePage />,
          },
          {
            path: routes.purchaseAnalytics.relativeLink,
            element: <PurchaseAnalyticsPage />,
          },
        ],
      },

      {
        path: routes.orderManagement.directLink,
        children: [
          {
            index: true,
            element: <OrderManagementPage />,
          },
          {
            path: routes.orderManagementAnalytics.relativeLink,
            element: <OrdemanagementAnalyticsPage />,
          },
        ],
      },
      {
        path: routes.customer.directLink,
        children: [
          {
            index: true,
            element: <CustomerPage />,
          },
          {
            path: routes.solocustomer.directLink,
            children: [
              { index: true, element: <SoloCustomer /> },
              {
                path: routes.solocustomerTracking.relativeLink,
                element: <SoloCustomerTracking />,
                children: [
                  {
                    path: ":billid",
                    element: <SoloCustomerTracking />,
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        path: routes.servicecomplaints.directLink,
        element: <ServiceCompaints />,

        children: [
          {
            path: routes.servicecomplaintsAddNew.relativeLink,
            element: <ServiceComplaintsAddNewPage />,
          },
          {
            path: routes.servicecomplaintsEdit.relativeLink,
            element: <ServiceComplaintsEditPage />,
          },
        ],
      },
      {
        path: routes.salesandmarketing.directLink,

        children: [
          {
            index: true,
            element: <SalesAndMarketingMainLayout />,
          },
          {
            path: routes.salesAndMarketingAnalytics.relativeLink,
            element: <SalesAndMarketingAnalyticsPage />,
          },
        ],
      },

      {
        path: routes?.marketing?.directLink,

        children: [
          {
            index: true,
            element: <MarketingPage />,
          },
          {
            path: routes?.marketingAnalytics?.relationLink,
            element: <MarketingAnalytics />,
          },
          {
            path: routes?.marketingWalkin.directLink,
            element: <MarketingWalkin />,
          },
        ],
      },

      {
        path: routes?.networking?.directLink,
        element: <NetworkingPage />,
        children: [
          {
            path: routes?.networkingadd?.relationLink,
            element: <NetworkingAdd />,
          },
        ],
      },

      {
        path: routes.ticketing.relativeLink,
        element: <TicketingPage />,
        children: [
          {
            path: routes.ticketingadd.relativeLink,
            element: <TicketingCreate />,
          },
          {
            path: routes.ticketingedit.relativeLink,
            element: <TicketingEdit />,
          },
        ],
      },

      // Task Management
      {
        path: routes.taskManagementMain.relativeLink,
        element: <TaskManagementPage />,
        children: [
          {
            path: routes.taskManagementAdd.directLink,
            element: <TaskManagementAddPage />,
          },
        ],
      },
      {
        path: routes.sap.relativeLink,
        element: <SAPPage />,
        children: [
          {
            path: routes.sapadd.relativeLink,
            element: <SAPAddPage />,
          },
          {
            path: routes.sapedit.relativeLink,
            element: <SAPEditPage />,
          },
        ],
      },
    ],
  },

  // customer dashboard
  {
    path: routes?.customerDashboard?.directLink,
    element: <CustomerDashboard />,
    children: [
      {
        path: routes?.customerDashboardOrders?.relativeLink,
        children: [
          { index: true, element: <CustomerOrderPage /> },
          {
            path: routes?.customerDashboardTracking?.relativeLink,
            element: <CustomerDashboardOrderTracking />,
          },
        ],
      },
    ],
  },
]);

const App = () => {
  return (
    <Provider store={store}>
      <Toaster />
      <RouterProvider router={router} />
    </Provider>
  );
};

export default App;
