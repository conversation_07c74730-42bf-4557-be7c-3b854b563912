import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import InputComponentWithErrorMessage from "../../../BasicUIElements/InputComponentWithErrorMessage";
import LoginHeader from "../Utlis/LoginHeader";
import AuthenticationTemplate from "../Utlis/AuthenticationTemplate";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import LoginFooter from "../Utlis/LoginFooter";
import logo from "../../../Assest/Logo/DharanAnalyticsBlackLogo.png";
import { routes } from "../../../Config/routesConfig";

const SignupCustomerLayout = () => {
  return <></>;
};

//      const dispatch = useDispatch();
//      const navigate = useNavigate();

//      const { content, ui } = useSelector((state) => {
//           return state.customerSignUp;
//      });
//      const {
//           firstName,
//           lastName,
//           mobileNumber,
//           email,
//           password,
//           confirmPassword,
//      } = content;

//      const clickHandler = () => {
//           dispatch(CustomerSignupSliceActions.updateContentBlurFirstName());
//           dispatch(CustomerSignupSliceActions.updateContentBlurLastName());
//           dispatch(CustomerSignupSliceActions.updateContentBlurMobileNumber());
//           dispatch(CustomerSignupSliceActions.updateContentBlurEmail());
//           dispatch(CustomerSignupSliceActions.updateContentBlurPassword());
//           dispatch(
//                CustomerSignupSliceActions.updateContentBlurConfirmPassword()
//           );
//           dispatch(customerSignUpThunk(content, navigate));
//      };

//      return (
//           <AuthenticationTemplate>
//                <div className=" w-full h-full flex flex-col justify-center items-center ">
//                     <div className=" w-fit h-fit flex flex-col gap-[2rem] mb-[1rem]">
//                          <div className=" md:hidden w-[155px] h-[49px] mb-5">
//                               <img
//                                    src={logo}
//                                    alt="Dharan company logo"
//                                    className=" w-full h-full object-contain"
//                               />
//                          </div>
//                          <LoginHeader>CREATE CUSTOMER ACCOUNT</LoginHeader>
//                     </div>

//                     <div className=" w-fit h-fit flex flex-col gap-[1.2rem]">
//                          <InputComponentWithErrorMessage
//                               type="text"
//                               name="First Name"
//                               placeholder="First name"
//                               className=" w-[300px] sm:w-[368px] h-[48px] ps-5  border border-[#D8E1F2] rounded text-sm font-semibold placeholder:font-poppins placeholder:text-xs placeholder:font-[400] placeholder:text-[#929BAC] "
//                               onChange={(e) => {
//                                    dispatch(
//                                         CustomerSignupSliceActions.updateContentFirstName(
//                                              e.target.value
//                                         )
//                                    );
//                               }}
//                               onBlur={(e) => {
//                                    dispatch(
//                                         CustomerSignupSliceActions.updateContentBlurFirstName()
//                                    );
//                               }}
//                               errorMessage={firstName.message}
//                               value={firstName.value}
//                               isValid={firstName.isValid}
//                               isTouched={firstName.isTouched}
//                          />
//                          <InputComponentWithErrorMessage
//                               type="text"
//                               name="Last Name"
//                               placeholder="Last name"
//                               className=" w-[300px] sm:w-[368px] h-[48px] ps-5  border border-[#D8E1F2] rounded text-sm font-semibold placeholder:font-poppins placeholder:text-xs placeholder:font-[400] placeholder:text-[#929BAC] "
//                               onChange={(e) => {
//                                    dispatch(
//                                         CustomerSignupSliceActions.updateContentLastName(
//                                              e.target.value
//                                         )
//                                    );
//                               }}
//                               onBlur={(e) => {
//                                    dispatch(
//                                         CustomerSignupSliceActions.updateContentBlurLastName()
//                                    );
//                               }}
//                               errorMessage={lastName.message}
//                               value={lastName.value}
//                               isValid={lastName.isValid}
//                               isTouched={lastName.isTouched}
//                          />
//                          <InputComponentWithErrorMessage
//                               type="text"
//                               name="Email Address"
//                               placeholder="Email Address"
//                               className=" w-[300px] sm:w-[368px] h-[48px] ps-5  border border-[#D8E1F2] rounded text-sm font-semibold placeholder:font-poppins placeholder:text-xs placeholder:font-[400] placeholder:text-[#929BAC] "
//                               onChange={(e) => {
//                                    dispatch(
//                                         CustomerSignupSliceActions.updateContentEmail(
//                                              e.target.value
//                                         )
//                                    );
//                               }}
//                               onBlur={(e) => {
//                                    dispatch(
//                                         CustomerSignupSliceActions.updateContentBlurEmail()
//                                    );
//                               }}
//                               errorMessage={email.message}
//                               value={email.value}
//                               isValid={email.isValid}
//                               isTouched={email.isTouched}
//                          />
//                          <InputComponentWithErrorMessage
//                               type="number"
//                               name="Mobile Number"
//                               placeholder="Mobile Number"
//                               className=" w-[300px] sm:w-[368px] h-[48px] ps-5  border border-[#D8E1F2] rounded text-sm font-semibold placeholder:font-poppins placeholder:text-xs placeholder:font-[400] placeholder:text-[#929BAC] "
//                               onChange={(e) => {
//                                    dispatch(
//                                         CustomerSignupSliceActions.updateContentMobileNumber(
//                                              e.target.value
//                                         )
//                                    );
//                               }}
//                               onBlur={(e) => {
//                                    dispatch(
//                                         CustomerSignupSliceActions.updateContentBlurMobileNumber()
//                                    );
//                               }}
//                               errorMessage={mobileNumber.message}
//                               value={mobileNumber.value}
//                               isValid={mobileNumber.isValid}
//                               isTouched={mobileNumber.isTouched}
//                          />

//                          <InputComponentWithErrorMessage
//                               type="password"
//                               name="Password"
//                               placeholder="Password"
//                               className=" w-[300px] sm:w-[368px] h-[48px] ps-5  border border-[#D8E1F2] rounded text-sm font-semibold placeholder:font-poppins placeholder:text-xs placeholder:font-[400] placeholder:text-[#929BAC] "
//                               onChange={(e) => {
//                                    dispatch(
//                                         CustomerSignupSliceActions.updateContentPassword(
//                                              e.target.value
//                                         )
//                                    );
//                               }}
//                               onBlur={(e) => {
//                                    dispatch(
//                                         CustomerSignupSliceActions.updateContentBlurPassword()
//                                    );
//                               }}
//                               errorMessage={password.message}
//                               value={password.value}
//                               isValid={password.isValid}
//                               isTouched={password.isTouched}
//                          />

//                          <InputComponentWithErrorMessage
//                               type="password"
//                               name="Confirm Password"
//                               placeholder="Confirm Password"
//                               className=" w-[300px] sm:w-[368px] h-[48px] ps-5  border border-[#D8E1F2] rounded text-sm font-semibold placeholder:font-poppins placeholder:text-xs placeholder:font-[400] placeholder:text-[#929BAC] "
//                               onChange={(e) => {
//                                    dispatch(
//                                         CustomerSignupSliceActions.updateContentConfirmPassword(
//                                              e.target.value
//                                         )
//                                    );
//                               }}
//                               onBlur={(e) => {
//                                    //    dispatch(
//                                    //         CustomerSignupSliceActions.updateContentBlurConfirmPassword()
//                                    //    );
//                               }}
//                               errorMessage={confirmPassword.message}
//                               value={confirmPassword.value}
//                               isValid={confirmPassword.isValid}
//                               isTouched={confirmPassword.isTouched}
//                          />

//                          <div className="w-full h-full">
//                               <ButtonComponent
//                                    isLoading={ui.createButtonIsLoading}
//                                    onClick={clickHandler}
//                                    className=" w-[179px] h-[49px] bg-mainLinearGradient rounded-[6px] font-poppins font-[500] text-[14px] text-[#fff]"
//                               >
//                                    Create account
//                               </ButtonComponent>
//                          </div>
//                          <LoginFooter
//                               isUser={false}
//                               route={routes.customerSignIn.directLink}
//                          />
//                     </div>
//                </div>
//           </AuthenticationTemplate>
//      );
// };

export default SignupCustomerLayout;
