import React from "react";
import AuthenticationTemplate from "../Utlis/AuthenticationTemplate";

import logo from "../../../Assest/Logo/DharanAnalyticsBlackLogo.png";
import LoginHeader from "../Utlis/LoginHeader";
import { routes } from "../../../Config/routesConfig";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

import LoginFooter from "../Utlis/LoginFooter";
import InputComponentWithErrorMessage from "../../../BasicUIElements/InputComponentWithErrorMessage";
import {
     CustomerResetPasswordSliceActions,
     customerResetPasswordThunk,
} from "../../../Store/AuthenticationSlices/CustomerSlices/CustomerResetPasswordSlice";

const ResetCustomerPasswordLayout = () => {
     const dispatch = useDispatch();
     const navigate = useNavigate();

     const { content } = useSelector((state) => {
          return state.customerResetPassword;
     });

     const { mobilePhone, password, oldPassword, confirmPassword } = content;

     const clickHandler = () => {
          dispatch(
               CustomerResetPasswordSliceActions.updateContentBlurPassword()
          );
          dispatch(
               CustomerResetPasswordSliceActions.updateContentBlurOldPassword()
          );
          dispatch(
               CustomerResetPasswordSliceActions.updateContentBlurMobilePhone()
          );
          dispatch(
               CustomerResetPasswordSliceActions.updateContentBlurConfirmPassword()
          );
          dispatch(customerResetPasswordThunk(content));
     };

     return (
          <AuthenticationTemplate>
               <div className=" w-full h-full flex flex-col justify-center items-center ">
                    <div className=" w-fit h-fit flex flex-col gap-[2rem] mb-[1rem] items-center">
                         <div className=" md:hidden w-[155px] h-[49px] mb-5">
                              <img
                                   src={logo}
                                   alt="Dharan company logo"
                                   className=" w-full h-full object-contain"
                              />
                         </div>
                         <LoginHeader>CUSTOMER RESET PASSWORD</LoginHeader>
                    </div>
                    <div className=" w-fit h-fit flex flex-col gap-[1.2rem]">
                         <InputComponentWithErrorMessage
                              type="number"
                              name="Mobile Phone"
                              placeholder="Mobile Phone"
                              className=" w-[300px] sm:w-[368px] h-[48px] ps-5  border border-[#D8E1F2] rounded text-sm font-semibold placeholder:font-poppins placeholder:text-xs placeholder:font-[400] placeholder:text-[#929BAC] "
                              onChange={(e) => {
                                   dispatch(
                                        CustomerResetPasswordSliceActions.updateContentMobilePhone(
                                             e.target.value
                                        )
                                   );
                              }}
                              onBlur={(e) => {
                                   dispatch(
                                        CustomerResetPasswordSliceActions.updateContentBlurMobilePhone()
                                   );
                              }}
                              errorMessage={mobilePhone.message}
                              value={mobilePhone.value}
                              isValid={mobilePhone.isValid}
                              isTouched={mobilePhone.isTouched}
                         />

                         <InputComponentWithErrorMessage
                              type="password"
                              name="Password"
                              placeholder="Old Password"
                              className=" w-[300px] sm:w-[368px] h-[48px] ps-5  border border-[#D8E1F2] rounded text-sm font-semibold placeholder:font-poppins placeholder:text-xs placeholder:font-[400] placeholder:text-[#929BAC] "
                              onChange={(e) => {
                                   dispatch(
                                        CustomerResetPasswordSliceActions.updateContentOldPassword(
                                             e.target.value
                                        )
                                   );
                              }}
                              onBlur={(e) => {
                                   dispatch(
                                        CustomerResetPasswordSliceActions.updateContentBlurOldPassword()
                                   );
                              }}
                              errorMessage={oldPassword.message}
                              value={oldPassword.value}
                              isValid={oldPassword.isValid}
                              isTouched={oldPassword.isTouched}
                         />

                         <InputComponentWithErrorMessage
                              type="password"
                              name="Password"
                              placeholder="Password"
                              className=" w-[300px] sm:w-[368px] h-[48px] ps-5  border border-[#D8E1F2] rounded text-sm font-semibold placeholder:font-poppins placeholder:text-xs placeholder:font-[400] placeholder:text-[#929BAC] "
                              onChange={(e) => {
                                   dispatch(
                                        CustomerResetPasswordSliceActions.updateContentPassword(
                                             e.target.value
                                        )
                                   );
                              }}
                              onBlur={(e) => {
                                   dispatch(
                                        CustomerResetPasswordSliceActions.updateContentBlurPassword()
                                   );
                              }}
                              errorMessage={password.message}
                              value={password.value}
                              isValid={password.isValid}
                              isTouched={password.isTouched}
                         />

                         <InputComponentWithErrorMessage
                              type="password"
                              name="Password"
                              placeholder="Confirm Password"
                              className=" w-[300px] sm:w-[368px] h-[48px] ps-5  border border-[#D8E1F2] rounded text-sm font-semibold placeholder:font-poppins placeholder:text-xs placeholder:font-[400] placeholder:text-[#929BAC] "
                              onChange={(e) => {
                                   dispatch(
                                        CustomerResetPasswordSliceActions.updateContentConfirmPassword(
                                             e.target.value
                                        )
                                   );
                              }}
                              onBlur={(e) => {}}
                              errorMessage={confirmPassword.message}
                              value={confirmPassword.value}
                              isValid={confirmPassword.isValid}
                              isTouched={confirmPassword.isTouched}
                         />

                         <div className="w-full h-full flex flex-row justify-start items-center">
                              <ButtonComponent
                                   onClick={clickHandler}
                                   className=" w-[179px] h-[49px] bg-mainLinearGradient rounded-[6px] font-poppins font-[500] text-[14px] text-[#fff]"
                              >
                                   Reset Password
                              </ButtonComponent>
                         </div>
                    </div>
               </div>
          </AuthenticationTemplate>
     );
};

export default ResetCustomerPasswordLayout;
