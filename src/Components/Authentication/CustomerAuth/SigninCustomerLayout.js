import React, { useState } from "react";
import AuthenticationTemplate from "../Utlis/AuthenticationTemplate";

import logo from "../../../Assest/Logo/DharanAnalyticsBlackLogo.png";
import LoginHeader from "../Utlis/LoginHeader";
import { routes } from "../../../Config/routesConfig";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

import LoginFooter from "../Utlis/LoginFooter";
import InputComponentWithErrorMessage from "../../../BasicUIElements/InputComponentWithErrorMessage";
import { isUsernameValid, passwordValid } from "../../../Config/FormConfig";
import { API_ENDPOINTS, api_headers, BaseURL } from "../../../API/APIConfig";
import { PostAPI } from "../../../API/PostAPI";
import {
  MasterSliceActions,
  setLoginAuth_LocalStorage,
} from "../../../Store/MasterSlice/MasterSlice";

const SigninCustomerLayout = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // State
  // Username
  const [username, setUsername] = useState({
    value: "",
    isValid: false,
    isTouched: false,
    message: "Username should not be empty",
  });
  // Password
  const [password, setPassword] = useState({
    value: "",
    isValid: false,
    isTouched: false,
    message: "Password should be atleast 6 characters",
  });
  // isLoading
  const [isLoading, setIsLoading] = useState(false);

  const clickHandler = async () => {
    setUsername((state) => {
      return { ...state, isTouched: true };
    });
    setPassword((state) => {
      return {
        ...state,
        isTouched: true,
      };
    });

    if (!username?.isValid && !password.isValid) {
      return;
    }

    setIsLoading(true);

    let response = await customer_signin_function({
      username: username?.value,
      password: password?.value,
    });

    if (response) {
      let loginData = response?.loginData;
      let authobj = {
        id: loginData?.user?._id,
        username: loginData?.user?.username,
        token: loginData?.token,
        mobilephone: loginData?.user?.mobilephone,
        email: loginData?.user?.email,
        role: loginData?.user?.role,
      };

      // If mustChangePassword is true
      if (loginData?.user?.mustChangePassword) {
        navigate(
          routes?.resetPassword?.directLink +
            "?" +
            routes?.resetPassword?.userIdSearchParam +
            "=" +
            authobj?.id +
            "&" +
            routes?.resetPassword?.tokenSeachParam +
            "=" +
            authobj?.token +
            "&" +
            routes?.resetPassword?.signFormSearchParam +
            "=customer"
        );
      } else {
        setLoginAuth_LocalStorage(authobj);
        dispatch(MasterSliceActions.updateAuthObj(authobj));
        navigate(routes.customerDashboardOrders?.directLink);
      }
    }

    //dispatch(customerSigninThunk(content, navigate));
    setIsLoading(false);
  };

  return (
    <AuthenticationTemplate>
      <div className=" w-full h-full sm:ps-0 ps-3 flex flex-col justify-center items-center ">
        <div className=" sm:w-fit w-full h-fit  flex flex-col gap-[2rem] mb-[1rem]">
          <div className=" md:hidden w-[155px] h-[49px] mb-5">
            <img
              src={logo}
              alt="Dharan company logo"
              className=" w-full h-full object-contain"
            />
          </div>
          <LoginHeader>CUSTOMER SIGN IN</LoginHeader>
        </div>
        <div className=" sm:w-fit w-full h-fit flex flex-col gap-[1.2rem]">
          <InputComponentWithErrorMessage
            type="text"
            name="User Name"
            placeholder="User name"
            className=" w-[80%] sm:w-[368px] h-[48px] ps-5  border border-[#D8E1F2] rounded text-sm font-semibold placeholder:font-poppins placeholder:text-xs placeholder:font-[400] placeholder:text-[#929BAC] "
            onChange={(e) => {
              setUsername((state) => {
                return {
                  ...state,
                  value: e.target.value,
                  isValid: isUsernameValid(e.target.value),
                };
              });
            }}
            onBlur={(e) => {
              setUsername((state) => {
                return { ...state, isTouched: true };
              });
            }}
            errorMessage={username.message}
            value={username.value}
            isValid={username?.isValid}
            isTouched={username.isTouched}
          />

          <InputComponentWithErrorMessage
            type="password"
            name="Password"
            placeholder="Password"
            className=" w-[80%] sm:w-[368px] h-[48px] ps-5  border border-[#D8E1F2] rounded text-sm font-semibold placeholder:font-poppins placeholder:text-xs placeholder:font-[400] placeholder:text-[#929BAC] "
            onChange={(e) => {
              setPassword((state) => {
                return {
                  ...state,
                  value: e.target.value,
                  isValid: passwordValid(e.target.value),
                };
              });
            }}
            onBlur={(e) => {
              setPassword((state) => {
                return {
                  ...state,
                  isTouched: true,
                };
              });
            }}
            errorMessage={password.message}
            value={password.value}
            isValid={password.isValid}
            isTouched={password.isTouched}
          />

          <div className="w-full h-full flex flex-row justify-between items-center">
            <ButtonComponent
              isLoading={isLoading}
              onClick={clickHandler}
              className="  sm:w-[179px] w-[125px]  sm:h-[49px] h-[40px] bg-mainLinearGradient rounded-[6px] font-poppins font-[500] sm:text-[14px] text-[12px] text-[#fff]"
            >
              Log In
            </ButtonComponent>
            {/* <ButtonComponent
              onClick={() => {
                navigate(routes.customerResetPassword.directLink);
              }}
              className="font-poppins font-[500] text-[14px] hover:underline"
            >
              Reset Password ?
            </ButtonComponent> */}
          </div>
          <LoginFooter
            isUser={false}
            route={routes.customerSignUp.directLink}
          />
        </div>
      </div>
    </AuthenticationTemplate>
  );
};

export default SigninCustomerLayout;

const customer_signin_function = async (obj) => {
  let url = new URL(BaseURL?.mainURl + API_ENDPOINTS?.userSignin);

  let headers = { ...api_headers };

  let body = obj;

  let response = await PostAPI(url, body, headers);

  if (response?.ok) {
    let responsedata = await response?.json();
    return responsedata;
  }

  return null;
};
