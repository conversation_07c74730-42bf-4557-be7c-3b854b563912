import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import InputComponentWithErrorMessage from "../../../BasicUIElements/InputComponentWithErrorMessage";
import LoginHeader from "../Utlis/LoginHeader";
import AuthenticationTemplate from "../Utlis/AuthenticationTemplate";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import LoginFooter from "../Utlis/LoginFooter";

import logo from "../../../Assest/Logo/DharanAnalyticsBlackLogo.png";
import { isUsernameValid, passwordValid } from "../../../Config/FormConfig";
import { API_ENDPOINTS, api_headers, BaseURL } from "../../../API/APIConfig";
import { PostAPI } from "../../../API/PostAPI";
import {
  MasterSliceActions,
  setLoginAuth_LocalStorage,
  update_auth_obj_master_slice_thunk,
} from "../../../Store/MasterSlice/MasterSlice";

import { routes } from "../../../Config/routesConfig";
import toast from "react-hot-toast";
const SigninUserLayout = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const [email, setEmail] = useState({
    value: "",
    isValid: false,
    isTouched: false,
    errorMessage: "Please enter a valid email",
  });

  const [password, setPassword] = useState({
    value: "",
    isValid: false,
    isTouched: false,
    errorMessage: "password should be atleast 6 characters",
  });

  const [isLoading, setIsLoading] = useState(false);

  const signInButtonClickHandler = async () => {
    setEmail((state) => {
      return { ...state, isTouched: true };
    });
    setPassword((state) => {
      return { ...state, isTouched: true };
    });

    if (!email?.isValid || !password?.isValid) {
      toast("Fill the authentication form");
      return;
    }

    setIsLoading(true);
    let toast_id = toast.loading("Logging in", { position: "top-center" });

    let response = await user_signin_function({
      username: email?.value,
      password: password.value,
    });

    if (response?.ok) {
      let responsedata = await response.json();
      let loginData = responsedata?.loginData;

      let authobj = {
        id: loginData?.user?._id,
        username: loginData?.user?.username,
        token: loginData?.token,
        mobilephone: loginData?.user?.mobilephone,
        email: loginData?.user?.email,
        role: loginData?.user?.role,
      };

      let access = {};

      if (responsedata?.accessesData) {
        let accessesData = responsedata?.accessesData?.user?.access;

        // Old
        access = {
          user: {
            read: accessesData?.user?.Read,
            delete: accessesData?.user?.deletes,
            edit: accessesData?.user?.edit,
            insights: accessesData?.user?.insigths,
            write: accessesData?.user?.write,
          },
          purchase: {
            read: accessesData?.Purchases?.Read,
            delete: accessesData?.Purchases?.deletes,
            edit: accessesData?.Purchases?.edit,
            insights: accessesData?.Purchases?.insigths,
            write: accessesData?.Purchases?.write,
          },
          sales: {
            read: accessesData?.Sales?.Read,
            delete: accessesData?.Sales?.deletes,
            edit: accessesData?.Sales?.edit,
            insights: accessesData?.Sales?.insigths,
            write: accessesData?.Sales?.write,
          },
          marketing: {
            read: accessesData?.marketing?.Read,
            write: accessesData?.marketing?.write,
            insights: accessesData?.marketing?.insigths,
            edit: accessesData?.marketing?.edit,
            delete: accessesData?.marketing?.deletes,
          },
          orders: {
            read: accessesData?.Orders?.Read,
            write: accessesData?.Orders?.write,
            insights: accessesData?.Orders?.insigths,
            edit: accessesData?.Orders?.edit,
            delete: accessesData?.Orders?.deletes,
          },
          customers: {
            read: accessesData?.customers?.Read,
            write: accessesData?.customers?.write,
            insights: accessesData?.customers?.insigths,
            edit: accessesData?.customers?.edit,
            delete: accessesData?.customers?.deletes,
          },
          networking: {
            read: accessesData?.networking?.Read,
            write: accessesData?.networking?.write,
            insights: accessesData?.networking?.insigths,
            edit: accessesData?.networking?.edit,
            delete: accessesData?.networking?.deletes,
          },
          serviceComplaints: {
            read: accessesData?.serviceComplaints?.Read,
            write: accessesData?.serviceComplaints?.write,
            insights: accessesData?.serviceComplaints?.insigths,
            edit: accessesData?.serviceComplaints?.edit,
            delete: accessesData?.serviceComplaints?.deletes,
          },
          tickets: {
            read: accessesData?.tickets?.Read,
            write: accessesData?.tickets?.write,
            insights: accessesData?.tickets?.insigths,
            edit: accessesData?.tickets?.edit,
            delete: accessesData?.tickets?.deletes,
          },
          taskManagement: {
            read: accessesData?.taskManagement.Read,
            write: accessesData?.taskManagement.write,
            insights: accessesData?.taskManagement.insigths,
            edit: accessesData?.taskManagement.edit,
            delete: accessesData?.taskManagement.deletes,
          },
        };
      }

      // If mustChangePassword is true
      if (loginData?.user?.mustChangePassword) {
        toast.success(
          "As you are logged into this application for a first time, Please change your password for safety purpose",
          {
            id: toast_id,
            duration: 5000,
          }
        );
        navigate(
          routes?.resetPassword?.directLink +
            "?" +
            routes?.resetPassword?.userIdSearchParam +
            "=" +
            authobj?.id +
            "&" +
            routes?.resetPassword?.tokenSeachParam +
            "=" +
            authobj?.token +
            "&" +
            routes?.resetPassword?.signFormSearchParam +
            "=user"
        );
      } else {
        setLoginAuth_LocalStorage(authobj, access);
        dispatch(MasterSliceActions.updateAuthObj(authobj));
        dispatch(update_auth_obj_master_slice_thunk(authobj));
        dispatch(MasterSliceActions?.updateAccess(access));
        navigate(routes?.purchase?.directLink);
        toast.success("Getting into application", { id: toast_id });
      }
    } else {
      const responsedate = await response.json();
      toast.error(responsedate?.error, {
        id: toast_id,
      });
    }

    setIsLoading(false);
  };

  return (
    <AuthenticationTemplate>
      <div className=" w-full h-full flex flex-col justify-center items-center ">
        <div className=" sm:w-fit w-full sm:ps-0 ps-3 h-fit flex flex-col gap-[2rem]">
          <div className=" md:hidden w-[155px] h-[49px] mb-5">
            <img
              src={logo}
              alt="Dharan company logo"
              className=" w-full h-full object-contain"
            />
          </div>
          <LoginHeader>USER SIGN IN</LoginHeader>
          <div className=" sm:w-fit w-full h-fit flex flex-col gap-[1.2rem] ">
            <InputComponentWithErrorMessage
              type="text"
              name="email"
              placeholder="Username"
              className="w-[80%] sm:w-[368px] h-[48px] ps-5  border border-[#D8E1F2] rounded text-sm font-semibold placeholder:font-poppins placeholder:text-xs placeholder:font-[400] placeholder:text-[#929BAC] "
              errorMessage={email.errorMessage}
              value={email.value}
              onChange={(e) => {
                setEmail((state) => {
                  return {
                    ...state,
                    value: e.target.value,
                    isValid: isUsernameValid(e.target.value),
                  };
                });
              }}
              onBlur={() => {
                setEmail((state) => {
                  return { ...state, isTouched: true };
                });
              }}
              isValid={email.isValid}
              isTouched={email.isTouched}
            />
            <InputComponentWithErrorMessage
              type="password"
              name="email"
              placeholder="Password"
              className="w-[80%] sm:w-[368px] h-[48px] ps-5  border border-[#D8E1F2] rounded text-sm font-semibold placeholder:font-poppins placeholder:text-xs placeholder:font-[400] placeholder:text-[#929BAC] "
              errorMessage={password.errorMessage}
              value={password.value}
              onChange={(e) => {
                setPassword((state) => {
                  return {
                    ...state,
                    value: e.target.value,
                    isValid: passwordValid(e.target.value),
                  };
                });
              }}
              onBlur={() => {
                setPassword((state) => {
                  return { ...state, isTouched: true };
                });
              }}
              isValid={password.isValid}
              isTouched={password.isTouched}
            />
          </div>
          <div className="w-full h-fit  flex flex-row justify-between  items-center ">
            <ButtonComponent
              isLoading={isLoading}
              onClick={signInButtonClickHandler}
              className=" sm:w-[179px] w-[125px]  sm:h-[49px] h-[40px] bg-mainLinearGradient rounded-[6px] font-poppins font-[700] sm:text-[14px] text-[12px] text-[#fff]"
            >
              Sign in
            </ButtonComponent>
            <div></div>
            {/* 
            <h5 className=" font-poppins font-[500] sm:text-[14px] text-[10px] text-[#019BA2] cursor-pointer">
              Forgot Password?
            </h5> */}
          </div>
          <LoginFooter isUser={true} route="/users/signup" />
        </div>
      </div>
    </AuthenticationTemplate>
  );
};

export default SigninUserLayout;

const user_signin_function = async (obj) => {
  let url = new URL(BaseURL?.mainURl + API_ENDPOINTS?.userSignin);

  let headers = { ...api_headers };

  let body = obj;

  let response = await PostAPI(url, body, headers);

  return response;
};
