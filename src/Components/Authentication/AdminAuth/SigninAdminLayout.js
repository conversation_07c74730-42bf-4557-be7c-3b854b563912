import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import InputComponentWithErrorMessage from "../../../BasicUIElements/InputComponentWithErrorMessage";
import LoginHeader from "../Utlis/LoginHeader";
import AuthenticationTemplate from "../Utlis/AuthenticationTemplate";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import LoginFooter from "../Utlis/LoginFooter";
import {
  AdminSigninSliceActions,
  AuthenticationAdminSignInThunk,
} from "../../../Store/AuthenticationSlices/AdminSlices/AdminSigninSlice";
import logo from "../../../Assest/Logo/DharanAnalyticsBlackLogo.png";
import toast from "react-hot-toast";
import { API_ENDPOINTS, api_headers, BaseURL } from "../../../API/APIConfig";
import { PostAPI } from "../../../API/PostAPI";
import {
  Master_Roles,
  MasterSliceActions,
  setLoginAuth_LocalStorage,
  update_auth_obj_master_slice_thunk,
} from "../../../Store/MasterSlice/MasterSlice";
import { routes } from "../../../Config/routesConfig";

const SigninAdminLayout = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const { content, ui } = useSelector((state) => {
    return state.adminSignIn;
  });

  const { username, password } = content;
  const { signinButtonIsLoading } = ui;

  const signInButtonClickHandler = async () => {
    dispatch(AdminSigninSliceActions.usernameUpdateIsTouched());
    dispatch(AdminSigninSliceActions.passwordUpdateIsTouched());

    if (!username?.isValid || !password.isValid) {
      toast("Fill the authentication form");
      return;
    }

    setIsLoading(true);
    let toast_id = toast.loading("Logging in", { position: "top-center" });

    const url = new URL(BaseURL.mainURl + API_ENDPOINTS.adminSignin);
    const bodyObj = {
      username: username.value,
      password: password.value,
    };

    let headers = {
      ...api_headers,
    };

    let response = await PostAPI(url, bodyObj, headers);

    if (response.ok) {
      const responsedate = await response.json();
      let data = responsedate?.data;
      let user = data?.user;
      let token = data?.token;

      let obj = {
        token: token,
        id: user?._id,
        email: user?.email,
        username: user?.username,
        mobilephone: user?.mobile,
        role: Master_Roles.admin,
      };

      if (obj?.role?.toLocaleLowerCase() !== "Admin"?.toLocaleLowerCase()) {
        toast.error("You are not admin, please use your login form", {
          id: toast_id,
        });
        setIsLoading(false);
        return;
      }

      let access = {
        user: {
          read: true,
          write: true,
          insights: true,
          edit: true,
          delete: true,
        },
        purchase: {
          read: true,
          write: true,
          insights: true,
          edit: true,
          delete: true,
        },
        sales: {
          read: true,
          write: true,
          insights: true,
          edit: true,
          delete: true,
        },
        marketing: {
          read: true,
          write: true,
          insights: true,
          edit: true,
          delete: true,
        },
        orders: {
          read: true,
          write: true,
          insights: true,
          edit: true,
          delete: true,
        },
        customers: {
          read: true,
          write: true,
          insights: true,
          edit: true,
          delete: true,
        },
        networking: {
          read: true,
          write: true,
          insights: true,
          edit: true,
          delete: true,
        },
        serviceComplaints: {
          read: true,
          write: true,
          insights: true,
          edit: true,
          delete: true,
        },
        tickets: {
          read: true,
          write: true,
          insights: true,
          edit: true,
          delete: true,
        },
        taskManagement: {
          read: true,
          write: true,
          insights: true,
          edit: true,
          delete: true,
        },
      };

      setIsLoading(false);
      // If mustChangePassword is true
      if (data?.user?.mustChangePassword) {
        toast.success(
          "As you are logged into this application for a first time, Please change your password for safety purpose",
          {
            id: toast_id,
            duration: 5000,
          }
        );
        navigate(
          routes?.resetPassword?.directLink +
            "?" +
            routes?.resetPassword?.userIdSearchParam +
            "=" +
            obj?.id +
            "&" +
            routes?.resetPassword?.tokenSeachParam +
            "=" +
            obj?.token +
            "&" +
            routes?.resetPassword?.signFormSearchParam +
            "=admin"
        );
      } else {
        dispatch(MasterSliceActions.updateAuthObj(obj));
        setLoginAuth_LocalStorage(obj, access);
        dispatch(MasterSliceActions?.updateAccess(access));
        dispatch(update_auth_obj_master_slice_thunk(obj));
        navigate(routes.userManagement.directLink);
        toast.success("Getting into application", { id: toast_id });
      }
    } else {
      const responsedate = await response.json();
      toast.error(responsedate?.error, {
        id: toast_id,
      });
      setIsLoading(false);
    }

    dispatch(AuthenticationAdminSignInThunk(content, navigate));
  };

  return (
    <AuthenticationTemplate>
      <div className=" w-full h-full flex flex-col justify-center items-center ">
        <div className=" sm:w-fit w-full sm:ps-0 ps-3 h-fit flex flex-col gap-[2rem]">
          <div className=" md:hidden w-[155px] h-[49px] mb-5">
            <img
              src={logo}
              alt="Dharan company logo"
              className=" w-full h-full object-contain"
            />
          </div>
          <LoginHeader>ADMIN SIGN IN</LoginHeader>

          <div className=" sm:w-fit w-full  h-fit flex flex-col gap-[1.2rem] ">
            <InputComponentWithErrorMessage
              type="text"
              name="username"
              placeholder="Username"
              className=" w-[80%] sm:w-[368px] h-[48px] ps-5  border border-[#D8E1F2] rounded text-sm font-semibold placeholder:font-poppins placeholder:text-xs placeholder:font-[400] placeholder:text-[#929BAC] "
              errorMessage={username.errorMessage}
              value={username.value}
              onChange={(e) => {
                dispatch(
                  AdminSigninSliceActions.usernameUpdateValue(e.target.value)
                );
              }}
              onBlur={() => {
                dispatch(AdminSigninSliceActions.usernameUpdateIsTouched());
              }}
              isValid={username.isValid}
              isTouched={username?.isTouched}
            />
            <InputComponentWithErrorMessage
              type="password"
              name="email"
              placeholder="Password"
              className="w-[80%] sm:w-[368px] h-[48px] ps-5  border border-[#D8E1F2] rounded text-sm font-semibold placeholder:font-poppins placeholder:text-xs placeholder:font-[400] placeholder:text-[#929BAC] "
              errorMessage={password.errorMessage}
              value={password.value}
              onChange={(e) => {
                dispatch(
                  AdminSigninSliceActions.passwordUpdateValue(e.target.value)
                );
              }}
              onBlur={() => {
                dispatch(AdminSigninSliceActions.passwordUpdateIsTouched());
              }}
              isValid={password.isValid}
              isTouched={password.isTouched}
            />
          </div>

          <div className="w-full h-full  flex flex-row justify-between  items-center  ">
            <ButtonComponent
              isLoading={isLoading}
              onClick={signInButtonClickHandler}
              className=" sm:w-[179px] w-[125px]  sm:h-[49px] h-[40px] bg-mainLinearGradient rounded-[6px] font-poppins font-[700] sm:text-[14px] text-[12px] text-[#fff]"
            >
              Sign in
            </ButtonComponent>

            {/* <h5 className=" font-poppins font-[500] text-[14px] text-[#019BA2] cursor-pointer">
                                   Forgot Password?
                              </h5> */}
            <div></div>
          </div>

          <LoginFooter isUser={true} route="/admin/signup" />
        </div>
      </div>
    </AuthenticationTemplate>
  );
};

export default SigninAdminLayout;
