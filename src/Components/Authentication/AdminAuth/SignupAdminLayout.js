import React from "react";
import InputComponentWithErrorMessage from "../../../BasicUIElements/InputComponentWithErrorMessage";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import LoginFooter from "../Utlis/LoginFooter";
import AuthenticationTemplate from "../Utlis/AuthenticationTemplate";
import LoginHeader from "../Utlis/LoginHeader";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
     AdminSignupSliceActions,
     AuthenticationAdminSignUpThunk,
} from "../../../Store/AuthenticationSlices/AdminSlices/AdminSignupSlice";
import logo from "../../../Assest/Logo/DharanAnalyticsBlackLogo.png";
const SignupAdminLayout = () => {
     const navigate = useNavigate();
     const dispatch = useDispatch();

     const { content, ui } = useSelector((state) => {
          return state.adminSignUp;
     });
     const { username, mobilephone, password } = content;
     const { signupButtonIsLoading } = ui;

     const clickHandler = () => {
          dispatch(AdminSignupSliceActions.mobilePhoneUpdateIsTouched());
          dispatch(AdminSignupSliceActions.passwordUpdateIsTouched());
          dispatch(AdminSignupSliceActions.usernameUpdateIsTouched());
          dispatch(AuthenticationAdminSignUpThunk(content, navigate));
     };

     return (
          <AuthenticationTemplate>
               <div className=" w-full h-full flex flex-col justify-center items-center ">
                    <div className=" w-fit h-fit flex flex-col gap-[2rem]">
                         <div className=" md:hidden w-[155px] h-[49px] mb-5">
                              <img
                                   src={logo}
                                   alt="Dharan company logo"
                                   className=" w-full h-full object-contain"
                              />
                         </div>
                         <LoginHeader>CREATE ADMIN ACCOUNT</LoginHeader>
                         <div className=" w-fit h-fit flex flex-col gap-[1.2rem]">
                              <InputComponentWithErrorMessage
                                   type="text"
                                   name="Username"
                                   placeholder="User name"
                                   className=" w-[300px] sm:w-[368px] h-[48px] ps-5  border border-[#D8E1F2] rounded text-sm font-semibold placeholder:font-poppins placeholder:text-xs placeholder:font-[400] placeholder:text-[#929BAC] "
                                   errorMessage={username.errorMessage}
                                   value={username.value}
                                   onChange={(e) => {
                                        dispatch(
                                             AdminSignupSliceActions.usernameUpdateValue(
                                                  e.target.value
                                             )
                                        );
                                   }}
                                   onBlur={(e) => {
                                        dispatch(
                                             AdminSignupSliceActions.usernameUpdateIsTouched()
                                        );
                                   }}
                                   isValid={username.isValid}
                                   isTouched={username.isTouched}
                              />

                              <InputComponentWithErrorMessage
                                   type="number"
                                   name="phone"
                                   placeholder="Phone"
                                   className=" w-[300px] sm:w-[368px] h-[48px] ps-5  border border-[#D8E1F2] rounded text-sm font-semibold placeholder:font-poppins placeholder:text-xs placeholder:font-[400] placeholder:text-[#929BAC] "
                                   errorMessage={mobilephone.errorMessage}
                                   value={mobilephone.value}
                                   onChange={(e) => {
                                        dispatch(
                                             AdminSignupSliceActions.mobilePhoneValueUpdate(
                                                  e.target.value
                                             )
                                        );
                                   }}
                                   onBlur={(e) => {
                                        dispatch(
                                             AdminSignupSliceActions.mobilePhoneUpdateIsTouched()
                                        );
                                   }}
                                   isValid={mobilephone.isValid}
                                   isTouched={mobilephone.isTouched}
                              />
                              <InputComponentWithErrorMessage
                                   type="password"
                                   name="email"
                                   placeholder="Password"
                                   className=" w-[300px] sm:w-[368px] h-[48px] ps-5  border border-[#D8E1F2] rounded text-sm font-semibold placeholder:font-poppins placeholder:text-xs placeholder:font-[400] placeholder:text-[#929BAC] "
                                   errorMessage={password.errorMessage}
                                   value={password.value}
                                   onChange={(e) => {
                                        dispatch(
                                             AdminSignupSliceActions.passwordUpdateValue(
                                                  e.target.value
                                             )
                                        );
                                   }}
                                   onBlur={() => {
                                        dispatch(
                                             AdminSignupSliceActions.passwordUpdateIsTouched()
                                        );
                                   }}
                                   isValid={password.isValid}
                                   isTouched={password.isTouched}
                              />
                         </div>
                         <div className="w-full h-full">
                              <ButtonComponent
                                   isLoading={signupButtonIsLoading}
                                   onClick={clickHandler}
                                   className=" w-[179px] h-[49px] bg-mainLinearGradient rounded-[6px] font-poppins font-[500] text-[14px] text-[#fff]"
                              >
                                   Create account
                              </ButtonComponent>
                         </div>
                         <LoginFooter isUser={false} route="/admin/signin" />
                    </div>
               </div>
          </AuthenticationTemplate>
     );
};

export default SignupAdminLayout;
