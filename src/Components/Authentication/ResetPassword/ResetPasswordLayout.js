import React, { useState } from "react";

// UI layout
import AuthenticationTemplate from "../Utlis/AuthenticationTemplate";
import LoginHeader from "../Utlis/LoginHeader";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import InputComponentWithErrorMessage from "../../../BasicUIElements/InputComponentWithErrorMessage";
//asset
import logo from "../../../Assest/Logo/DharanAnalyticsBlackLogo.png";

// Hooks
import { useNavigate, useSearchParams } from "react-router-dom";
import { routes } from "../../../Config/routesConfig";
import { passwordValid } from "../../../Config/FormConfig";
import { API_ENDPOINTS, api_headers, BaseURL } from "../../../API/APIConfig";
import { PostAPI } from "../../../API/PostAPI";
import toast from "react-hot-toast";

const ResetPasswordLayout = () => {
  const navigate = useNavigate();

  const [searchParams] = useSearchParams();
  let userId = searchParams.get(routes?.resetPassword?.userIdSearchParam);
  let signinform = searchParams.get(routes?.resetPassword?.signFormSearchParam);
  let token = searchParams.get(routes?.resetPassword?.tokenSeachParam);

  // State Management
  const [oldPassword, setOldPassword] = useState({
    value: "",
    isValid: false,
    isTouched: false,
    errorMessage: "Password should be more than 6 characters",
  });
  const [newPassword, setNewPassword] = useState({
    value: "",
    isValid: false,
    isTouched: false,
    errorMessage: "Password should be more than 6 characters",
  });
  const [confirmNewPassword, setConfirmNewPassword] = useState({
    value: "",
    isValid: false,
    isTouched: false,
    errorMessage: "Not same",
  });

  const [isLoading, setIsLoading] = useState(false);

  const clickHandler = async () => {
    setOldPassword((state) => {
      return {
        ...state,
        isTouched: true,
      };
    });
    setNewPassword((state) => {
      return {
        ...state,
        isTouched: true,
      };
    });
    setConfirmNewPassword((state) => {
      return {
        ...state,
        isTouched: true,
      };
    });

    if (
      !oldPassword?.isValid ||
      !newPassword?.isValid ||
      !confirmNewPassword?.isValid
    ) {
      return;
    }

    setIsLoading(true);

    let response = await resetPassword_function(
      oldPassword?.value,
      newPassword?.value,
      userId,
      token
    );

    let responsedata = await response.json();

    if (response.ok) {
      if (responsedata) {
        if (signinform === "user") {
          navigate("/users/signin");
        }
        if (signinform === "customer") {
          navigate("/customer/signin");
        }
        if (signinform === "admin") {
          navigate("/admin/signin");
        }
      }
      toast.success("Your password has updated successfully", {
        duration: 4000,
      });
      toast.success("Login with your new password", {
        duration: 5000,
      });
    } else {
      const responsedate = await response.json();
      toast.error(responsedate?.error, {});
    }
    setIsLoading(false);
  };

  return (
    <AuthenticationTemplate>
      <div className=" w-full h-full flex flex-col justify-center items-center ">
        <div className=" w-fit h-fit flex flex-col gap-[1.2rem]">
          <div className=" w-fit h-fit flex flex-col gap-[2rem]">
            <div className=" md:hidden w-[155px] h-[49px] mb-5">
              <img
                src={logo}
                alt="Dharan company logo"
                className=" w-full h-full object-contain"
              />
            </div>
            <LoginHeader>RESET PASSWORD</LoginHeader>
          </div>
          <InputComponentWithErrorMessage
            type="text"
            name="Old Password"
            placeholder="Old Password"
            className="w-[300px] sm:w-[368px] h-[48px] ps-5  border border-[#D8E1F2] rounded text-sm font-semibold placeholder:font-poppins placeholder:text-xs placeholder:font-[400] placeholder:text-[#929BAC] "
            errorMessage={oldPassword.errorMessage}
            value={oldPassword.value}
            onChange={(e) => {
              setOldPassword((state) => {
                return {
                  ...state,
                  value: e.target.value,
                  isValid: passwordValid(e.target.value),
                };
              });
            }}
            onBlur={() => {
              setOldPassword((state) => {
                return {
                  ...state,
                  isTouched: true,
                };
              });
            }}
            isValid={oldPassword.isValid}
            isTouched={oldPassword.isTouched}
          />
          <InputComponentWithErrorMessage
            type="password"
            name="new password"
            placeholder="New Password"
            className="w-[300px] sm:w-[368px] h-[48px] ps-5  border border-[#D8E1F2] rounded text-sm font-semibold placeholder:font-poppins placeholder:text-xs placeholder:font-[400] placeholder:text-[#929BAC] "
            errorMessage={newPassword.errorMessage}
            value={newPassword.value}
            onChange={(e) => {
              setNewPassword((state) => {
                return {
                  ...state,
                  value: e.target.value,
                  isValid: passwordValid(e.target.value),
                };
              });
            }}
            onBlur={() => {
              setNewPassword((state) => {
                return {
                  ...state,
                  isTouched: true,
                };
              });
            }}
            isValid={newPassword.isValid}
            isTouched={newPassword.isTouched}
          />
          <InputComponentWithErrorMessage
            type="password"
            name="confirm new password"
            placeholder="Confirm New Password"
            className="w-[300px] sm:w-[368px] h-[48px] ps-5  border border-[#D8E1F2] rounded text-sm font-semibold placeholder:font-poppins placeholder:text-xs placeholder:font-[400] placeholder:text-[#929BAC] "
            errorMessage={confirmNewPassword.errorMessage}
            value={confirmNewPassword.value}
            onChange={(e) => {
              setConfirmNewPassword((state) => {
                return {
                  ...state,
                  value: e.target.value,
                  isValid: e.target?.value === newPassword?.value,
                };
              });
            }}
            onBlur={() => {
              setConfirmNewPassword((state) => {
                return {
                  ...state,
                  isTouched: true,
                };
              });
            }}
            isValid={confirmNewPassword.isValid}
            isTouched={confirmNewPassword.isTouched}
          />
          <div className="w-full h-full">
            <ButtonComponent
              isLoading={isLoading}
              onClick={clickHandler}
              className=" w-[179px] h-[49px] bg-mainLinearGradient rounded-[6px] font-poppins font-[500] text-[14px] text-[#fff]"
            >
              Reset Password
            </ButtonComponent>
          </div>
        </div>
      </div>
    </AuthenticationTemplate>
  );
};

export default ResetPasswordLayout;

const resetPassword_function = async (
  oldPassword,
  newPassword,
  userId,
  token
) => {
  let url = new URL(
    BaseURL?.mainURl + API_ENDPOINTS?.resetPassword + "/" + userId
  );

  let headers = { ...api_headers, Authorization: token };

  let body = {
    oldPassword: oldPassword,
    newPassword: newPassword,
  };

  let response = await PostAPI(url, body, headers);

  return response;
};
