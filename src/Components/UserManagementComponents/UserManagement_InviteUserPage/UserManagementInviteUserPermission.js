import React from "react";
import CheckBoxComponent from "../../../BasicUIElements/CheckBoxComponent";
import { useDispatch, useSelector } from "react-redux";
import { UserPermissionTableConfig } from "../../../Config/UserConfig";
import { UserInviteUserSliceActions } from "../../../Store/UserSlices/UserInviteUser";

const RowElement = ({
  title = "",
  value = [true, false, false, false],
  index = true,
  updateFunction = () => {},
}) => {
  const clickHandler = (index) => {
    updateFunction(title, index);
  };

  return (
    <div
      className={` w-full h-[40px] bg-[#F6F7F8] flex flex-row justify-between ${
        index ? "bg-[#f0f0f0]" : "bg-[#fff]"
      } `}
    >
      <div className=" w-fit h-full flex flex-col justify-center font-[600] font-lato text-[#172B70] ps-4 text-[12px]">
        {title}
      </div>
      <div className=" w-fit h-full flex flex-row justify-center items-center gap-[1.5rem] me-[5rem]">
        {value.map((item, index) => {
          return (
            <div className=" w-[50px] h-full flex flex-row justify-center items-center">
              <CheckBoxComponent
                value={item}
                onClick={() => {
                  clickHandler(index);
                }}
              />
            </div>
          );
        })}
      </div>
    </div>
  );
};

const UserManagementInviteUserPermission = () => {
  const dispatch = useDispatch();

  ///---------------------------------------------
  const permissionValues = useSelector((state) => {
    return state.userinviteuser.permissionValues;
  });

  const permissionClickHandler = (title, index) => {
    dispatch(
      UserInviteUserSliceActions.permissionValuesChange({
        title,
        index,
      })
    );
  };

  return (
    <div className=" w-full h-fit flex flex-row justify-center">
      <div className=" w-[550px] h-[240px] rounded-[8px] overflow-hidden flex flex-col justify-evenly border-2 border-[#00000040] border-opacity-10  ">
        <div className=" w-full h-[40px] flex  flex-row  justify-between border-b-2">
          <div></div>

          <div className=" w-fit h-full flex flex-row justify-center items-center gap-[1.5rem] me-[5rem]">
            <h1 className=" w-[50px] text-center h-fit font-[600] font-lato text-[12px] text-[#577496]">
              Lead
            </h1>
            <h1 className=" w-[50px] text-center h-fit  font-[600] font-lato text-[12px] text-[#577496]">
              Add New
            </h1>
            <h1 className=" w-[50px] text-center h-fit  font-[600] font-lato text-[12px] text-[#577496]">
              Edit
            </h1>
            <h1 className=" w-[50px] text-center  h-fit  font-[600] font-lato text-[12px] text-[#577496]">
              Delete
            </h1>
          </div>
        </div>

        <RowElement
          title={UserPermissionTableConfig.leads}
          index={true}
          value={permissionValues.leads}
          updateFunction={permissionClickHandler}
        />
        <RowElement
          title={UserPermissionTableConfig.customers}
          index={false}
          value={permissionValues.customers}
          updateFunction={permissionClickHandler}
        />
        <RowElement
          title={UserPermissionTableConfig.orderDetails}
          index={true}
          value={permissionValues.orderDetails}
          updateFunction={permissionClickHandler}
        />
        <RowElement
          title={UserPermissionTableConfig.products}
          index={false}
          value={permissionValues.products}
          updateFunction={permissionClickHandler}
        />
        <RowElement
          title={UserPermissionTableConfig.reports}
          index={true}
          value={permissionValues.reports}
          updateFunction={permissionClickHandler}
        />

        {/* 
                       <div className=" w-full h-[40px] bg-[#F6F7F8] flex flex-row justify-between ">
                            <div className=" w-fit h-full flex flex-col justify-center font-[600] font-lato text-[#172B70] ps-4 text-[12px]">
                                 Leads
                            </div>
                            <div className=" w-fit h-full flex flex-row justify-center items-center gap-[1.5rem] me-[5rem]">
                                 <div className=" w-[50px] h-full flex flex-row justify-center items-center">
                                      <CheckBoxComponent value={false} />
                                 </div>
                                 <div className=" w-[50px] h-full flex flex-row justify-center items-center">
                                      <CheckBoxComponent value={false} />
                                 </div>
                                 <div className=" w-[50px] h-full flex flex-row justify-center items-center">
                                      <CheckBoxComponent value={false} />
                                 </div>
                                 <div className=" w-[50px] h-full flex flex-row justify-center items-center">
                                      <CheckBoxComponent value={false} />
                                 </div>
                            </div>
                       </div> */}
        {/* <div className=" w-[100px] h-[40px] bg-[#F6F7F8] ">
                       <div className=" w-fit h-full flex flex-col justify-center font-[600] font-lato text-[#172B70] ps-4 text-[12px]">
                            Customers
                       </div>
                  </div> */}
        {/* <div className=" w-[100px] h-[40px] bg-[#F6F7F8] ">
                       <div className=" w-fit h-full flex flex-col justify-center font-[600] font-lato text-[#172B70] ps-4 text-[12px]">
                            Order Details
                       </div>
                  </div>
                  <div className=" w-[100px] h-[40px] bg-[#F6F7F8] ">
                       <div className=" w-fit h-full flex flex-col justify-center font-[600] font-lato text-[#172B70] ps-4 text-[12px]">
                            Products
                       </div>
                  </div>
                  <div className=" w-[100px] h-[40px] bg-[#F6F7F8] ">
                       <div className=" w-fit h-full flex flex-col justify-center font-[600] font-lato text-[#172B70] ps-4 text-[12px]">
                            Reports
                       </div>
                  </div> */}
      </div>
    </div>
  );
};

export default UserManagementInviteUserPermission;
