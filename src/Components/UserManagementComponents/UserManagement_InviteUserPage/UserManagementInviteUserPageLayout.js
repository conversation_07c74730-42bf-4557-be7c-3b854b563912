import React from "react";
import UserManagementModelHeader from "../Utils/UserManagementModelHeader";
import { useNavigate } from "react-router-dom";
import { RiUserAddFill } from "react-icons/ri";
import UserManagementInviteUserInputSection from "./UserManagementInviteUserInputSection";
import UserManagementInviteUserPermission from "./UserManagementInviteUserPermission";

const UserManagementInviteUserPageLayout = () => {
     const navigate = useNavigate();
     return (
          <div className=" w-[777px] h-[500px] rounded-[8px] bg-[#fff]">
               <UserManagementModelHeader
                    className=" w-full h-[70px] flex flex-row justify-between items-center border-b-2 ps-4 pe-4 pt-2 pb-2"
                    onClick={() => {
                         navigate("..");
                    }}
               >
                    <div className=" h-full flex flex-row items-center gap-[0.5rem]">
                         <RiUserAddFill size={28} color="#10888E40" />

                         <h1 className=" font-[600] text-[18px] bg-clip-text text-transparent bg-mainLinearGradient">
                              Invite Profile
                         </h1>
                    </div>
               </UserManagementModelHeader>
               <div className=" w-full h-fit pt-[2rem] flex flex-row justify-center">
                    <div className=" flex flex-col gap-[1.5rem]">
                         <UserManagementInviteUserInputSection />
                         <UserManagementInviteUserPermission />
                    </div>
               </div>
          </div>
     );
};

export default UserManagementInviteUserPageLayout;
