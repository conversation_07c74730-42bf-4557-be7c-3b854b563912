import React from "react";
import InputComponentWithErrorMessage from "../../../BasicUIElements/InputComponentWithErrorMessage";

const UserManagementInviteUserInputSection = () => {
     return (
          <div className=" w-full h-fit flex flex-col gap-[1rem] ">
               <h1 className=" text-[14px] font-lato font-[400] text-[#577496] ">
                    Fill in the details to invite user with permissions
               </h1>

               <div className=" w-full h-fit">
                    <InputComponentWithErrorMessage
                         className=" w-[550px] h-[45px] rounded-[4px] border-2 border-[#D8E1F2] text-[12px] font-lato font-[400] ps-4"
                         placeholder="Email Address"
                         isTouched={true}
                         errorMessage="sadf"
                         isValid={true}
                    />
               </div>
          </div>
     );
};

export default UserManagementInviteUserInputSection;
