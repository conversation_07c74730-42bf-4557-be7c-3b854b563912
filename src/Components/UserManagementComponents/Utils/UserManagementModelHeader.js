import React from "react";
import { HiXMark } from "react-icons/hi2";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";

const UserManagementModelHeader = ({
     className = " w-full h-fit flex flex-row justify-between ps-4 pe-4",
     children = <></>,
     onClick = () => {},
}) => {
     return (
          <div className={className}>
               <h1 className="  font-poppins font-[600] text-[20px]">
                    {children}
               </h1>
               <ButtonComponent
                    onClick={onClick}
                    className=" w-[32px] h-[32px] rounded-[6px]  flex flex-row justify-center items-center"
               >
                    <HiXMark size={"24"} />
               </ButtonComponent>
          </div>
     );
};

export default UserManagementModelHeader;
