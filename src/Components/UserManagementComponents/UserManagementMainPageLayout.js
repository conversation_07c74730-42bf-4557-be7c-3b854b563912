import React, { useEffect } from "react";
import UserManagementMainPageHeaders from "./UserManagement_MainPage/UserManagementMainPageHeaders";
import UserManagementMainPageListing from "./UserManagement_MainPage/UserManagementMainPageListing";
import UseRouterAuthentication from "../../Hooks/UseRouterAuthentication";
import UseUserManagement from "./UseUserManagement";
import { useSelector } from "react-redux";

const UserManagementMainPageLayout = () => {
  // Route Authentication
  const { isDashboardAccessable } = UseRouterAuthentication();
  const { fetchList } = UseUserManagement();

  const { auth, access } = useSelector((state) => {
    return state.master;
  });

  useEffect(() => {
    isDashboardAccessable("user");
    fetchList();
  }, [auth, access]);

  return (
    <div className=" w-full h-full flex flex-col flex-shrink-0 overflow-auto ">
      <UserManagementMainPageHeaders />
      <div className=" w-full ps-10 text-[25px] font-poppins text-[#00BD94]">
        User
      </div>
      <div className=" ps-10 w-full h-fit flex flex-col gap-[0.5rem] ">
        <UserManagementMainPageListing />
      </div>
    </div>
  );
};

export default UserManagementMainPageLayout;
