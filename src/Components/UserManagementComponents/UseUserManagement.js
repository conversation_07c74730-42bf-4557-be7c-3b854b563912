import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { API_ENDPOINTS, api_headers, BaseURL } from "../../API/APIConfig";
import { PostAPI } from "../../API/PostAPI";
import { GetAPI } from "../../API/GetAPI";
import { UserMainSliceActions } from "../../Store/UserSlices/UserMainSlice";

const UseUserManagement = () => {
  const { auth, access } = useSelector((state) => {
    return state.master;
  });

  const dispatch = useDispatch();

  // Fetch List
  const fetchList = () => {
    if (auth?.id) {
      dispatch(fetc_Admin_User_List(auth));
    }
  };

  return {
    fetchList,
  };
};

export default UseUserManagement;

const fetc_Admin_User_List = (auth) => {
  return async (dispatch) => {
    let url = new URL(BaseURL?.mainURl + API_ENDPOINTS?.fetchuserlist);

    let headers = { ...api_headers, Authorization: auth?.token };

    let response = await GetAPI(url, headers);

    if (response?.ok) {
      let responseData = await response?.json();
      let list = responseData?.data?.user;

      dispatch(UserMainSliceActions?.updateUserList(list));
    }
  };
};
