import React from "react";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";

const UserManagementEditProfilePhoto = () => {
     return (
          <div className=" w-full h-fit  flex flex-row justify-center ">
               <div className=" w-[255px] h-[355px] bg-[#F4F8FF] flex flex-col justify-evenly rounded-[8px]">
                    <div className=" w-full h-[250px] border-2">
                         <img
                              className=" w-full h-full object-cover "
                              src={
                                   "https://www.kindpng.com/picc/m/252-2524695_dummy-profile-image-jpg-hd-png-download.png"
                              }
                              alt="profile"
                         />
                    </div>
                    <div className=" w-full h-fit ps-4 pe-4 ">
                         <input
                              type="range"
                              className=" w-full h-[1px] bg-[#8999AB]"
                         />
                    </div>
                    <div className=" w-full h-fit flex flex-row justify-evenly">
                         <ButtonComponent className=" w-[60%] h-[33px] rounded-[6px] text-[12px] text-[#fff] font-lato bg-mainLinearGradient ">
                              Crop & Upload image
                         </ButtonComponent>
                         <ButtonComponent className=" w-[30%] h-[33px] rounded-[6px] border-[#D8E1F2] border-2 text-[12px] font-lato text-[#8999AB]">
                              Cancel
                         </ButtonComponent>
                    </div>
               </div>
          </div>
     );
};

export default UserManagementEditProfilePhoto;
