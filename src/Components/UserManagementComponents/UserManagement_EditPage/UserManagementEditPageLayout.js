import React from "react";
import { FaUserCircle } from "react-icons/fa";
import UserManagementModelHeader from "../Utils/UserManagementModelHeader";
import { useNavigate } from "react-router-dom";
import UserManagementEditProfilePhoto from "./UserManagementEditProfilePhoto";
import UserManagementEditNavBar from "./UserManagementEditNavBar";
import UserManagementEditInputSection from "./UserManagementEditInputSection";
import UserManagementEditPermission from "./UserManagementEditPermission";
import UserManagementEditSaveAndCancel from "./UserManagementEditSaveAndCancel";
import { userEditPageModes } from "../../../Store/UserSlices/UserEditSlice";
import { useSelector } from "react-redux";

const UserManagementEditPageLayout = () => {
     const navigate = useNavigate();
     const modePage = useSelector((state) => {
          return state.userEdit.ui?.modePage;
     });

     return (
          <div className=" w-[900px] h-[550px] rounded-[6px] bg-[#fff] overflow-hidden">
               <UserManagementModelHeader
                    className=" w-full h-[70px] flex flex-row justify-between items-center border-b-2 ps-4 pe-4 pt-2 pb-2"
                    onClick={() => {
                         navigate("..");
                    }}
               >
                    <div className=" h-full flex flex-row items-center gap-[0.5rem]">
                         <FaUserCircle size={28} color="#10888E40" />

                         <h1 className=" font-[600] text-[18px] bg-clip-text text-transparent bg-mainLinearGradient">
                              Edit Profile
                         </h1>
                    </div>
               </UserManagementModelHeader>
               <div className=" w-full h-fit flex flex-row justify-between overflow-y-auto">
                    <div className=" w-[35%] h-fit  ">
                         <UserManagementEditProfilePhoto />
                    </div>

                    <div className=" w-[65%] h-fit flex flex-col justify-start gap-[1rem] pt-[1rem] ">
                         <UserManagementEditNavBar />

                         {modePage === userEditPageModes.userDetails && (
                              <UserManagementEditInputSection />
                         )}

                         {modePage === userEditPageModes.permission && (
                              <UserManagementEditPermission />
                         )}

                         <UserManagementEditSaveAndCancel />
                    </div>
               </div>
          </div>
     );
};

export default UserManagementEditPageLayout;
