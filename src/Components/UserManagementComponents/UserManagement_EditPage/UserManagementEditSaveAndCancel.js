import React from "react";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { userUpdateAccessThunk } from "../../../Store/UserSlices/UserEditSlice";

const UserManagementEditSaveAndCancel = () => {
     const dispatch = useDispatch();
     const navigate = useNavigate();
     const auth = useSelector((state) => {
          return state.master.auth;
     });
     const { content, permissionValues, ui } = useSelector((state) => {
          return state.userEdit;
     });
     const { saveButtonIsLoading } = ui;

     const saveButtonClickHandler = () => {
          if (saveButtonIsLoading) {
               return;
          }
          dispatch(
               userUpdateAccessThunk(auth, content, permissionValues, navigate)
          );
     };
     return (
          <div className=" w-full h-fit mt-[2rem] flex flex-row gap-[1rem]">
               <ButtonComponent
                    isLoading={saveButtonIsLoading}
                    onClick={saveButtonClickHandler}
                    className=" w-[128px] h-[33px] rounded-[6px] bg-mainLinearGradient text-[12px] font-lato text-[#fff] flex flex-row justify-center items-center"
               >
                    Save
               </ButtonComponent>
               <ButtonComponent
                    onClick={() => {
                         navigate(-1);
                    }}
                    className=" w-[128px] h-[33px] rounded-[6px] border-2 border-[#D8E1F2] text-[12px] font-lato text-[#666666] flex flex-row justify-center items-center"
               >
                    Cancel
               </ButtonComponent>
          </div>
     );
};

export default UserManagementEditSaveAndCancel;
