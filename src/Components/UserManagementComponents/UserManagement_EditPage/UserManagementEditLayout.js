import React, { useState } from "react";
import ModelHeaderAndRoute from "../../Utils/UtilsComponents/ModelHeaderAndRoute";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import CheckBoxComponent from "../../../BasicUIElements/CheckBoxComponent";
import { useDispatch, useSelector } from "react-redux";
import { userEditSliceActions } from "../../../Store/UserSlices/UserEditSlice";
import { useNavigate } from "react-router-dom";
import { routes } from "../../../Config/routesConfig";
import { API_ENDPOINTS, api_headers, BaseURL } from "../../../API/APIConfig";
import { PostAPI } from "../../../API/PostAPI";
import UseUserManagement from "../UseUserManagement";

const UserManagementEditLayout = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { auth, access } = useSelector((state) => {
    return state.master;
  });
  const { permissionValues, userDetails } = useSelector((state) => {
    return state.userEdit;
  });
  const { fetchList } = UseUserManagement();

  // Click Handler
  const clickHandler = (key, index, value) => {
    let value_keys = { ...permissionValues };
    let array = [...value_keys[key]];

    array[index] = !value;
    value_keys[key] = array;

    dispatch(userEditSliceActions?.updatePermissionValue(value_keys));
  };

  const closeModal = () => {
    dispatch(
      userEditSliceActions.updatePermissionValue({
        users: [false, false, false, false, false],
        purchase: [false, false, false, false, false],
        sales: [false, false, false, false, false],
        marketing: [false, false, false, false, false],
        orders: [false, false, false, false, false],
        customers: [false, false, false, false, false],
        networking: [false, false, false, false, false],
        serviceComplaints: [false, false, false, false, false],
        tickets: [false, false, false, false, false],
        taskManagement: [false, false, false, false, false],
      })
    );
    dispatch(
      userEditSliceActions?.updateUserDetails({
        username: "",
        id: "",
      })
    );
    navigate(routes?.userManagement?.directLink);
  };

  //------------------

  const [isLoading, setIsLoading] = useState(false);

  const submithandler = async () => {
    setIsLoading(true);
    let response = await edit_user_access_function(
      permissionValues,
      userDetails,
      auth
    );
    setIsLoading(false);
    fetchList();
    closeModal();
  };

  //----------------
  return (
    <div className=" w-[600px] max-h-[95vh] h-fit overflow-y-auto flex flex-col  bg-[#fff] rounded-[6px] p-4 z-[150]">
      <ModelHeaderAndRoute
        className="w-full h-fit flex flex-row justify-between ps-4 pe-4 "
        title="Edit the User "
        onClick={() => {
          closeModal();
        }}
      />

      <h1 className=" ps-[1rem] mt-[0.5rem] font-poppins text-[16px] flex gap-[0.5rem]">
        Username:
        {userDetails?.username ? (
          <p className=" text-[#72BF78]"> {userDetails?.username} </p>
        ) : (
          <p className=" text-[#CB6040]"> "Not Found" </p>
        )}
      </h1>

      <div className=" w-full h-fit flex flex-row justify-center mt-[1rem] ">
        <div className=" w-[95%] border-2 flex flex-col border-black rounded-[10px] border-opacity-10 p-2 gap-[0.5rem] ">
          {/* Heading  */}
          <div className=" w-full h-[30px] font-poppins text-[14px] flex flex-row justify-between items-center ">
            <div className=" w-[100px] h-fit text-[#172B70] font-lato font-bold  "></div>
            <div className=" w-[50px] h-full flex flex-row justify-center">
              View
            </div>
            <div className=" w-[50px] h-full flex flex-row justify-center">
              Add
            </div>
            <div className=" w-[50px] h-full flex flex-row justify-center">
              Edit
            </div>
            <div className=" w-[50px] h-full flex flex-row justify-center">
              Delete
            </div>
            <div className=" w-[50px] h-full flex flex-row justify-center">
              Insights
            </div>
          </div>
          {/* Users  */}
          <div className=" w-full h-[30px] font-poppins text-[14px] flex flex-row justify-between items-center bg-[#F6F7F8] ">
            <div className=" w-[100px] h-fit text-[#172B70] font-lato font-bold  ">
              Users
            </div>

            {permissionValues["users"]?.map((item, index) => {
              return (
                <div className=" w-[50px] h-full flex flex-row justify-center">
                  <CheckBoxComponent
                    value={item}
                    onClick={() => {
                      clickHandler("users", index, item);
                    }}
                  />
                </div>
              );
            })}
          </div>
          {/* Purchase  */}
          <div className=" w-full h-[30px] font-poppins text-[14px] flex flex-row justify-between items-center bg-[#F6F7F8] ">
            <div className=" w-[100px] h-fit text-[#172B70] font-lato font-bold  ">
              Purchase
            </div>

            {permissionValues["purchase"]?.map((item, index) => {
              return (
                <div className=" w-[50px] h-full flex flex-row justify-center">
                  <CheckBoxComponent
                    value={item}
                    onClick={() => {
                      clickHandler("purchase", index, item);
                    }}
                  />
                </div>
              );
            })}
          </div>
          {/* Sales  */}
          <div className=" w-full h-[30px] font-poppins text-[14px] flex flex-row justify-between items-center  ">
            <div className=" w-[100px] h-fit text-[#172B70] font-lato font-bold  ">
              Sales
            </div>

            {permissionValues["sales"]?.map((item, index) => {
              return (
                <div className=" w-[50px] h-full flex flex-row justify-center">
                  <CheckBoxComponent
                    value={item}
                    onClick={() => {
                      clickHandler("sales", index, item);
                    }}
                  />
                </div>
              );
            })}
          </div>
          {/* Marketing  */}
          <div className=" w-full h-[30px] font-poppins text-[14px] flex flex-row justify-between items-center bg-[#F6F7F8] ">
            <div className=" w-[100px] h-fit text-[#172B70] font-lato font-bold  ">
              Marketing
            </div>

            {permissionValues["marketing"]?.map((item, index) => {
              return (
                <div className=" w-[50px] h-full flex flex-row justify-center">
                  <CheckBoxComponent
                    value={item}
                    onClick={() => {
                      clickHandler("marketing", index, item);
                    }}
                  />
                </div>
              );
            })}
          </div>
          {/* Orders  */}
          <div className=" w-full h-[30px] font-poppins text-[14px] flex flex-row justify-between items-center ">
            <div className=" w-[100px] h-fit text-[#172B70] font-lato font-bold  ">
              Orders
            </div>

            {permissionValues["orders"]?.map((item, index) => {
              return (
                <div className=" w-[50px] h-full flex flex-row justify-center">
                  <CheckBoxComponent
                    value={item}
                    onClick={() => {
                      clickHandler("orders", index, item);
                    }}
                  />
                </div>
              );
            })}
          </div>
          {/* Customers  */}
          <div className=" w-full h-[30px] font-poppins text-[14px] flex flex-row justify-between items-center ">
            <div className=" w-[100px] h-fit text-[#172B70] font-lato font-bold  ">
              Customers
            </div>

            {permissionValues["customers"]?.map((item, index) => {
              return (
                <div className=" w-[50px] h-full flex flex-row justify-center">
                  <CheckBoxComponent
                    value={item}
                    onClick={() => {
                      clickHandler("customers", index, item);
                    }}
                  />
                </div>
              );
            })}
          </div>
          {/* Networking  */}
          <div className=" w-full h-[30px] font-poppins text-[14px] flex flex-row justify-between items-center bg-[#F6F7F8] ">
            <div className=" w-[100px] h-fit text-[#172B70] font-lato font-bold  ">
              Networking
            </div>

            {permissionValues["networking"]?.map((item, index) => {
              return (
                <div className=" w-[50px] h-full flex flex-row justify-center">
                  <CheckBoxComponent
                    value={item}
                    onClick={() => {
                      clickHandler("networking", index, item);
                    }}
                  />
                </div>
              );
            })}
          </div>
          {/* Service Complaints  */}
          <div className=" w-full h-[30px] font-poppins text-[14px] flex flex-row justify-between items-center ">
            <div className=" w-[100px] h-fit text-[#172B70] font-lato font-bold truncate  ">
              Service Complaints
            </div>

            {permissionValues["serviceComplaints"]?.map((item, index) => {
              return (
                <div className=" w-[50px] h-full flex flex-row justify-center">
                  <CheckBoxComponent
                    value={item}
                    onClick={() => {
                      clickHandler("serviceComplaints", index, item);
                    }}
                  />
                </div>
              );
            })}
          </div>
          {/* Tickets  */}
          <div className=" w-full h-[30px] font-poppins text-[14px] flex flex-row justify-between items-center bg-[#F6F7F8] ">
            <div className=" w-[100px] h-fit text-[#172B70] font-lato font-bold truncate  ">
              Tickets
            </div>

            {permissionValues["tickets"]?.map((item, index) => {
              return (
                <div className=" w-[50px] h-full flex flex-row justify-center">
                  <CheckBoxComponent
                    value={item}
                    onClick={() => {
                      clickHandler("tickets", index, item);
                    }}
                  />
                </div>
              );
            })}
          </div>
          {/* Task Management  */}
          <div className=" w-full h-[30px] font-poppins text-[14px] flex flex-row justify-between items-center  ">
            <div className=" w-[100px] h-fit text-[#172B70] font-lato font-bold truncate  ">
              Task Management
            </div>

            {permissionValues["taskManagement"]?.map((item, index) => {
              return (
                <div className=" w-[50px] h-full flex flex-row justify-center">
                  <CheckBoxComponent
                    value={item}
                    onClick={() => {
                      clickHandler("taskManagement", index, item);
                    }}
                  />
                </div>
              );
            })}
          </div>
          {/* -------------------------- */}
        </div>
      </div>

      <div className=" w-full h-fit mb-[2rem]">
        <div className=" w-full h-fit flex flex-row justify-center gap-[1rem] mt-[1.5rem]">
          <ButtonComponent
            onClick={() => {
              closeModal();
            }}
            className=" w-[45%] h-[58px] rounded-[6px] border-[#00BD94] border-2 bg-clip-text text-transparent bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] flex flex-row justify-center items-center"
          >
            Cancel
          </ButtonComponent>
          <ButtonComponent
            onClick={submithandler}
            isLoading={isLoading}
            className=" w-[45%] h-[58px] rounded-[6px] bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] text-[#fff] flex flex-row justify-center items-center"
          >
            Edit
          </ButtonComponent>
        </div>
      </div>
    </div>
  );
};

export default UserManagementEditLayout;

export const edit_user_access_function = async (access, userDetails, auth) => {
  let url = new URL(
    BaseURL?.mainURl + API_ENDPOINTS?.userEditAccess + "/" + userDetails?.id
  );

  let headers = { ...api_headers, Authorization: auth?.token };

  let access_obj = [
    { key: "users", api_key: "user" },
    { key: "purchase", api_key: "Purchases" },
    { key: "sales", api_key: "Sales" },
    { key: "marketing", api_key: "marketing" },
    { key: "orders", api_key: "Orders" },
    { key: "customers", api_key: "customers" },
    { key: "networking", api_key: "networking" },
    { key: "serviceComplaints", api_key: "serviceComplaints" },
    { key: "tickets", api_key: "tickets" },
    { key: "taskManagement", api_key: "taskManagement" },
  ];

  for (let item of access_obj) {
    let obj = {
      access_data: {
        Read: access[item?.key][0],
        write: access[item?.key][1],
        edit: access[item?.key][2],
        deletes: access[item?.key][3],
        insigths: access[item?.key][4],
      },
      product: item?.api_key,
    };

    let response = await PostAPI(url, obj, headers);

    if (response?.ok) {
    } else {
      return;
    }
  }

  return true;
};
