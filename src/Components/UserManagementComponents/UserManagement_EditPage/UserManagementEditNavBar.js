import React from "react";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import { useDispatch, useSelector } from "react-redux";
import {
     userEditPageModes,
     userEditSliceActions,
} from "../../../Store/UserSlices/UserEditSlice";

const UserManagementEditNavBar = () => {
     const dispatch = useDispatch();

     const modePage = useSelector((state) => {
          return state.userEdit.ui?.modePage;
     });

     return (
          <div className=" w-full h-[35px]">
               <ButtonComponent
                    onClick={() => {
                         dispatch(
                              userEditSliceActions.modifyUserEditPageMode(
                                   userEditPageModes.userDetails
                              )
                         );
                    }}
                    className={` w-[113px] h-full text-[16px] ${
                         modePage === userEditPageModes.userDetails
                              ? " text-transparent bg-clip-text bg-mainLinearGradient font-lato  border-b-[2px] border-[#00BD94] "
                              : " text-[16px] text-[#8999AB] font-lato  border-b-[2px] border-transparent"
                    }`}
               >
                    User Details
               </ButtonComponent>
               <ButtonComponent
                    onClick={() => {
                         dispatch(
                              userEditSliceActions.modifyUserEditPageMode(
                                   userEditPageModes.permission
                              )
                         );
                    }}
                    className={` w-[113px] h-full ${
                         modePage === userEditPageModes.permission
                              ? " text-transparent bg-clip-text bg-mainLinearGradient font-lato  border-b-[2px] border-[#00BD94] "
                              : " text-[16px] text-[#8999AB] font-lato  border-b-[2px] border-transparent"
                    }`}
               >
                    Permission
               </ButtonComponent>
          </div>
     );
};

export default UserManagementEditNavBar;
