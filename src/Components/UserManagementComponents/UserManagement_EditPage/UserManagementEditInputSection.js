import { useSelector } from "react-redux";
import InputComponentWithErrorMessage from "../../../BasicUIElements/InputComponentWithErrorMessage";
import React from "react";

const UserManagementEditInputSection = () => {
     const { email, username, mobilephone } = useSelector((state) => {
          return state.userEdit.content;
     });

     return (
          <>
               <div className=" w-full h-fit  flex flex-col gap-[1rem] ">
                    {/* Full Name  */}
                    <div className=" w-full h-fit flex flex-row justify-between items-center ">
                         <div className="  w-[100px] h-fit font-lato font-[400] text-[#8999AB] text-[12px] ">
                              Username
                         </div>
                         <div className=" flex-1 flex flex-row justify-center">
                              <div className=" w-[90%] h-fit ">
                                   <InputComponentWithErrorMessage
                                        type="text"
                                        name="Username"
                                        placeholder="Full Name"
                                        value={username.value}
                                        isValid={username.isValid}
                                        isTouched={true}
                                        className=" w-full h-[39px] border-2 rounded-[8px] border-[#D8E1F2] ps-2 placeholder:text-[#8999AB] text-[12px] font-lato "
                                   />
                              </div>
                         </div>
                    </div>
                    {/* Display Name  */}
                    <div className=" w-full h-fit flex flex-row justify-between items-center ">
                         <div className=" w-[100px] h-fit font-lato font-[400] text-[#8999AB] text-[12px] ">
                              Email Address
                         </div>
                         <div className=" flex-1 flex flex-row justify-center">
                              <div className=" w-[90%] h-fit ">
                                   <InputComponentWithErrorMessage
                                        type="text"
                                        name="displayname"
                                        placeholder="Display Name"
                                        value={email.value}
                                        isValid={email.isValid}
                                        isTouched={true}
                                        className=" w-full h-[39px] border-2 rounded-[8px] border-[#D8E1F2] ps-2 placeholder:text-[#8999AB] text-[12px] font-lato "
                                   />
                              </div>
                         </div>
                    </div>
                    {/* Email ID  */}
                    <div className=" w-full h-fit flex flex-row justify-between items-center ">
                         <div className=" w-[100px] h-fit font-lato font-[400] text-[#8999AB] text-[12px] ">
                              Mobile Phone
                         </div>
                         <div className=" flex-1 flex flex-row justify-center">
                              <div className=" w-[90%] h-fit ">
                                   <InputComponentWithErrorMessage
                                        type="email"
                                        name="email"
                                        placeholder="Email id"
                                        value={mobilephone.value}
                                        isValid={mobilephone.isValid}
                                        isTouched={true}
                                        className=" w-full h-[39px] border-2 rounded-[8px] border-[#D8E1F2] ps-2 placeholder:text-[#8999AB] text-[12px] font-lato "
                                   />
                              </div>
                         </div>
                    </div>
                    {/* Description */}
                    {/* <div className=" w-full h-fit flex flex-row justify-between items-center ">
                         <div className=" w-[100px] h-fit font-lato font-[400] text-[#8999AB] text-[12px] ">
                              Description
                         </div>
                         <div className=" flex-1 flex flex-row justify-center">
                              <div className=" w-[90%] h-fit ">
                                   <InputComponentWithErrorMessage
                                        type="text"
                                        name="description"
                                        placeholder="Description"
                                        value=""
                                        className=" w-full h-[39px] border-2 rounded-[8px] border-[#D8E1F2] ps-2 placeholder:text-[#8999AB] text-[12px] font-lato "
                                   />
                              </div>
                         </div>
                    </div> */}
               </div>
          </>
     );
};

export default UserManagementEditInputSection;
