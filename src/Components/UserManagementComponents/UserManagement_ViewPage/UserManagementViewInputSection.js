import React from "react";

const UserManagementViewInputSection = ({ content }) => {
     return (
          <>
               <div className=" w-full h-fit  flex flex-col gap-[1rem] ">
                    {/* Full Name  */}
                    <div className=" w-full h-fit flex flex-row justify-between items-center ">
                         <div className="  w-[100px] h-fit font-lato font-[600] text-[#577496] text-[12px] ">
                              Username
                         </div>
                         <div className=" flex-1 flex flex-row justify-center">
                              <h4 className=" ps-6 w-full h-[39px] flex flex-col justify-center font-[500] font-lato text-[12px] text-[#577496]">
                                   {content.username}
                              </h4>
                         </div>
                    </div>
                    {/* Display Name  */}
                    <div className=" w-full h-fit flex flex-row justify-between items-center ">
                         <div className=" w-[100px] h-fit font-lato font-[600] text-[#577496] text-[12px] ">
                              Mobile Phone
                         </div>
                         <div className=" flex-1 flex flex-row justify-center">
                              <h4 className=" ps-6 w-full h-[39px] flex flex-col justify-center font-[500] font-lato text-[12px] text-[#577496]">
                                   {content?.mobilephone}
                              </h4>
                         </div>
                    </div>
                    {/* Email ID  */}
                    <div className=" w-full h-fit flex flex-row justify-between items-center ">
                         <div className=" w-[100px] h-fit font-lato font-[600] text-[#577496] text-[12px] ">
                              Email ID
                         </div>
                         <div className=" flex-1 flex flex-row justify-center">
                              <h4 className=" ps-6 w-full h-[39px] flex flex-col justify-center font-[500] font-lato text-[12px] text-[#577496]">
                                   {content?.email}
                              </h4>
                         </div>
                    </div>
                    {/* Description */}
                    {/* <div className=" w-full h-fit flex flex-row justify-between items-center ">
                         <div className=" w-[100px] h-fit font-lato font-[600] text-[#577496] text-[12px] ">
                              Description
                         </div>
                         <div className=" flex-1 flex flex-row justify-center">
                              <h4 className=" ps-6 w-full h-[39px] flex flex-col justify-center font-[500] font-lato text-[12px] text-[#577496]">
                                   Manager
                              </h4>
                         </div>
                    </div> */}
               </div>
          </>
     );
};

export default UserManagementViewInputSection;
