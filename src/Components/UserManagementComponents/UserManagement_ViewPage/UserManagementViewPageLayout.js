import React from "react";
import UserManagementModelHeader from "../Utils/UserManagementModelHeader";
import { useNavigate } from "react-router-dom";
import { FaUserCircle } from "react-icons/fa";
import UserManagementVIewNavBar from "./UserManagementVIewNavBar";
import UserManagementViewInputSection from "./UserManagementViewInputSection";
import UserManagementViewPagePermission from "./UserManagementViewPagePermission";
import { useSelector } from "react-redux";
import { userViewPageMode } from "../../../Store/UserSlices/UserViewSlice";
import UserManagementViewSaveAndCancelButton from "./UserManagementViewSaveAndCancelButton";

const UserManagementViewPageLayout = () => {
     const navigate = useNavigate();
     const modePage = useSelector((state) => {
          return state.userView.ui.modePage;
     });
     const { content, permissionValues } = useSelector((state) => {
          return state.userView;
     });
     return (
          <div className=" w-[900px] h-[500px] rounded-[6px] bg-[#fff]">
               <UserManagementModelHeader
                    className=" w-full h-[70px] flex flex-row justify-between items-center border-b-2 ps-4 pe-4 pt-2 pb-2"
                    onClick={() => {
                         navigate("..");
                    }}
               >
                    {" "}
                    <div className=" h-full flex flex-row items-center gap-[0.5rem]">
                         <FaUserCircle size={28} color="#10888E40" />

                         <h1 className=" font-[600] text-[18px] bg-clip-text text-transparent bg-mainLinearGradient">
                              View Profile
                         </h1>
                    </div>
               </UserManagementModelHeader>
               <div className=" w-full h-fit flex flex-row justify-between ">
                    <div className=" w-[35%] h-fit flex flex-row justify-center pt-[2rem]">
                         <div className=" w-[130px] h-[130px] bg-[#D0D1D2] rounded-[50%] overflow-hidden">
                              <img
                                   src={
                                        "https://www.kindpng.com/picc/m/252-2524695_dummy-profile-image-jpg-hd-png-download.png"
                                   }
                                   alt="Profile "
                                   className=" w-full h-full object-cover"
                              />
                         </div>
                    </div>

                    <div className=" w-[60%] h-fit flex flex-col gap-[1rem] pt-[1.5rem]">
                         <UserManagementVIewNavBar />
                         {modePage === userViewPageMode.userDetails && (
                              <UserManagementViewInputSection
                                   content={content}
                              />
                         )}
                         {modePage === userViewPageMode.permission && (
                              <UserManagementViewPagePermission
                                   access={permissionValues}
                              />
                         )}
                         <UserManagementViewSaveAndCancelButton />
                    </div>
               </div>
          </div>
     );
};

export default UserManagementViewPageLayout;
