import React from "react";
import { useDispatch, useSelector } from "react-redux";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";

import {
     userViewPageMode,
     userViewSliceActions,
} from "../../../Store/UserSlices/UserViewSlice";

const UserManagementVIewNavBar = () => {
     const dispatch = useDispatch();

     const modePage = useSelector((state) => {
          return state.userView.ui?.modePage;
     });

     return (
          <div className=" w-full h-[35px]">
               <ButtonComponent
                    onClick={() => {
                         dispatch(
                              userViewSliceActions.modifyUserViewModePage(
                                   userViewPageMode.userDetails
                              )
                         );
                    }}
                    className={` w-[113px] h-full text-[16px] ${
                         modePage === userViewPageMode.userDetails
                              ? " text-transparent bg-clip-text bg-mainLinearGradient font-lato  border-b-[2px] border-[#00BD94] "
                              : " text-[16px] text-[#8999AB] font-lato  border-b-[2px] border-transparent"
                    }`}
               >
                    User Details
               </ButtonComponent>
               <ButtonComponent
                    onClick={() => {
                         dispatch(
                              userViewSliceActions.modifyUserViewModePage(
                                   userViewPageMode.permission
                              )
                         );
                    }}
                    className={` w-[113px] h-full ${
                         modePage === userViewPageMode.permission
                              ? " text-transparent bg-clip-text bg-mainLinearGradient font-lato  border-b-[2px] border-[#00BD94] "
                              : " text-[16px] text-[#8999AB] font-lato  border-b-[2px] border-transparent"
                    }`}
               >
                    Permission
               </ButtonComponent>
          </div>
     );
};

export default UserManagementVIewNavBar;
