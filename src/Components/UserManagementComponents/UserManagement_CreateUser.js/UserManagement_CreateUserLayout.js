import React, { useEffect, useState } from "react";
import ModelHeaderAndRoute from "../../Utils/UtilsComponents/ModelHeaderAndRoute";
import { useNavigate } from "react-router-dom";
import { routes } from "../../../Config/routesConfig";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import FormEnginee from "../../../UI_Enginee/Form/FormEnginee";
import {
  formData_parser,
  formEnginee_Formatee_Structure_and_State,
} from "../../../Dashboard_API_calling_and_formatting/Form_Formate_and_API";
import {
  change_other_fields,
  vaildator_Form_function,
} from "../../../UI_Enginee/Form/FormEngineeConfig";
import { useDispatch, useSelector } from "react-redux";
import { API_ENDPOINTS, api_headers, BaseURL } from "../../../API/APIConfig";
import { PostAPI } from "../../../API/PostAPI";
import { userListFetch } from "../../../Store/TicketsSlice/TicketsSlice";
import toast from "react-hot-toast";
import UseUserManagement from "../UseUserManagement";

const formConfig = [
  {
    type: "text",
    key: "username",
    defaultValue: "",
    placeholder: "Username",
    required: true,
  },
  {
    type: "number",
    key: "mobile",
    defaultValue: "",
    placeholder: "Mobile Number",
    required: true,
  },
  {
    type: "singleselect",
    key: "department",
    options: [
      "purchase",
      "sales",
      "order",
      "others",
      "Warehouse",
      "DBF and D Ceramic",
      "Accounts",
    ],
    defaultValue: "Pick a department",
    placeholder: "Pick a department",
    required: true,
    // ------------------------
    change: [
      // -------------------
      {
        value: "purchase",
        effect: [
          {
            key: "role",
            structure: {
              options: [
                "Purchase Manager",
                "Purchase Executive",
                "Billing",
                "user",
                "admin",
                "customer",
                "executive",
                "team_lead",
                "manager",
                "chief_Officer",
              ],
            },
            state: {
              value: "Pick a role for purchase department",
              valid: false,
            },
          },
        ],
      },
      // -------------------
      {
        value: "sales",
        effect: [
          {
            key: "role",
            structure: {
              options: [
                "Division Manager",
                "Sales Manager",
                "Team Leader",
                "Sales Executive",
                "Warehouse Executive",
                "Sop",
                "user",
                "admin",
                "customer",
                "executive",
                "team_lead",
                "manager",
                "chief_Officer",
              ],
            },
            state: {
              value: "Pick a role for Sales department",
            },
          },
        ],
      },
      // -------------------
      {
        value: "Warehouse",
        effect: [
          {
            key: "role",
            structure: {
              options: [
                "Manager",
                "Asst Manager",
                "Inward Manager",
                "Warehouse Executive",
                "user",
                "admin",
                "customer",
                "executive",
                "team_lead",
                "manager",
                "chief_Officer",
              ],
            },
            state: {
              value: "Pick a role for Warehouse department",
            },
          },
        ],
      },
      // -------------------
      {
        value: "DBF and D Ceramic",
        effect: [
          {
            key: "role",
            structure: {
              options: [
                "Division Manager",
                "Sales Manager",
                "Team Leader",
                "Sales Executive",
                "Warehouse Executive",
                "Sop",
                "user",
                "admin",
                "customer",
                "executive",
                "team_lead",
                "manager",
                "chief_Officer",
              ],
            },
            state: {
              value: "Pick a role for DBF and D Ceramic department",
            },
          },
        ],
      },
      // -------------------
      {
        value: "Accounts",
        effect: [
          {
            key: "role",
            structure: {
              options: [
                "Accounts Manager",
                "Accountant",
                "Accounts Executive",
                "user",
                "admin",
                "customer",
                "executive",
                "team_lead",
                "manager",
                "chief_Officer",
              ],
            },
            state: {
              value: "Pick a role for Accounts department",
            },
          },
        ],
      },
      // -------------------
      {
        value: "others",
        effect: [
          {
            key: "role",
            structure: {
              options: [
                "General Manager",
                "Cashier",
                "user",
                "admin",
                "customer",
                "executive",
                "team_lead",
                "manager",
                "chief_Officer",
              ],
            },
            state: {
              value: "Pick a role for Other department",
            },
          },
        ],
      },
      // -------------------
      {
        value: "order",
        effect: [
          {
            key: "role",
            structure: {
              options: [
                "user",
                "admin",
                "customer",
                "executive",
                "team_lead",
                "manager",
                "chief_Officer",
              ],
            },
            state: {
              value: "Pick a role",
            },
          },
          // -------------------
        ],
        // -------------------
      },
      // -------------------
    ],
    // ------------------------
  },
  {
    type: "singleselect",
    key: "role",
    options: [
      "user",
      "admin",
      "customer",
      "executive",
      "team_lead",
      "manager",
      "chief_Officer",
    ],
    defaultValue: "Pick a role",
    placeholder: "Pick a role",
    required: true,
  },
  {
    type: "singleselect",
    key: "unit",
    options: ["DNSP", "DBF", "DUS", "DC"],
    defaultValue: "Select a unit",
    placeholder: "Select a unit",
    required: true,
  },
  {
    type: "text",
    key: "password",
    defaultValue: "",
    placeholder: "Password",
    required: true,
  },
];

const UserManagement_CreateUserLayout = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { auth } = useSelector((state) => {
    return state.master;
  });

  const { fetchList } = UseUserManagement();

  const [structure, setStructure] = useState([]);
  const [state, setState] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    let ss = formEnginee_Formatee_Structure_and_State(formConfig);
    setState(ss.state);
    setStructure(ss.structure);
  }, []);

  // ---------------------
  const updateChangeState = (update_state, change_state) => {
    let obj = { ...update_state };
    for (let item in change_state) {
      obj = {
        ...obj,
        [item]: {
          ...obj[item],
          ...change_state[item],
        },
      };
    }
    return obj;
  };

  const updateChangeStructure = (update_structure, change_structure) => {
    let return_structure = [];

    for (let item of update_structure) {
      let current = { ...item };
      if (change_structure[item?.key]) {
        current = { ...current, ...change_structure[item?.key] };
      }
      return_structure.push(current);
    }
    return return_structure;
  };
  // ---------------------

  const updateFunctionOfForm = (key, value, currentInput = null) => {
    // Updating the state
    let updateState = {
      ...state,
      [key]: { ...state[key], value: value },
    };

    if (currentInput?.change && currentInput?.change?.length > 0) {
      let returnObj = change_other_fields(value, currentInput);

      updateState = updateChangeState(updateState, returnObj?.state);
      let structure_update = updateChangeStructure(
        structure,
        returnObj?.structure
      );
      setStructure(structure_update);
    }
    setState(updateState);
    // dispatch(SalesAndMarketingSliceActions?.updateState(updateState));
  };

  const submitHandler = async () => {
    let vaildate = vaildator_Form_function(structure, state);
    setState(vaildate?.validate_Obj);
    if (!vaildate?.isFormValid) {
      return;
    }

    let parsedObj = formData_parser(structure, state).parser_obj;

    if (parsedObj.unit) {
      parsedObj = {
        ...parsedObj,
        unit: parsedObj.unit,
      };
    }

    setIsLoading(true);

    let response = await createUser_function(auth, parsedObj);

    let toast_id = toast.loading("Creating", { position: "top-right" });

   

    if (response?.ok) {
      let responsedata = await response.json();
      dispatch(userListFetch(auth));
      fetchList();
      closeModal();
      toast.custom(
        (t) => (
          <div
            className={`${
              t.visible ? "animate-enter" : "animate-leave"
            } max-w-md w-full bg-white shadow-lg rounded-lg pointer-events-auto flex ring-1 ring-black ring-opacity-5`}
          >
            <div className="flex-1 w-0 p-4">
              <div className="flex items-start">
                <div className="ml-3 flex-1">
                  <p className="text-sm font-medium text-gray-900">
                    Username : {state["username"].value}
                  </p>
                  <p className="mt-1 text-sm text-gray-500">
                    Password : {state["password"].value}
                  </p>
                </div>
              </div>
            </div>
            <div className="flex border-l border-gray-200">
              <button
                onClick={() => {
                  navigator.clipboard
                    .writeText(
                      `Username : ${state["username"].value} \nPassword : ${state["password"].value}`
                    )
                    .then(() => {
                      toast.success("Copied", {
                        id: t.id,
                      });
                    })
                    .catch((err) => {
                      toast.error("Failed to copy", {
                        id: t.id,
                      });
                    });
                  // toast.dismiss(t.id);
                }}
                className="w-full border border-transparent rounded-none rounded-r-lg p-4 flex items-center justify-center text-sm font-medium text-indigo-600 hover:text-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                Copy
              </button>
            </div>
          </div>
        ),
        { id: toast_id, duration: 20000 }
      );
    } else {
      let responsedata = await response.json();
      toast.error(responsedata?.error, { id: toast_id });
    }

    setIsLoading(false);
  };

  // Closing the modal
  const closeModal = () => {
    navigate(-1);
  };

  return (
    <div className=" w-full h-full flex flex-row justify-center items-center">
      <div className=" md:w-[424px] w-[80%] max-h-[95vh] h-fit overflow-y-auto bg-[#fff] rounded-[6px] p-4 pb-[2rem] ">
        <ModelHeaderAndRoute
          className="w-full h-fit flex flex-row justify-between ps-4 pe-4 "
          title="Create a user"
          onClick={closeModal}
        />

        <FormEnginee
          form={structure}
          formState={state}
          formStateFunction={updateFunctionOfForm}
        />

        {/* --- Button  */}
        <div className=" w-full h-fit mt-[1rem]">
          <div className=" w-full h-fit flex flex-row justify-center gap-[1rem]">
            <ButtonComponent
              onClick={closeModal}
              className=" w-[45%] h-[58px] rounded-[6px] border-[#00BD94] border-2 bg-clip-text text-transparent bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] flex flex-row justify-center items-center"
            >
              Cancel
            </ButtonComponent>
            <ButtonComponent
              isLoading={isLoading}
              onClick={submitHandler}
              className=" w-[45%] h-[58px] rounded-[6px] bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] text-[#fff] flex flex-row justify-center items-center"
            >
              Create a user
            </ButtonComponent>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserManagement_CreateUserLayout;

const createUser_function = async (auth, obj) => {
  let url = new URL(BaseURL?.mainURl + API_ENDPOINTS?.adminCreateUser);

  let body = obj;

  let headers = { ...api_headers, authorization: auth?.token };

  let response = await PostAPI(url, body, headers);

  return response;
};
