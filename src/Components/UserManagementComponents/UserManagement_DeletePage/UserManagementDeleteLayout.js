import React from "react";
import { useNavigate } from "react-router-dom";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import { MdOutlineDelete } from "react-icons/md";
import logo from "../../../Assest/Utils/successlogo.png";
import { useDispatch, useSelector } from "react-redux";
import { userDeleteThunk } from "../../../Store/UserSlices/UserMainSlice";

const UserManagementDeleteLayout = () => {
     const navigate = useNavigate();
     const dispatch = useDispatch();
     const { id, deleteButtonIsLoading } = useSelector((state) => {
          return state.userMain.delete;
     });
     const auth = useSelector((state) => {
          return state.master.auth;
     });

     const clickHandler = () => {
          dispatch(userDeleteThunk(id, auth.token));
     };
     return (
          <div className=" w-[424px] h-[130px] rounded-[8px] bg-[#fff] p-3 flex flex-col justify-between">
               <div className=" w-full h-fit flex flex-row justify-between items-center ps-4 pe-4 ">
                    <h1 className="  font-poppins font-[400] text-[18px]">
                         Add you sure you want to delete ?
                    </h1>
                    <ButtonComponent
                         onClick={() => {
                              navigate("..");
                         }}
                         className=" w-[32px] h-[32px] rounded-[6px]  flex flex-row justify-center items-center"
                    >
                         <MdOutlineDelete color="red" size={32} />
                    </ButtonComponent>
               </div>

               {/* <div className=" w-full h-fit flex flex-row justify-center">
                    <div className=" w-[60px] h-[60px] flex flex-row justify-center items-center">
                         <img
                              src={logo}
                              alt="success logo"
                              className=" w-full h-full object-cover"
                         />
                    </div>
               </div> */}

               <div className=" w-full h-fit flex flex-row justify-center gap-[1rem]">
                    <ButtonComponent
                         onClick={() => {
                              navigate(-1);
                         }}
                         className=" w-[45%] h-[58px] rounded-[6px] border-[#00BD94] border-2 bg-clip-text text-transparent bg-mainLinearGradient text-[20px] font-inter font-[500] flex flex-row justify-center items-center"
                    >
                         Cancel
                    </ButtonComponent>
                    <ButtonComponent
                         isLoading={deleteButtonIsLoading}
                         onClick={clickHandler}
                         className=" w-[45%] h-[58px] rounded-[6px] bg-mainLinearGradient text-[20px] font-inter font-[500] text-[#fff] flex flex-row justify-center items-center"
                    >
                         Delete
                    </ButtonComponent>
               </div>
          </div>
     );
};

export default UserManagementDeleteLayout;
