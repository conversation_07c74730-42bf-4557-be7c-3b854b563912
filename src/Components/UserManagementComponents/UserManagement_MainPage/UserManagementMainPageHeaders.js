import React from "react";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import { useNavigate } from "react-router-dom";
import { routes } from "../../../Config/routesConfig";
import UseRouterAuthentication from "../../../Hooks/UseRouterAuthentication";

const UserManagementMainPageHeaders = () => {
  const navigate = useNavigate();
  const clickHandler = () => {
    navigate(routes?.userManagementCreateUser?.relativeLink);
  };

  // Router Authentication
  const { routeAuthentication } = UseRouterAuthentication();

  return (
    <div className=" w-full h-[80px] pt-[5rem] pb-[0rem] flex flex-row justify-between items-end  overflow-hidden">
      <div></div>
      <div className=" w-[150px]  h-fit flex flex-col justify-end gap-[1.5rem]">
        <div className=" w-fit h-fit flex flex-row items-center gap-[1.5rem]">
          {routeAuthentication("user", "write") && (
            <ButtonComponent
              onClick={clickHandler}
              className=" text-[10px] text-[#fff] w-[112px] h-[29px] rounded-[6px] bg-mainLinearGradient"
            >
              Create User
            </ButtonComponent>
          )}

          {/* <SearchBar
                              className=" w-[233px] h-[34px] bg-[#10888E33] bg-opacity-20 relative font-[600] font-lato text-[12px]"
                              isLogoAtFront={true}
                         /> */}
        </div>
      </div>
    </div>
  );
};

export default UserManagementMainPageHeaders;
