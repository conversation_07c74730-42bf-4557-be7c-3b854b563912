import React, { useState } from "react";
import CheckBoxComponent from "../../../BasicUIElements/CheckBoxComponent";
import UserManagementMainPageTableButton from "./Utils/UserManagementMainPageTableButton";
import { useNavigate } from "react-router-dom";
import { routes } from "../../../Config/routesConfig";
import { useDispatch } from "react-redux";
import UseRouterAuthentication from "../../../Hooks/UseRouterAuthentication";
import { userEditSliceActions } from "../../../Store/UserSlices/UserEditSlice";

const UserManagementMainPageIndiv = ({ content = {} }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [isHover, setIsHover] = useState(false);

  // // Router Authentication
  const { routeAuthentication } = UseRouterAuthentication();

  const editHandler = () => {
    let userDetails = {
      username: content?.username,
      id: content?._id,
    };

    dispatch(userEditSliceActions?.updateUserDetails(userDetails));

    // ---- Dashboard Access Parsing ----------------

    let access_keys = content?.accesslevel;

    let users = access_keys?.filter((item) => item?.access === "user")[0];
    let purchase = access_keys?.filter(
      (item) => item?.access === "Purchases"
    )[0];
    let sales = access_keys?.filter((item) => item?.access === "Sales")[0];
    let marketing = access_keys?.filter(
      (item) => item?.access === "marketing"
    )[0];
    let orders = access_keys?.filter((item) => item?.access === "Orders")[0];
    let customers = access_keys?.filter(
      (item) => item?.access === "customers"
    )[0];
    let networking = access_keys?.filter(
      (item) => item?.access === "networking"
    )[0];
    let serviceComplaints = access_keys?.filter(
      (item) => item?.access === "serviceComplaints"
    )[0];
    let tickets = access_keys?.filter((item) => item?.access === "tickets")[0];
    let taskManagement = access_keys?.filter(
      (item) => item?.access === "taskManagement"
    )[0];

    // -----------------------------------------------

    let permissionValues = {
      users: [
        users["Read"],
        users["write"],
        users["edit"],
        users["deletes"],
        users["insigths"],
      ],
      purchase: [
        purchase["Read"],
        purchase["write"],
        purchase["edit"],
        purchase["deletes"],
        purchase["insigths"],
      ],
      sales: [
        sales["Read"],
        sales["write"],
        sales["edit"],
        sales["deletes"],
        sales["insigths"],
      ],
      marketing: [
        marketing["Read"],
        marketing["write"],
        marketing["edit"],
        marketing["deletes"],
        marketing["insigths"],
      ],
      orders: [
        orders["Read"],
        orders["write"],
        orders["edit"],
        orders["deletes"],
        orders["insigths"],
      ],
      customers: [
        customers["Read"],
        customers["write"],
        customers["edit"],
        customers["deletes"],
        customers["insigths"],
      ],
      networking: [
        networking["Read"],
        networking["write"],
        networking["edit"],
        networking["deletes"],
        networking["insigths"],
      ],
      serviceComplaints: [
        serviceComplaints["Read"],
        serviceComplaints["write"],
        serviceComplaints["edit"],
        serviceComplaints["deletes"],
        serviceComplaints["insigths"],
      ],
      tickets: [
        tickets["Read"],
        tickets["write"],
        tickets["edit"],
        tickets["deletes"],
        tickets["insigths"],
      ],
      taskManagement: [
        taskManagement["Read"],
        taskManagement["write"],
        taskManagement["edit"],
        taskManagement["deletes"],
        taskManagement["insigths"],
      ],
    };

    dispatch(userEditSliceActions.updatePermissionValue(permissionValues));

    navigate(routes?.userManagementEdit?.directLink);
  };
  const viewHandler = () => {};
  const deleteHandler = () => {};

  return (
    <div
      key={content?._id}
      className={`w-full h-[70px] flex flex-row justify-between border-b-2 ${
        isHover ? " bg-[#10888E1A]" : ""
      } `}
      onMouseEnter={() => {
        setIsHover(true);
      }}
      onMouseLeave={() => {
        setIsHover(false);
      }}
    >
      <div className={`w-fit h-full flex flex-row gap-[0.5rem] `}>
        <div className=" w-[60px] h-full flex flex-row justify-center items-center">
          {/* <CheckBoxComponent value={false} /> */}
        </div>

        <div className=" w-fit h-full flex flex-row items-center gap-[0.8rem] ">
          <div className=" w-[53px] h-[53px] rounded-[50%] border-2 border-[#00BD94] flex flex-row justify-center items-center">
            <div className=" w-[90%] h-[90%] rounded-[50%] bg-[#10888E40] bg-opacity-25 overflow-hidden">
              <img
                className=" w-full h-full object-cover "
                src={
                  "https://www.kindpng.com/picc/m/252-2524695_dummy-profile-image-jpg-hd-png-download.png"
                }
                alt={content?.username}
              />
            </div>
          </div>
          <div className=" w-fit h-full flex flex-col justify-center">
            <div className=" w-fit h-fit text-[14px] font-[600] font-lato text-[#384F7A]">
              {content?.username}
            </div>
            <div className=" font-[500] font-lato text-[14px] text-[#76849F] w-fit h-fit flex flex-row gap-[0.25rem] ">
              <p>{content?.email}</p>
              {/* <p> | </p>
                                   <p>Created:</p>
                                   <p className=" font-[700]">12/06/2023</p> */}
            </div>
          </div>
        </div>
      </div>

      {isHover && (
        <div className=" w-fit h-full pe-[2rem] flex flex-row items-center gap-[1.5rem] ">
          {routeAuthentication("user", "edit") && (
            <UserManagementMainPageTableButton
              mode={"edit"}
              onClick={editHandler}
            />
          )}

          {routeAuthentication("user", "edit") && (
            <UserManagementMainPageTableButton
              mode={"view"}
              onClick={viewHandler}
            />
          )}

          {routeAuthentication("user", "delete") && (
            <UserManagementMainPageTableButton
              mode={"delete"}
              onClick={deleteHandler}
            />
          )}
        </div>
      )}
    </div>
  );
};

export default UserManagementMainPageIndiv;
