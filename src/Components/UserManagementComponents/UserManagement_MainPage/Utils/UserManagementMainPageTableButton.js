import React, { useState } from "react";
import ButtonComponent from "../../../../BasicUIElements/ButtonComponent";
import { FaRegEdit } from "react-icons/fa";
import { BiSolidUserDetail } from "react-icons/bi";
import { MdOutlineDelete } from "react-icons/md";

const UserManagementMainPageTableButton = ({ mode, onClick = () => {} }) => {
     const [isHover, setIsHover] = useState(false);

     return (
          <div
               className=" w-[39px] h-[39px] rounded-[50%]"
               onMouseEnter={() => {
                    setIsHover(true);
               }}
               onMouseLeave={() => {
                    setIsHover(false);
               }}
          >
               <ButtonComponent
                    onClick={onClick}
                    className={` w-[39px] h-[39px] rounded-[50%]  flex flex-row justify-center items-center duration-300 ${
                         isHover ? "bg-mainLinearGradient" : " bg-[#fff]"
                    } `}
               >
                    {mode === "edit" && (
                         <FaRegEdit
                              color={isHover ? "white" : "black"}
                              size={20}
                         />
                    )}
                    {mode === "view" && (
                         <BiSolidUserDetail
                              color={isHover ? "white" : "black"}
                              size={20}
                         />
                    )}
                    {mode === "delete" && (
                         <MdOutlineDelete
                              color={isHover ? "white" : "black"}
                              size={20}
                         />
                    )}
               </ButtonComponent>
          </div>
     );
};

export default UserManagementMainPageTableButton;
