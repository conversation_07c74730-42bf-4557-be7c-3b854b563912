import React, { useEffect } from "react";
import CheckBoxComponent from "../../../BasicUIElements/CheckBoxComponent";
import UserManagementMainPageIndiv from "./UserManagementMainPageIndiv";
import { useDispatch, useSelector } from "react-redux";
import { PaginationOfUserList } from "../../../Store/UserSlices/UserMainSlice";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import LeftArrowSVG from "../../../Assest/SVG/LeftArrowSVG";

const UserManagementMainPageListing = () => {
  const dispatch = useDispatch();
  const userMaster = useSelector((state) => {
    return state.userMain;
  });
  const auth = useSelector((state) => {
    return state.master.auth;
  });

  const userList = userMaster?.userList;
  let pageNo = userMaster?.pageNo;

  useEffect(() => {
    dispatch(PaginationOfUserList(pageNo, auth.token));
  }, []);

  const clickHandler = (p) => {
    dispatch(PaginationOfUserList(p, auth.token));
  };

  return (
    <div className=" w-full h-fit flex flex-col">
      <div className=" w-full flex flex-row justify-between items-center pe-8">
        <div className=" w-fit  h-[52px] gap-[1rem] flex flex-row justify-start items-center">
          <div className=" w-[60px] h-full flex flex-row justify-center items-center">
            {/* <CheckBoxComponent value={false} /> */}
          </div>
          {/* <h1 className=" w-fit h-fit text-[#384F7A] font-[400] text-[14px] ">
                              Search All
                         </h1> */}
        </div>
        <div className="w-fit h-fit flex flex-row items-center gap-3">
          <ButtonComponent
            onClick={() => {
              if (pageNo === 1) return;
              clickHandler(--pageNo);
            }}
            className=" w-[30px] h-[30px] flex flex-row justify-center items-center"
          >
            <LeftArrowSVG />
          </ButtonComponent>
          <div className=" font-poppins text-[16px] ">{pageNo}</div>
          <ButtonComponent
            onClick={() => {
              clickHandler(++pageNo);
            }}
            className=" w-[30px] h-[30px] flex flex-row justify-center items-center rotate-180"
          >
            <LeftArrowSVG />
          </ButtonComponent>
        </div>
      </div>

      {userList.map((item) => {
        return <UserManagementMainPageIndiv content={item} />;
      })}

      {[1, 2, 3].map((item) => {
        return <div className=" w-full h-[70px]"></div>;
      })}
    </div>
  );
};

export default UserManagementMainPageListing;
