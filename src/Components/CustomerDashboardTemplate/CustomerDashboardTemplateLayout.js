import React, { useEffect } from "react";
import UseRouterAuthentication from "../../Hooks/UseRouterAuthentication";
import { useDispatch, useSelector } from "react-redux";
import CustomerDashboardSideNavBar from "./CustomerDashboardSideNavBar";
import CustomerDashboardTopNavBarMobileScreen from "./CustomerDashboardTopNavBarMobileScreen";

const CustomerDashboardTemplateLayout = ({ children }) => {
  const master = useSelector((state) => {
    return state.master;
  });
  const dispatch = useDispatch();

  const {checkAuthentication} = UseRouterAuthentication();

  useEffect(() => {
    let auth = master?.auth;
    checkAuthentication("Auth");
  }, []);

  return (
    <>
      <div className=" w-full  h-screen md:flex hidden flex-row ">
        <CustomerDashboardSideNavBar />
        <div className=" flex-1 overflow-hidden ">{children}</div>
      </div>

      <div className=" w-full h-screen md:hidden flex-shrink-0 flex flex-col gap-[0.5rem]">
        <CustomerDashboardTopNavBarMobileScreen />
        <div className=" flex-1 ">{children}</div>
      </div>
    </>
  );
};

export default CustomerDashboardTemplateLayout;
