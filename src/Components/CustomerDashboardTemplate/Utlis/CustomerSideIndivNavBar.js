import React, { useMemo } from "react";
import { findActive } from "../../DashBoardTemplate/Utils/IndivSideNavBar";
import { NavLink, useLocation } from "react-router-dom";
import { customer_nav_bar_config_obj } from "../../../Config/NavBarConfig";
import CustomerNavSvg from "../../../Assest/SVG/CustomerNavSvg";
import OrderNavSvg from "../../../Assest/SVG/OrderNavSvg";

// Svg handler for each route / dashboard
const SvgElementForNavBar = ({ isActive = false, name = "" }) => {
  let activeColor = "#44A1A0";
  let inActiveColor = "white";
  return (
    <>
      {/* User Management Dashboard SVG */}
      {customer_nav_bar_config_obj.orders?.name === name && (
        <OrderNavSvg color={isActive ? activeColor : inActiveColor} />
      )}
    </>
  );
};

const CustomerSideIndivNavBar = ({
  content = {},
  children,
  isExtended = false,
}) => {
  const location = useLocation();

  let isActive = useMemo(() => {
    return findActive(location.pathname, content?.link);
  }, [location.pathname]);

  return (
    <>
      {isExtended ? (
        <div className=" w-full h-fit flex flex-row justify-start ps-2">
          <NavLink
            to={content?.link}
            className={` w-[90%] h-fit flex flex-row items-center ps-2 pe-2 pt-1 pb-1 ${
              isActive ? "bg-[#BDE0DD]" : ""
            } rounded-[10px]`}
          >
            <div className=" cursor-pointer w-[56px] h-[56px] flex flex-row justify-center items-center  rounded-[12px]">
              <div className="">
                <SvgElementForNavBar isActive={isActive} name={content?.name} />
              </div>
            </div>
            <div
              className={`font-[700] font-lato text-[18px] ${
                isActive ? "text-[#44A1A0]" : " text-[white]"
              }`}
            >
              {content?.name}
            </div>
          </NavLink>
        </div>
      ) : (
        <div className=" w-full h-fit flex flex-row justify-center">
          <NavLink
            to={content?.link}
            className={` cursor-pointer w-[56px] h-[56px] flex flex-row justify-center ${
              isActive ? "bg-[#BDE0DD]" : ""
            } items-center  rounded-[12px]`}
          >
            <div className="">
              <SvgElementForNavBar isActive={isActive} name={content?.name} />
            </div>
          </NavLink>
        </div>
      )}
    </>
  );
};

export default CustomerSideIndivNavBar;
