import React from "react";
import DharanLogoSideNav from "../DashBoardTemplate/Utils/DharanLogoSideNav";
import ButtonComponent from "../../BasicUIElements/ButtonComponent";
import { useDispatch, useSelector } from "react-redux";
import UseRouterAuthentication from "../../Hooks/UseRouterAuthentication";
import { MdArrowBackIos } from "react-icons/md";
import { MasterSliceActions } from "../../Store/MasterSlice/MasterSlice";
import { customer_nav_bar_config_obj } from "../../Config/NavBarConfig";
import CustomerSideIndivNavBar from "./Utlis/CustomerSideIndivNavBar";

const CustomerDashboardSideNavBar = () => {
  const { ui, auth } = useSelector((state) => {
    return state.master;
  });

  const { isExtended } = ui;
  const dispatch = useDispatch();

  const { checkAuthentication } = UseRouterAuthentication();

  return (
    <>
      <div
        className={` relative  ${
          isExtended ? "w-[254px]" : "w-[88px]"
        } h-full bg-mainLinearGradient pt-[10px] flex flex-col justify-between duration-300 `}
      >
        <ButtonComponent
          onClick={() => {
            dispatch(MasterSliceActions.modifyIsExtended(!isExtended));
          }}
          className={` absolute w-[30px] h-[30px] rounded-[50%]  ${
            !isExtended ? " rotate-180" : " rotate-0"
          } duration-300 bg-mainLinearGradient bg-opacity-10 flex flex-row justify-center items-center right-[-15px] z-[1500] top-[5rem]`}
        >
          <MdArrowBackIos color="#2422207A" />
        </ButtonComponent>

        <div className=" w-full">
          <DharanLogoSideNav />

          <div className=" w-full h-fit flex flex-col items-center gap-[0.5rem] mt-[1rem]">
            <CustomerSideIndivNavBar
              isExtended={isExtended}
              content={customer_nav_bar_config_obj?.orders}
            ></CustomerSideIndivNavBar>
          </div>
        </div>
        {/* <div>
                       <IndivSideNavBar content={ExitConfig} />
                  </div> */}
      </div>
    </>
  );
};

export default CustomerDashboardSideNavBar;
