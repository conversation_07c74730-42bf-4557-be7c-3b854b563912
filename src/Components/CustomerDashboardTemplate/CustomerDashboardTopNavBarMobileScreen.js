import React from "react";
import ButtonComponent from "../../BasicUIElements/ButtonComponent";
import { MasterSliceActions } from "../../Store/MasterSlice/MasterSlice";
import { useDispatch, useSelector } from "react-redux";
import { NavLink, useLocation } from "react-router-dom";
import { findActive } from "../DashBoardTemplate/Utils/IndivSideNavBar";
import { customer_nav_bar_config_obj } from "../../Config/NavBarConfig";
import { FaXmark } from "react-icons/fa6";
import { RxHamburgerMenu } from "react-icons/rx";
import OrderNavSvg from "../../Assest/SVG/OrderNavSvg";
import UseRouterAuthentication from "../../Hooks/UseRouterAuthentication";

const HeadingText = ({ location }) => {
  return (
    <>
      {findActive(
        location.pathname,
        customer_nav_bar_config_obj.orders?.link
      ) && (
        <h1 className="  font-poppins font-[800] text-[14px] text-[#fff]">
          {customer_nav_bar_config_obj.orders?.name}
        </h1>
      )}
    </>
  );
};

const SvgElementForNavBar = ({ isActive = true, name = "" }) => {
  return (
    <>
      {customer_nav_bar_config_obj?.orders?.name === name && (
        <OrderNavSvg color={"#44A1A0"} />
      )}
    </>
  );
};

const IndivNavListTopNavBar = ({ children, content }) => {
  const dispatch = useDispatch();
  const location = useLocation();

  let isActive = findActive(location.pathname, content?.link);
  return (
    <NavLink
      to={content?.link}
      onClick={() => {
        dispatch(MasterSliceActions.modifyIsMobileScreenOpen(false));
      }}
      className={`w-full min-h-[60px]  ps-[7vw] flex flex-row justify-start items-center gap-[1rem] ${
        isActive ? "bg-[#F1F7FE] " : ""
      }`}
    >
      <>
        <div className=" w-fit h-full justify-center flex flex-col">
          <div className=" w-[45px] h-[45px]  flex flex-row justify-center items-center ">
            <SvgElementForNavBar name={content?.name} />
          </div>
        </div>
        <h1 className="font-[400] font-poppins text-[14px] text-[#019BA2]">
          {content?.name}
        </h1>
      </>
    </NavLink>
  );
};

const TotalListComponent = () => {
  const {checkAuthentication} = UseRouterAuthentication();
  return (
    <div className=" w-full h-screen absolute bg-[#fff] left-0 top-0  dropdown duration-300 flex flex-col overflow-auto gap-[1rem] pt-[90px] pb-[20px] z-[10000]">
      <IndivNavListTopNavBar
        content={customer_nav_bar_config_obj.orders}
      ></IndivNavListTopNavBar>
    </div>
  );
};

const CustomerDashboardTopNavBarMobileScreen = () => {
  const dispatch = useDispatch();

  const ui = useSelector((state) => {
    return state.master.ui;
  });

  const location = useLocation();

  return (
    <div className=" w-full h-[75px] flex flex-row gap-[4vw] bg-mainLinearGradient flex-shrink-0  z-[20] relative ">
      <div className=" w-full h-full flex flex-row gap-[4vw] bg-mainLinearGradient flex-shrink-0 ps-6 pe-6 z-[10001] relative">
        <div className=" w-fit h-full flex flex-col justify-center">
          <ButtonComponent
            className=" w-[40px] h-[40px] duration-300 "
            onClick={() => {
              dispatch(
                MasterSliceActions.modifyIsMobileScreenOpen(
                  !ui.isMobileScreenOpen
                )
              );
            }}
          >
            {ui.isMobileScreenOpen ? (
              <FaXmark size={30} color={"grey"} />
            ) : (
              <RxHamburgerMenu size={30} color={"white"} />
            )}
          </ButtonComponent>
        </div>
        <div className=" w-fit h-full flex flex-col justify-center">
          <HeadingText location={location} />
        </div>
      </div>

      {ui.isMobileScreenOpen && <TotalListComponent />}
    </div>
  );
};

export default CustomerDashboardTopNavBarMobileScreen;
