import { useDispatch, useSelector } from "react-redux";
import { API_ENDPOINTS, api_headers, BaseURL } from "../../API/APIConfig";
import { GetAPI } from "../../API/GetAPI";
import { parser_table_filter_data } from "../../UI_Enginee/TableHeaderEnginee/TableHeaderEnginee";
import { TicketsSliceActions } from "../../Store/TicketsSlice/TicketsSlice";
import { PostAPI } from "../../API/PostAPI";

export let dateObj = [
  {
    id: "Last year",
    content: "Last year",
  },
  {
    id: "Last Month",
    content: "Last Month",
  },
  {
    id: "Last Week",
    content: "Last Week",
  },
  {
    id: "Custom Date",
    content: "Custom Date",
  },
  {
    id: "Till now",
    content: "Till now",
  },
];

const UseTickets = () => {
  const dispatch = useDispatch();

  const { filter, table } = useSelector((state) => {
    return state.tickets;
  });

  const { auth } = useSelector((state) => {
    return state.master;
  });

  // fetch_list
  const fetch_list = (updated_filter_state = null, updated_date = null) => {
    if (!auth?.id) {
      return;
    }

    let filterObj = parser_table_filter_data(table.header, filter.state);
    let filterState = filterObj?.state;
    let isFilter = filterObj?.isFilter;

    let customDate = "Custom Date";
    let startDate = filter?.date?.startDate;
    let endDate = filter?.date?.endDate;
    let filter_options = filter?.date?.filter?.content;

    if (updated_filter_state) {
      let filterState_parser = parser_table_filter_data(
        table.header,
        updated_filter_state
      );
      filterState = filterState_parser?.state;
      isFilter = filterState_parser?.isFilter;
    }

    if (updated_date) {
      startDate = updated_date?.startDate;
      endDate = updated_date?.endDate;
      filter_options = updated_date?.filter?.content;
    }

    // console.log("----------------------------");
    // console.log(filterState, isFilter);
    // console.log(startDate, "---", endDate, "---", filter_options);

    if (isFilter) {
      dispatch(tickets_filter_list_thunk(filterState, auth));
    } else {
      dispatch(tickets_list_thunk(auth));
    }
    // Header Count
    dispatch(tickets_header_count_thunk(auth));
  };

  const fetchList = () => {
    if (!auth?.id) {
      return;
    }

    fetch_list();

    // let filterObj = parser_table_filter_data(table.header, filter.state);

    // if (filterObj?.isFilter) {
    //   dispatch(tickets_filter_list_thunk(filterObj?.state, auth));
    // } else {
    //   dispatch(tickets_list_thunk(auth));
    // }

    // dispatch(tickets_header_count_thunk(auth));
  };

  const fetchListWithNewFilter = (filterObj) => {
    if (!auth?.id) {
      return;
    }

    window.alert(
      "Bug in the tickets dashboard filter. Please connect with developer "
    );

    // dispatch(tickets_filter_list_thunk(filterObj, auth));
  };
  return { fetchList, fetchListWithNewFilter, fetch_list };
};

export default UseTickets;

// LIST
const tickets_list_thunk = (auth) => {
  return async (dispatch) => {
    // API
    let api = new URL(BaseURL.mainURl + API_ENDPOINTS.ticketList);

    api.searchParams.set("userId", auth.id);

    let headers = { ...api_headers, Authorization: auth.token };

    let response = await GetAPI(api, headers);

    if (response?.ok) {
      let responsedata = await response?.json();

      let array = responsedata?.data;

      if (responsedata?.data?.raisedBy?.length >= 0) {
        array = [
          ...responsedata?.data?.raisedBy,
          ...responsedata?.data?.raisedTo,
        ];
      }

      dispatch(TicketsSliceActions.updateRaiseByList(array?.reverse()));
    }
  };
};

// Filter API
const tickets_filter_list_thunk = (filter, auth) => {
  return async (dispatch) => {
    let url = new URL(BaseURL?.mainURl + API_ENDPOINTS?.ticketListWithFilter);

    url.searchParams.set("userId", auth?.id);

    let headers = { ...api_headers, userId: auth?.id };

    let body = filter;

    let response = await PostAPI(url, body, headers);

    if (response?.ok) {
      let responsedata = await response?.json();

      let array = responsedata;

      if (responsedata?.raisedBy?.length >= 0) {
        array = [...responsedata?.raisedBy, ...responsedata?.raisedTo];
      }

      dispatch(TicketsSliceActions.updateRaiseByList(array?.reverse()));
    }
  };
};

// Header Count API
const tickets_header_count_thunk = (auth) => {
  return async (dispatch) => {
    let url = new URL(BaseURL?.mainURl + API_ENDPOINTS?.ticketHeaderCount);
    url.searchParams.set("userId", auth?.id);

    let header = { ...api_headers };

    let response = await GetAPI(url, header);

    if (response?.ok) {
      let responsedata = await response?.json();

      let headerCount = [
        { title: "Tickets", count: responsedata["Total"] },
        { title: "Open", count: responsedata["Open"] },
        { title: "In Progress", count: responsedata["In Progress"] },
        { title: "Closed", count: responsedata["Closed"] },
      ];

      dispatch(TicketsSliceActions?.updateHeaderCount(headerCount));
    }
  };
};
