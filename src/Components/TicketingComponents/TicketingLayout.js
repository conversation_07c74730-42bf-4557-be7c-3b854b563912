import React, { useEffect } from "react";
import SectionHeaders from "../Utils/SectionHeaders";
import TicketingDataBox from "./TicketingDataBox";
import TicketingTableLayout from "./TicketingTable/TicketingTableLayout";
import ButtonComponent from "../../BasicUIElements/ButtonComponent";
import { useNavigate } from "react-router-dom";
import { routes } from "../../Config/routesConfig";
import { useDispatch, useSelector } from "react-redux";
import {
  TicketsSliceActions,
  userListFetch,
} from "../../Store/TicketsSlice/TicketsSlice";
import UseRouterAuthentication from "../../Hooks/UseRouterAuthentication";
import UseTickets, { dateObj } from "./UseTickets";
import DateFilterTemplateComponent from "../../UI_templates/DateFilterTemplate/DateFilterTemplateComponent";
const TicketingLayout = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { fetch_list } = UseTickets();

  const { filter, headerCount } = useSelector((state) => {
    return state.tickets;
  });

  const { auth, access } = useSelector((state) => {
    return state.master;
  });

  const { isDashboardAccessable, routeAuthentication } =
    UseRouterAuthentication();
  useEffect(() => {
    isDashboardAccessable("tickets");
    dispatch(userListFetch(auth));
    fetch_list();
  }, [auth, access]);

  return (
    <div className=" w-full h-full flex flex-col overflow-y-auto ">
      <div className=" w-full h-[70px]  flex flex-row justify-between items-center ps-8 pe-8 mt-[0.5rem]">
        <SectionHeaders>Ticketing</SectionHeaders>
        {routeAuthentication("tickets", "write") && (
          <ButtonComponent
            onClick={() => {
              navigate("?page=" + routes?.ticketingadd?.searchParams);
            }}
            className=" w-[205px] h-[36px] bg-mainLinearGradient rounded-[12px] font-inter font-[700] text-[14px] text-[#fff] "
          >
            + Add a New Ticket
          </ButtonComponent>
        )}
      </div>

      <div className=" w-full mt-[2rem] ps-3 mb-[2rem]">
        <TicketingDataBox headerCount={headerCount} />
      </div>
      <div className=" w-full h-[2rem]"></div>

      <DateFilterTemplateComponent
        fetch={fetch_list}
        updateSlice={TicketsSliceActions.updateFilterDate}
        dateObj={dateObj}
        customDate="Custom Date"
        date={filter?.date}
      />

      <TicketingTableLayout />
    </div>
  );
};

export default TicketingLayout;
