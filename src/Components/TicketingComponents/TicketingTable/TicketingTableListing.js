import React from "react";
import TicketingTableIndivRow from "./TicketingTableIndivRow";
import { useSelector } from "react-redux";

const TicketingTableListing = ({ list = [] }) => {
  return (
    <div className=" w-fit h-fit min-h-[210px] pb-[4rem] ">
      {list?.map((item) => {
        return <TicketingTableIndivRow content={item} />;
      })}
    </div>
  );
};

export default TicketingTableListing;
