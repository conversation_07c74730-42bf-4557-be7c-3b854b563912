import React, { useState } from "react";
import TicketingTableTopper from "./TicketingTableTopper";
import TicketingTableHeader from "./TicketingTableHeader";
import TicketingTableListing from "./TicketingTableListing";
import TicketsFilterTab from "./TicketsFilterTab";
import { useSelector } from "react-redux";

function isSubarrayPresent(str2, str1) {
  // Check if the subArrayString is a substring of fullString
  return str2
    ?.toString()
    ?.toLowerCase()
    ?.includes(str1?.toString()?.toLowerCase());
}

const TicketingTableLayout = () => {
  const { raisedByList, raisedToList } = useSelector((state) => {
    return state.tickets;
  });

  let list = [...raisedByList, ...raisedToList];

  const [searchState, setSearchState] = useState("");

  const update = (e) => {
    setSearchState(e);
  };

  list = list?.filter((item) => {
    return isSubarrayPresent(item?.name, searchState);
  });
  return (
    <>
      <TicketingTableTopper state={searchState} setState={update} />
      <TicketsFilterTab />
      <div className=" w-fit h-fit flex flex-col overflow-auto">
        <div className=" w-fit h-fit flex flex-col ">
          <TicketingTableHeader />
          <TicketingTableListing list={list} />
        </div>
      </div>

      {/* <div className=" flex-col flex-shrink-0 flex">
        <TicketingTableTopper state={searchState} setState={update} />
        <TicketsFilterTab />
        <div className=" w-full h-fit flex flex-col overflow-auto">
          <div className=" w-full h-fit flex flex-col ">
            <TicketingTableHeader />
            <TicketingTableListing list={list} />
          </div>
        </div>
      </div> */}
    </>
  );
};

export default TicketingTableLayout;
