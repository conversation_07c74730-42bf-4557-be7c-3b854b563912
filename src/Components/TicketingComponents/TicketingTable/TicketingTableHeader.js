import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { TicketsSliceActions } from "../../../Store/TicketsSlice/TicketsSlice";
import TableHeaderEnginee, {
  parser_table_filter_data,
} from "../../../UI_Enginee/TableHeaderEnginee/TableHeaderEnginee";
import TableHeaderEngineeWrapper from "../../../UI_Enginee/TableHeaderEnginee/TableHeaderEngineeWrapper";
import UseTickets from "../UseTickets";

const TicketingTableHeader = () => {
  const { filter, table } = useSelector((state) => {
    return state.tickets;
  });
  const auth = useSelector((state) => {
    return state.master.auth;
  });

  const dispatch = useDispatch();
  const { fetchListWithNewFilter, fetch_list } = UseTickets();

  const updateState = (key, value) => {
    let updatedState = {
      ...filter?.state,
      [key]: value,
    };
    dispatch(TicketsSliceActions.updateFilterState(updatedState));

    fetch_list(updatedState);

    // let filterObj = parser_table_filter_data(table.header, updatedState);

    // if (filterObj?.isFilter) {
    //   fetchListWithNewFilter(filterObj.state);
    // } else {
    //   fetchListWithNewFilter({});
    // }
  };

  return (
    <TableHeaderEngineeWrapper className="w-fit h-[52px] border-b-2 flex flex-row">
      <div className=" w-[60px] h-full flex flex-row justify-center items-center">
        {/* <CheckBoxComponent value={false} onClick={() => {}} /> */}
      </div>

      {/* ----------------------------------------------- */}

      <TableHeaderEnginee
        table={table.header}
        state={filter?.state}
        updateState={updateState}
      />
    </TableHeaderEngineeWrapper>
  );
};

export default TicketingTableHeader;
