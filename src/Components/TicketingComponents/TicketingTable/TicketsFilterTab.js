import React from "react";
import { useDispatch, useSelector } from "react-redux";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import UseTickets from "../UseTickets";
import { TicketsSliceActions } from "../../../Store/TicketsSlice/TicketsSlice";
import { parser_table_filter_data } from "../../../UI_Enginee/TableHeaderEnginee/TableHeaderEnginee";

const Selected = ({ text = "All", onClick }) => {
  return (
    <ButtonComponent
      onClick={onClick}
      className=" w-fit h-[48px] border-b-[4px] border-[#019BA2] ps-6 pe-6 font-poppins text-[16px] text-[#019BA2] font-[400]"
    >
      {text}
    </ButtonComponent>
  );
};

const Upselected = ({ text = "", onClick }) => {
  return (
    <ButtonComponent
      onClick={onClick}
      className=" w-fit h-[48px] border-b-[4px]  border-opacity-0 ps-6 pe-6 font-poppins text-[16px]  font-[400]"
    >
      {text}
    </ButtonComponent>
  );
};

// Helper Function
const isAll = (content = []) => {
  // If no status and more than one status are selected, then all should light up
  if (content?.length <= 0) {
    return true;
  }
  return false;
};

const isCompleted = (content = [], id) => {
  // If only completed status is picked,
  if (content?.length == 1 && content[0]?.id === id) {
    return true;
  }
  return false;
};

const TicketsFilterTab = () => {
  const dispatch = useDispatch();
  const { filter, table } = useSelector((state) => {
    return state.tickets;
  });

  const { fetchListWithNewFilter, fetch_list } = UseTickets();

  const updateState = (key, value) => {
    let updatedState = {
      ...filter?.state,
      [key]: value,
    };
    dispatch(TicketsSliceActions.updateFilterState(updatedState));

    fetch_list(updatedState);

    // let filterObj = parser_table_filter_data(table.header, updatedState);

    // if (filterObj?.isFilter) {
    //   fetchListWithNewFilter(filterObj.state);
    // } else {
    //   fetchListWithNewFilter({});
    // }
  };
  return (
    <div className="min-w-full  w-full overflow-auto flex flex-row justify-between ps-6 pe-6 ">
      <div className=" w-fit flex flex-row flex-shrink-0 ">
        {isAll(filter?.state?.status) ? (
          <Selected onClick={() => {}} text="All" />
        ) : (
          <Upselected
            onClick={() => {
              updateState("status", []);
            }}
            text="All"
          />
        )}

        {isCompleted(filter?.state.status, "New") ? (
          <Selected onClick={() => {}} text="New" />
        ) : (
          <Upselected
            onClick={() => {
              updateState("status", [{ id: "New", content: "New" }]);
            }}
            text="New"
          />
        )}
        {isCompleted(filter?.state.status, "In Progress") ? (
          <Selected onClick={() => {}} text="In Progress" />
        ) : (
          <Upselected
            onClick={() => {
              updateState("status", [
                { id: "In Progress", content: "In Progress" },
              ]);
            }}
            text="In Progress"
          />
        )}
        {isCompleted(filter?.state.status, "Closed") ? (
          <Selected onClick={() => {}} text="Closed" />
        ) : (
          <Upselected
            onClick={() => {
              updateState("status", [{ id: "Closed", content: "Closed" }]);
            }}
            text="Closed"
          />
        )}
      </div>
    </div>
  );
};

export default TicketsFilterTab;
