import React, { useState } from "react";
import {
  TicketType_Closed,
  TicketType_TicketsDropdown,
  TicketingTableConfig,
  returnUserNameWIthID_tickets,
  selectColorCode_Ticket,
} from "../../../Config/TicketingConfig";
import { dateFormatFunctionSalesAndMarketing } from "../../../Store/SalesAndMarketingSlice/SalesAndMarketingSlice";
import TableTextContent from "../../Utils/TableStructures/TableTextContent";

import edit from "../../../Assest/Utils/edit.png";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { routes } from "../../../Config/routesConfig";
import { findContentObj } from "../../../Config/ServiceComplaintsTableConfig";
import { API_ENDPOINTS, BaseURL, defaultHeaders } from "../../../API/APIConfig";
import { PostAPI } from "../../../API/PostAPI";
import SelectComponent from "../../../BasicUIElements/SelectComponent";
import Spinner from "../../../BasicUIElements/Spinner";
import UseTickets from "../UseTickets";
import TableTextAreaTextModelDisplay from "../../../UI_Enginee/TableListEnginee/TableTextAreaTextModelDisplay";

export const getAssigneeString = (array, userlist) => {
  let arrayf = [];
  for (let i = 0; i < array.length; i++) {
    arrayf.push(returnUserNameWIthID_tickets(array[i], userlist));
  }
  let string = "";

  for (const f of arrayf) {
    string = string + f + ", ";
  }

  return string;
};

function isWithin30Seconds(date) {
  const now = new Date();
  const differenceInSeconds = Math.abs((now - date) / 1000); // Calculate absolute difference in seconds
  return differenceInSeconds <= 30;
}

const TicketingTableIndivRow = ({ content }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { fetch_list } = UseTickets();

  const { userList } = useSelector((state) => {
    return state.master.ui;
  });
  const name = content?.name;
  const ticketDate = dateFormatFunctionSalesAndMarketing(content.createdAt);
  const ticketRaisedby = returnUserNameWIthID_tickets(
    content?.raisedBy,
    userList
  );
  const assignee = getAssigneeString(content?.raisedTo, userList);
  const department = content?.department;
  const ticketDescription = content?.description;
  const ticketType = findContentObj(
    content?.status,
    TicketType_TicketsDropdown
  );
  const expectedResolutionTime = dateFormatFunctionSalesAndMarketing(
    content.timeline
  );
  const timeTaken = content.time_taken
    ? content.time_taken + (content.time_taken > 1 ? " Days" : " Day")
    : "-";

  let underWithin = isWithin30Seconds(new Date(content?.createdAt));

  const editClickHandler = () => {
    navigate(
      "?page=" + routes?.ticketingedit?.searchParams + "&id=" + content?._id
    );
  };
  const deleteClickHandler = () => {
    // navigate(routes.deleteOrderManagement.relativeLink);
  };

  //----------------------------------------------------------------------------------------------------------------

  const [isLoading, setIsLoading] = useState(false);
  const auth = useSelector((state) => {
    return state.master.auth;
  });

  const updateLeadStatus = async (e) => {
    setIsLoading(true);

    const response = await TicketEdit(
      {
        status: e.content,
      },
      content._id,
      auth
    );

    if (response) {
      fetch_list();
    }

    setIsLoading(false);
  };

  //---------------------------------------------------------------------------------------------------------------

  return (
    <div
      key={content.id}
      className={` w-fit h-[52px] border-b-2 flex flex-row hover:bg-[#F0FEFF] ${
        underWithin ? "bg-[#F0FEFF]" : ""
      } `}
    >
      <div className=" w-[60px] h-full flex flex-row justify-center items-center">
        <ButtonComponent
          onClick={editClickHandler}
          className=" w-[24px] h-[24px] flex flex-rwo justify-center items-center "
        >
          <img
            src={edit}
            className=" w-full h-full object-contain"
            alt="edit"
          />
        </ButtonComponent>
      </div>
      <TableTextContent
        size={TicketingTableConfig[0].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {content?.ticket_id}
      </TableTextContent>
      <TableTextAreaTextModelDisplay
        title="Ticket Name"
        size={TicketingTableConfig[0].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {name}
      </TableTextAreaTextModelDisplay>
      <TableTextContent
        size={TicketingTableConfig[1].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {ticketDate}
      </TableTextContent>
      <TableTextContent
        size={TicketingTableConfig[2].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] truncate"
      >
        {ticketRaisedby}
      </TableTextContent>
      {/* <TableTextContent
        size={TicketingTableConfig[3].className}
        className=" font-[400] font-inter text-[11px] text-[#6E7079] overflow-auto"
      ></TableTextContent> */}
      <div
        className={
          TicketingTableConfig[3].className +
          ` overflow-auto font-inter text-[10px] text-[#6E7079] pt-1 pb-1 pe-3  ${
            content?.raisedTo?.length > 2
              ? "text-[10px]"
              : " text-[14px] truncate"
          } `
        }
      >
        <div className=" w-full h-full overflow-y-auto overflow-x-hidden ps-[0rem]">
          {assignee}
        </div>
      </div>
      <TableTextContent
        size={TicketingTableConfig[4].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {department}
      </TableTextContent>
      <TableTextAreaTextModelDisplay
        size={TicketingTableConfig[5].className}
        title="Ticket Description"
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {ticketDescription}
      </TableTextAreaTextModelDisplay>
      {/* <div
        className={
          TicketingTableConfig[5].className +
          "overflow-auto font-inter text-[14px] text-[#6E7079] pt-1 pb-1 pe-3"
        }
      >
        <div className=" w-full h-full overflow-auto">{ticketDescription}</div>
      </div> */}
      <TableTextContent
        size={TicketingTableConfig[6].className}
        className=" font-[400] font-inter text-[14px] text-center text-[#6E7079]"
      >
        <div className=" w-full flex flex-row items-center ps-[2rem]">
          <SelectComponent
            className={` w-[150px] h-[30px]  rounded-[10px]  font-[600] text-[14px]  pe-4 ${selectColorCode_Ticket(
              ticketType
            )}`}
            value={ticketType}
            optionarr={TicketType_TicketsDropdown}
            updateFunction={(e) => {
              if (ticketType.content === e.content) return;
              updateLeadStatus(e);
            }}
          >
            <>{isLoading && <Spinner />}</>
          </SelectComponent>
        </div>
      </TableTextContent>
      <TableTextContent
        size={TicketingTableConfig[7].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-[2rem]"
      >
        {expectedResolutionTime}
      </TableTextContent>
      <TableTextAreaTextModelDisplay
        title="Remarks"
        size={TicketingTableConfig[8].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-[2rem]"
      >
        {content?.remarks}
      </TableTextAreaTextModelDisplay>
      <TableTextContent
        size={TicketingTableConfig[9].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-[2rem]"
      >
        {timeTaken}
      </TableTextContent>
    </div>
  );
};

export default TicketingTableIndivRow;

const TicketEdit = async (content, id, auth) => {
  let status = content?.status;

  let api = new URL(BaseURL.mainURl + API_ENDPOINTS.ticketEdit + "/" + id);

  let obj = {
    status,
  };

  if (status === TicketType_Closed.content) {
    obj = {
      ...obj,
      closedAt: new Date(),
    };
  }

  const header = {
    ...defaultHeaders,
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": ["POST", "GET", "OPTIONS", "DELETE", "PUT"],
    "Access-Control-Allow-Headers": [
      "append",
      "delete",
      "entries",
      "foreach",
      "get",
      "has",
      "keys",
      "set",
      "values",
      "Authorization",
    ],

    redirect: "follow",

    Authorization: auth.token,
  };

  let response = await PostAPI(api, obj, header, "PUT");

  if (response.ok) {
    return true;
  }

  return false;
};
