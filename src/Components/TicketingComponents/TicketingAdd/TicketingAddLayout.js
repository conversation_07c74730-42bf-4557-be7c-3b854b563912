import React, { useEffect, useState } from "react";
import Model<PERSON>eaderAndRoute from "../../Utils/UtilsComponents/ModelHeaderAndRoute";
import { useNavigate } from "react-router-dom";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import { useDispatch, useSelector } from "react-redux";
import { TicketsSliceActions } from "../../../Store/TicketsSlice/TicketsSlice";
import { ticketAddForm_Config } from "../../../Config/TicketingConfig";
import {
  API_ENDPOINTS,
  api_headers,
  BaseURL,
  defaultHeaders,
} from "../../../API/APIConfig";
import { PostAPI } from "../../../API/PostAPI";
import { routes } from "../../../Config/routesConfig";
import UseRouterAuthentication from "../../../Hooks/UseRouterAuthentication";
import {
  formData_parser,
  formEnginee_Formatee_Structure_and_State,
} from "../../../Dashboard_API_calling_and_formatting/Form_Formate_and_API";
import FormEnginee from "../../../UI_Enginee/Form/FormEnginee";
import { vaildator_Form_function } from "../../../UI_Enginee/Form/FormEngineeConfig";
import UseTickets from "../UseTickets";
import { GetAPI } from "../../../API/GetAPI";
import toast from "react-hot-toast";

const TicketingAddLayout = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { fetchList } = UseTickets();
  const { routeAuthentication } = UseRouterAuthentication();
  const { auth, access } = useSelector((state) => {
    return state.master;
  });
  useEffect(() => {
    if (!routeAuthentication("tickets", "write")) {
      navigate(routes?.ticketing?.directLink);
    }
  }, [auth, access]);

  // --------------------------------------------------------------

  const { add } = useSelector((state) => {
    return state.tickets;
  });

  const [state, setState] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  const fetch = async () => {
    let response = await fetch_ticket_add_form();

    if (response.structure?.length == 0) {
      return false;
    }

    let ss = formEnginee_Formatee_Structure_and_State(response.structure);
    setState(ss.state);
    dispatch(TicketsSliceActions.updateAddStructure(ss));

    return true;
  };

  useEffect(() => {
    // Setting structure and state if not added

    if (add.structure?.length === 0) {
      if (fetch()) {
      } else {
        window.alert("Please check the network and refresh");
      }
    } else {
      setState(add.state);
    }
  }, [add.state]);

  const updateFunctionOfForm = (key, value) => {
    // Updating the state
    let updateState = {
      ...state,
      [key]: { ...state[key], value: value },
    };
    setState(updateState);
  };

  // Submit Handler
  const submitHandler = async () => {
    let vaildate = vaildator_Form_function(add.structure, state);

    setState(vaildate?.validate_Obj);
    if (!vaildate?.isFormValid) {
      toast.error("Fill up all fields ", {
        position: "top-right",
      });
      return;
    }

    let parser_obj = formData_parser(add.structure, state);

    let obj = {
      ...parser_obj.parser_obj,
      raisedBy: auth?.id,
      assignee: parser_obj?.parser_obj?.assignee,
    };

    setIsLoading(true);
    let toast_id = toast.loading("Posting", { position: "top-right" });
    let response = await ticketsAddFunction(obj, auth);

    setIsLoading(false);

    if (response?.ok) {
      fetchList();
      fetch();
      navigate(routes.ticketing.directLink);
      toast.success("Added", { id: toast_id });
    } else {
      let responsedata = await response.json();
      toast.error(responsedata?.error, { id: toast_id });
    }
  };

  const closeModal = () => {
    navigate(routes.ticketing.directLink);
  };

  return (
    <div className=" w-[424px] max-h-[95vh] h-fit overflow-y-auto flex flex-col gap-[0rem] bg-[#fff] rounded-[6px] p-4 z-[150]">
      <ModelHeaderAndRoute
        className="w-full h-fit flex flex-row justify-between ps-4 pe-4"
        title="Create a Ticket"
        onClick={closeModal}
      />

      <FormEnginee
        form={add.structure}
        formState={state}
        formStateFunction={updateFunctionOfForm}
      />

      <div className=" w-full h-fit mb-[2rem] mt-[2rem]">
        <div className=" w-full h-fit flex flex-row justify-center gap-[1rem]">
          <ButtonComponent
            onClick={closeModal}
            className=" w-[45%] h-[58px] rounded-[6px] border-[#00BD94] border-2 bg-clip-text text-transparent bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] flex flex-row justify-center items-center"
          >
            Cancel
          </ButtonComponent>
          <ButtonComponent
            onClick={submitHandler}
            isLoading={isLoading}
            className=" w-[45%] h-[58px] rounded-[6px] bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] text-[#fff] flex flex-row justify-center items-center"
          >
            Create
          </ButtonComponent>
        </div>
      </div>
    </div>
  );
};

export default TicketingAddLayout;

export const fetch_ticket_add_form = async () => {
  let url = new URL(BaseURL?.mainURl + API_ENDPOINTS?.ticketsAddFormBuilder);

  let headers = { ...api_headers };

  let response = await GetAPI(url, headers);

  if (response.ok) {
    let responsedata = await response.json();
    return responsedata;
  }
  return {
    structure: [],
  };
};

export const ticketsAddFunction = async (add, auth) => {
  let obj = add;

  const headers = {
    ...defaultHeaders,
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": ["POST", "GET", "OPTIONS", "DELETE", "PUT"],
    "Access-Control-Allow-Headers": [
      "append",
      "delete",
      "entries",
      "foreach",
      "get",
      "has",
      "keys",
      "set",
      "values",
      "Authorization",
    ],

    redirect: "follow",

    Authorization: auth.token,
  };

  const api = new URL(BaseURL.mainURl + API_ENDPOINTS.ticketAdd);

  const response = await PostAPI(api, obj, headers);

  return response;
};
