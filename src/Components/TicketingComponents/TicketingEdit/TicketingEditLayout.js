import React, { useEffect, useState } from "react";
import Model<PERSON>eaderAndRoute from "../../Utils/UtilsComponents/ModelHeaderAndRoute";
import { useNavigate, useSearchParams } from "react-router-dom";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import { useDispatch, useSelector } from "react-redux";

import UseTickets from "../UseTickets";
import { API_ENDPOINTS, api_headers, BaseURL } from "../../../API/APIConfig";

import { PostAPI } from "../../../API/PostAPI";
import {
  formData_parser,
  formEnginee_edit_Formatee_Structure_and_State,
} from "../../../Dashboard_API_calling_and_formatting/Form_Formate_and_API";
import { TicketsSliceActions } from "../../../Store/TicketsSlice/TicketsSlice";
import FormEnginee from "../../../UI_Enginee/Form/FormEnginee";
import { routes } from "../../../Config/routesConfig";
import toast from "react-hot-toast";

const TicketingEditLayout = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { fetchList } = UseTickets();
  const { auth } = useSelector((state) => {
    return state.master;
  });

  //-----------------------------------------------------------------------------------------

  const [searchParams] = useSearchParams();

  const { edit } = useSelector((state) => {
    return state?.tickets;
  });

  // state
  const [state, setState] = useState({});
  const [isContentLoading, setIsContentLoading] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    let id = searchParams?.get("id");

    let fetch = async () => {
      setIsContentLoading(true);

      let result = await fetch_tickets_edit_form_with_id(id);

      if (!result) {
        window?.confirm(
          "Error at fetching data, please check out network and try again"
        );
      }

      let ss = formEnginee_edit_Formatee_Structure_and_State(result);
      setState(ss.state);

      dispatch(
        TicketsSliceActions.updateEditStructureAndState({
          ...ss,
          id: id,
        })
      );

      setIsContentLoading(false);

      if (ss.structure?.length === 0) {
        return false;
      } else {
        return true;
      }
    };

    fetch();
  }, [edit.id]);

  const updateFunctionOfForm = (key, value) => {
    setState((state) => {
      return { ...state, [key]: { ...state[key], value: value } };
    });
  };

  const closeModal = () => {
    navigate(routes.ticketing.directLink);
  };

  const clickHandler = async () => {
    let parser_obj = formData_parser(edit.structure, state);

    setIsLoading(true);

    let toast_id = toast.loading("Updating", { position: "top-right" });

    let response = await TicketEdit(
      parser_obj?.parser_obj,
      searchParams?.get("id"),
      auth
    );

    if (response?.ok) {
      fetchList();
      closeModal();
      toast.success("Updated", { id: toast_id });
    } else {  
      let responsedata = await response.json();
      toast.error(responsedata?.error, { id: toast_id });
    }
    setIsLoading(false);
  };

  //----------------------------------------------------------------------------------------

  return (
    <div className=" w-[424px] max-h-[95vh] h-fit overflow-y-auto flex flex-col  bg-[#fff] rounded-[6px] p-4 z-[150]">
      <ModelHeaderAndRoute
        className="w-full h-fit flex flex-row justify-between ps-4 pe-4 "
        title="Edit a Ticket"
        onClick={() => {
          closeModal();
        }}
      />

      <FormEnginee
        form={edit?.structure}
        formStateFunction={updateFunctionOfForm}
        formState={state}
      />

      <div className=" w-full h-fit mb-[2rem]">
        <div className=" w-full h-fit flex flex-row justify-center gap-[1rem] mt-[1.5rem]">
          <ButtonComponent
            onClick={() => {
              closeModal();
            }}
            className=" w-[45%] h-[58px] rounded-[6px] border-[#00BD94] border-2 bg-clip-text text-transparent bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] flex flex-row justify-center items-center"
          >
            Cancel
          </ButtonComponent>
          <ButtonComponent
            onClick={clickHandler}
            isLoading={isLoading}
            className=" w-[45%] h-[58px] rounded-[6px] bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] text-[#fff] flex flex-row justify-center items-center"
          >
            Edit
          </ButtonComponent>
        </div>
      </div>
    </div>
  );
};

export default TicketingEditLayout;

const fetch_tickets_edit_form_with_id = async (id) => {
  let url = new URL(BaseURL?.mainURl + API_ENDPOINTS?.ticketsEditFormBuilder);

  let headers = { ...api_headers };

  const response = await PostAPI(url, { id: id }, headers);

  if (response?.ok) {
    let responsedata = await response?.json();
    return responsedata?.structure;
  }

  return null;
};

const TicketEdit = async (content, id, auth) => {
  let api = new URL(BaseURL.mainURl + API_ENDPOINTS.ticketEdit + "/" + id);

  let obj = {
    ...content,
    closedAt: new Date(),
  };

  const header = {
    ...api_headers,
    Authorization: auth.token,
  };

  let response = await PostAPI(api, obj, header, "PUT");

  return response;
};
