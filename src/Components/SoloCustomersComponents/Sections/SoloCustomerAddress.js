import React from "react";
import location from "../../../Assest/Utils/Location.png";
import DataStructureBoxHeader from "../../Utils/DataStuctureBoxes/DataStructureBoxHeader";
import IndivSoloCustomer from "../Utils/IndivSoloCustomer";

const SoloCustomerAddress = () => {
     return (
          <div className=" w-[530px] h-[145px] flex flex-col justify-between border-2 p-3 rounded-[10px]">
               <DataStructureBoxHeader logo={location} />
               <div className=" w-full h-[55px] flex flex-row justify-between ">
                    <IndivSoloCustomer heading="Phone" stat="+918065650633" />
                    <IndivSoloCustomer
                         heading="Email"
                         stat="<EMAIL>"
                    />
                    <div></div>
               </div>
          </div>
     );
};

export default SoloCustomerAddress;
