import React, { useState } from "react";
import DataStructureBoxHeader from "../../Utils/DataStuctureBoxes/DataStructureBoxHeader";
import SelectComponent from "../../../BasicUIElements/SelectComponent";
import IndivIdualDataFieldLayout from "../../Utils/DataStuctureBoxes/IndivIdualDataFieldLayout";
import logo from "../../../Assest/Utils/Bagwhite.png";

const SoloCustomerStatOne = ({ content }) => {
  return (
    <div className=" w-[410px] h-[145px] flex flex-col flex-shrink-0 justify-between border-2 p-3 rounded-[10px]">
      <DataStructureBoxHeader logo={logo}></DataStructureBoxHeader>

      <div className=" w-full h-[55px] flex flex-row justify-between ">
        <IndivIdualDataFieldLayout
          heading="All Orders"
          stat={content?.allOrders}
          statColor="black"
        />
        <IndivIdualDataFieldLayout
          heading="Pending"
          stat={content?.pending}
          statColor="black"
        />
        <IndivIdualDataFieldLayout
          heading="Completed"
          stat={content?.completed}
          statColor="black"
        />
        <div></div>
      </div>
    </div>
  );
};

export default SoloCustomerStatOne;
