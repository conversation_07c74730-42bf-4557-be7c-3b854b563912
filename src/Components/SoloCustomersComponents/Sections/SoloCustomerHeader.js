import React from "react";
import SectionHeaders from "../../Utils/SectionHeaders";
import CopyButtonComponent from "../../../BasicUIElements/CopyButtonComponent";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import Left<PERSON>rrowSVG from "../../../Assest/SVG/LeftArrowSVG";
import { useLocation } from "react-router-dom";

const SoloCustomerHeader = () => {
     const location = useLocation();
     let path = window.location.host + location.pathname;
     return (
          <div className=" w-full h-fit flex flex-row justify-between items-end flex-shrink-0  flex-wrap gap-[1.5rem] ps-6 pe-6">
               <div className=" w-fit h-fit flex md:flex-row flex-col gap-[1.5rem] ">
                    <div className=" w-fit flex flex-row flex-wrap gap-[0.5rem]">
                         <SectionHeaders>Customer Since</SectionHeaders>
                         <h3 className=" w-fit font-inter text-[16px] font-[400] text-[#45464E]">
                              12 Sept 2022
                         </h3>
                    </div>
                    <div className=" w-fit flex flex-row flex-wrap gap-[0.5rem]">
                         <SectionHeaders> Profile Link: </SectionHeaders>
                         <h3 className=" w-fit font-inter text-[16px] font-[400] text-[#45464E]">
                              {path}
                         </h3>
                         <CopyButtonComponent text={path} />
                    </div>
               </div>
               <div className=" w-fit h-fit flex flex-row gap-[1.5rem]">
                    <ButtonComponent className=" w-[167px] h-[36px] rounded-[12px] bg-mainLinearGradient  font-[400] font-inter text-[14px] text-[#fff] flex flex-row  justify-evenly items-center">
                         <>
                              Edit Customer <div> |</div>{" "}
                              <div className=" rotate-[270deg]">
                                   <LeftArrowSVG color={"white"} />
                              </div>
                         </>
                    </ButtonComponent>
                    <ButtonComponent className=" w-[167px] h-[36px] rounded-[12px] bg-[#CC5F5F]  font-[400] font-inter text-[14px] text-[#fff]">
                         Suspend Customer
                    </ButtonComponent>
               </div>
          </div>
     );
};

export default SoloCustomerHeader;
