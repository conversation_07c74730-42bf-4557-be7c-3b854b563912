import React from "react";
import CheckBoxComponent from "../../../BasicUIElements/CheckBoxComponent";
import TableIndivHeader from "../../Utils/TableStructures/TableIndivHeader";
import { soloCustomerTableConfig } from "../../../Config/SoloCustomerTableConfig";

const SoloCustomerTableHeader = () => {
     return (
          <div className=" w-fit h-[52px] border-b-2 flex flex-row  ">
               {/* <div className=" w-[100px] h-full "></div> */}
               <div className=" w-[60px] h-full flex flex-row justify-center items-center">
                    {/* <CheckBoxComponent value={false} onClick={() => {}} /> */}
               </div>
               {soloCustomerTableConfig.map((content) => {
                    return (
                         <TableIndivHeader className={content?.className}>
                              {content?.title}
                         </TableIndivHeader>
                    );
               })}
          </div>
     );
};

export default SoloCustomerTableHeader;
