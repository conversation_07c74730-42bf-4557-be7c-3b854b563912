import React, { useState } from "react";
import CheckBoxComponent from "../../../BasicUIElements/CheckBoxComponent";
import { soloCustomerTableConfig } from "../../../Config/SoloCustomerTableConfig";
import TableTextContent from "../../Utils/TableStructures/TableTextContent";
import {
     gettingStockAvailablity,
     stockAvailablityTypes,
} from "../../../Config/OrderTableConfig";
import ListToggleButton from "../../../BasicUIElements/ListToggleButton";
import TableLinkContent from "../../Utils/TableStructures/TableLinkContent";
import { routes } from "../../../Config/routesConfig";

// id: item?.order_id,
// orderDate: item?.order_date,
// deliveryDate: item?.delivery_date,
// orderStatus: item?.order_status,
// orderTotal: item?.order_total,
// productDetails: item?.product_details,
// quantity: item?.quantity,
// salesPerson: item?.sales_person,
// stockAvailablity: item?.stock_availability,

const dateFormatFunction = (dt) => {
     let date = new Date(dt);

     let fullyear = date.getFullYear();
     let month =
          date.getMonth() + 1 < 10
               ? "0" + (date.getMonth() + 1)
               : date.getMonth() + 1;

     let dateNO = date.getDate();

     let format = fullyear + "-" + month + "-" + dateNO;

     return format;
};

const SoloCustomerTableIndivRow = ({ content, subArray = false }) => {
     const billId = content?.billId;
     const orderId = content?.orderId;
     const orderDate = dateFormatFunction(content?.orderDate);
     const productDetails = content?.productDetails;
     const quantity = content.quantity;
     const status = content?.status;
     let stockAvailablity = gettingStockAvailablity(content?.stockAvailablity);
     const orderAmount = content?.orderTotal;
     const salesPerson = content?.salesPerson;
     const deliveryDate = dateFormatFunction(content?.deliveryDate);

     // status
     // Sub array state management
     const [isSubArrayDisplaying, setIsSubArrayDisplaying] = useState(false);

     return (
          <>
               <div className=" w-fit h-[52px] border-b-2 flex flex-row hover:bg-[#F0FEFF]  ">
                    {/* <div className=" w-[100px] h-full"></div> */}
                    <div className=" w-[60px] h-full flex flex-row justify-center items-center">
                         {!subArray && (
                              <ListToggleButton
                                   value={isSubArrayDisplaying}
                                   onClick={() => {
                                        setIsSubArrayDisplaying((state) => {
                                             return !state;
                                        });
                                   }}
                              />
                         )}
                    </div>
                    <TableLinkContent
                         to={
                              routes?.solocustomerTracking?.relativeLink +
                              "/" +
                              billId
                         }
                         size={soloCustomerTableConfig[0].className}
                         className="  font-inter text-[14px]  font-[700]  text-[#019BA2] hover:underline-offset-4"
                    >
                         {billId}
                    </TableLinkContent>
                    <TableTextContent
                         size={soloCustomerTableConfig[1].className}
                         className=" font-[400] font-inter text-[14px] text-[#6E7079]"
                    >
                         {orderId}
                    </TableTextContent>

                    <TableTextContent
                         size={soloCustomerTableConfig[2].className}
                         className=" font-[400] font-inter text-[14px] text-[#6E7079]"
                    >
                         {orderDate}
                    </TableTextContent>
                    <TableTextContent
                         size={soloCustomerTableConfig[3].className}
                         className=" font-[400] font-inter text-[12px] text-[#6E7079] w-full h-full overflow-y-auto pt-1 pb-1 "
                    >
                         {productDetails}
                    </TableTextContent>
                    <TableTextContent
                         size={soloCustomerTableConfig[4].className}
                         className=" w-full text-center font-[400] font-inter  text-[14px] text-[#6E7079]"
                    >
                         <div className=" flex flex-row justify-start ps-[1rem] text-center">
                              {quantity}
                         </div>
                    </TableTextContent>
                    <TableTextContent
                         size={soloCustomerTableConfig[5].className}
                         className={` font-[400] font-inter text-[14px]f`}
                    >
                         <p style={{ color: stockAvailablity.color }}>
                              {stockAvailablity.text}
                         </p>
                    </TableTextContent>
                    <TableTextContent
                         size={soloCustomerTableConfig[6].className}
                         className=" w-full font-[400] font-inter text-[14px] text-[#6E7079]"
                    >
                         <div className=" flex flex-row justify-center text-center">
                              {orderAmount}
                         </div>
                    </TableTextContent>
                    <TableTextContent
                         size={soloCustomerTableConfig[7].className}
                         className=" font-[400] font-inter text-[12px] text-[#6E7079] w-full flex flex-row justify-center "
                    >
                         <div className="ps-2 pe-2 pt-1 pb-1 w-fit  h-fit bg-[#32936F29] rounded-[6px]  text-[#519C66] ">
                              {status}
                         </div>
                    </TableTextContent>
                    <TableTextContent
                         size={soloCustomerTableConfig[8].className}
                         className=" font-[400] font-inter text-[14px] text-[#6E7079]"
                    >
                         {salesPerson}
                    </TableTextContent>
                    <TableTextContent
                         size={soloCustomerTableConfig[9].className}
                         className=" font-[400] font-inter text-[14px] text-[#6E7079]"
                    >
                         {deliveryDate}
                    </TableTextContent>
               </div>

               {!subArray && isSubArrayDisplaying ? (
                    <>
                         {content?.subArray?.map((item) => {
                              return (
                                   <SoloCustomerTableIndivRow
                                        content={item}
                                        subArray={true}
                                   />
                              );
                         })}
                    </>
               ) : (
                    <></>
               )}
          </>
     );
};

export default SoloCustomerTableIndivRow;
