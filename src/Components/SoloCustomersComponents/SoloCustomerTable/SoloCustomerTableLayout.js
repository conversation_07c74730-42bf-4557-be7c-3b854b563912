import React from "react";
import SoloCustomerTableTopper from "./SoloCustomerTableTopper";
import SoloCustomerTableHeader from "./SoloCustomerTableHeader";
import SoloCustomerTableListing from "./SoloCustomerTableListing";

const SoloCustomerTableLayout = () => {
     return (
          <>
               <div className=" hidden flex-col flex-shrink-0 md:flex mt-[2rem]">
                    <SoloCustomerTableTopper />
                    <div className=" w-full h-fit flex flex-col ">
                         <SoloCustomerTableHeader />
                         <SoloCustomerTableListing />
                    </div>
               </div>
               <div className=" flex flex-col flex-shrink-0 md:hidden mt-[2rem]">
                    <SoloCustomerTableTopper />
                    <div className=" w-full h-fit flex flex-col ">
                         <SoloCustomerTableHeader />
                         <SoloCustomerTableListing />
                    </div>
               </div>
          </>
     );
};

export default SoloCustomerTableLayout;
