import React from "react";
import SoloCustomerTableIndivRow from "./SoloCustomerTableIndivRow";
import { useSelector } from "react-redux";

const SoloCustomerTableListing = () => {
     const { orderList } = useSelector((state) => {
          return state.singlecustomer;
     });
     return (
          <div className=" w-fit h-fit">
               {orderList.map((item) => {
                    return <SoloCustomerTableIndivRow content={item} />;
               })}
          </div>
     );
};

export default SoloCustomerTableListing;
