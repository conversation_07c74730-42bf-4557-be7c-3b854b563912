import React, { useEffect } from "react";
import SoloCustomerHeader from "./Sections/SoloCustomerHeader";
import SoloCustomerProfile from "./Sections/SoloCustomerProfile";
import SoloCustomerAddress from "./Sections/SoloCustomerAddress";
import SoloCustomerAmount from "./Sections/SoloCustomerAmount";
import SoloCustomerStatOne from "./Sections/SoloCustomerStatOne";
import SoloCustomerStatTwo from "./Sections/SoloCustomerStatTwo";
import SoloCustomerTableLayout from "./SoloCustomerTable/SoloCustomerTableLayout";
import { useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
  singleCustomerDataFetch,
  singleCustomerSliceActions,
} from "../../Store/CustomerSlices/singleCustomerSlice";

const SoloCustomersLayout = () => {
  const dispatch = useDispatch();
  let { customerid } = useParams();
  const { selectedCustomer, headerData } = useSelector((state) => {
    return state.singlecustomer;
  });

  useEffect(() => {
    if (selectedCustomer?.id !== customerid) {
      dispatch(
        singleCustomerSliceActions.updateSelectedCustomer({
          id: customerid,
        })
      );
      dispatch(singleCustomerDataFetch(customerid));
    }
  }, [selectedCustomer]);

  return (
    <div className=" w-full h-full overflow-y-auto">
      <SoloCustomerHeader />
      <div className=" w-full mt-[2rem] ps-[1rem] overflow-x-auto flex flex-row flex-shrink-0 pb-[1rem] gap-[1rem] pe-4">
        {/* <SoloCustomerProfile auth={auth} />
        <SoloCustomerAddress /> */}
        <SoloCustomerAmount content={headerData?.ordersTotal} />
      </div>
      <div className=" w-full h-fit overflow-x-auto mt-[2rem] ps-[1rem] flex flex-row flex-shrink-0 pb-[1rem] gap-[1rem] pe-4">
        <SoloCustomerStatOne content={headerData} />
        <SoloCustomerStatTwo content={headerData} />
      </div>

      <div className=" overflow-auto">
        <SoloCustomerTableLayout />
      </div>
    </div>
  );
};

export default SoloCustomersLayout;
