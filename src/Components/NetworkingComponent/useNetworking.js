import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { parser_table_filter_data } from "../../UI_Enginee/TableHeaderEnginee/TableHeaderEnginee";
import { networkingTableConfig } from "../../Config/NetworkingConfig";
import { API_ENDPOINTS, api_headers, BaseURL } from "../../API/APIConfig";
import { GetAPI } from "../../API/GetAPI";
import { NetworkingSliceActions } from "../../Store/NetworkingSlice/NetworkingSlice";

export let dateObj = [
  {
    id: "Last year",
    content: "Last year",
  },
  {
    id: "Last Month",
    content: "Last Month",
  },
  {
    id: "Last Week",
    content: "Last Week",
  },
  {
    id: "Custom Date",
    content: "Custom Date",
  },
  {
    id: "Till now",
    content: "Till now",
  },
];

const useNetworking = () => {
  const dispatch = useDispatch();
  const master = useSelector((state) => {
    return state.master;
  });

  let auth = master?.auth;
  const { filter } = useSelector((state) => {
    return state.networking;
  });

  // fetch_list
  const fetch_list = (updated_filter_state = null, updated_date = null) => {
    if (!auth?.id) {
      return;
    }

    let filterObj = parser_table_filter_data(
      networkingTableConfig,
      filter.state
    );
    let filterState = filterObj?.state;
    let isFilter = filterObj?.isFilter;

    let customDate = "Custom Date";
    let startDate = filter?.date?.startDate;
    let endDate = filter?.date?.endDate;
    let filter_options = filter?.date?.filter?.content;

    if (updated_filter_state) {
      let filterState_parser = parser_table_filter_data(
        networkingTableConfig,
        updated_filter_state
      );
      filterState = filterState_parser?.state;
      isFilter = filterState_parser?.isFilter;
    }

    if (updated_date) {
      startDate = updated_date?.startDate;
      endDate = updated_date?.endDate;
      filter_options = updated_date?.filter?.content;
    }

    // console.log("----------------------------");
    // console.log(filterState, isFilter);
    // console.log(startDate, "---", endDate, "---", filter_options);

    if (isFilter) {
      console.log("Fetch list : ");
    } else {
      console.log("Fetch list with filter : ");
      dispatch(fetch_networking_list_thunk());
    }
  };

  return { fetch_list };
};

export default useNetworking;

const fetch_networking_list_thunk = () => {
  return async (dispatch) => {
    let url = new URL(BaseURL.mainURl + API_ENDPOINTS?.networkingList);

    let headers = {
      ...api_headers,
    };

    let response = await GetAPI(url, headers);

    if (response?.ok) {
      let responseData = await response?.json();
      //   console.log(responseData);
      dispatch(
        NetworkingSliceActions?.updateList(
          responseData?.list_network?.reverse()
        )
      );
    } else {
      return null;
    }
  };
};
