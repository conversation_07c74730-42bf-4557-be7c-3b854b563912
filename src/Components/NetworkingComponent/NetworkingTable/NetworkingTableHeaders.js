import React from "react";
import { networkingTableConfig } from "../../../Config/NetworkingConfig";
import TableIndivHeader from "../../Utils/TableStructures/TableIndivHeader";

const NetworkingTableHeaders = () => {
  return (
    <div className=" w-fit h-[52px] border-b-2 flex flex-row  ">
      <div className=" w-[50px] h-full]"></div>
      {networkingTableConfig?.map((item) => {
        return (
          <TableIndivHeader className={item?.className}>
            {item?.title}
          </TableIndivHeader>
        );
      })}
    </div>
  );
};

export default NetworkingTableHeaders;
