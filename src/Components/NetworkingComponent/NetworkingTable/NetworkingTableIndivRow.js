import React from "react";
import { networkingTableConfig } from "../../../Config/NetworkingConfig";
import TableTextContent from "../../Utils/TableStructures/TableTextContent";
import UseRouterAuthentication from "../../../Hooks/UseRouterAuthentication";

const NetworkingTableIndivRow = ({ content }) => {
  const { routeAuthentication } = UseRouterAuthentication();

  let name = content?.Name;
  let email = content?.Email;
  let phoneNumber = content?.Phone_number;
  let location = content?.Location;
  let dateOfBirth = content?.Date_Of_Birth;
  let relationshipStatus = content?.Relationship_Status;
  let anvisoryDate = content?.Anvisory_Date;
  let Occupation = content?.Occupation;
  let employeeId = content?.Employee_id;
  let qualification = content?.Qualification;

  return (
    <div
      key={content?._id}
      className=" w-fit h-[52px] border-b-2 flex flex-row hover:bg-[#AB19171A] hover:bg-opacity-10 "
    >
      <div className=" w-[50px] h-full"></div>
      <TableTextContent
        size={networkingTableConfig[0].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {name}
      </TableTextContent>
      <TableTextContent
        size={networkingTableConfig[1].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {email}
      </TableTextContent>
      <TableTextContent
        size={networkingTableConfig[2].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {phoneNumber}
      </TableTextContent>
      <TableTextContent
        size={networkingTableConfig[3].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {location}
      </TableTextContent>
      <TableTextContent
        size={networkingTableConfig[4].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {dateOfBirth}
      </TableTextContent>
      <TableTextContent
        size={networkingTableConfig[5].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {relationshipStatus}
      </TableTextContent>
      <TableTextContent
        size={networkingTableConfig[6].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {anvisoryDate}
      </TableTextContent>
      <TableTextContent
        size={networkingTableConfig[7].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {Occupation}
      </TableTextContent>
      {/* <TableTextContent
        size={networkingTableConfig[8].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {employeeId}
      </TableTextContent>
      <TableTextContent
        size={networkingTableConfig[9].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {qualification}
      </TableTextContent> */}
    </div>
  );
};

export default NetworkingTableIndivRow;
