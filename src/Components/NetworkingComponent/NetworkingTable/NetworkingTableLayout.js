import React, { useState } from "react";
import NetworkingTableTopper from "./NetworkingTableTopper";
import NetworkingTableHeaders from "./NetworkingTableHeaders";
import NetworkingTableListing from "./NetworkingTableListing";
import { useSelector } from "react-redux";

function isSubarrayPresent(str2, str1) {
  // Check if the subArrayString is a substring of fullString
  return str2
    ?.toString()
    ?.toLowerCase()
    ?.includes(str1?.toString()?.toLowerCase());
}

const NetworkingTableLayout = () => {
  let { list } = useSelector((item) => {
    return item?.networking;
  });
  const [searchState, setSearchState] = useState("");

  const update = (e) => {
    setSearchState(e);
  };

  list = list?.filter((item) => {
    return isSubarrayPresent(item?.Name, searchState);
  });
  return (
    <>
      <NetworkingTableTopper state={searchState} setState={update} />
      <div className=" w-fit h-fit min-h-[750px] flex flex-col overflow-x-auto">
        <div className=" w-fit h-fit flex flex-col ">
          <NetworkingTableHeaders />
          <NetworkingTableListing list={list} />
        </div>
      </div>

      {/* <div className=" flex-col flex-shrink-0 flex">
        <NetworkingTableTopper state={searchState} setState={update} />
        <div className=" w-full h-fit flex flex-col overflow-x-auto">
          <div className=" w-full h-fit flex flex-col ">
            <NetworkingTableHeaders />
            <NetworkingTableListing list={list} />
          </div>
        </div>
      </div> */}
    </>
  );
};

export default NetworkingTableLayout;
