import React, { useEffect } from "react";
import ButtonComponent from "../../BasicUIElements/ButtonComponent";
import NetworkingTableLayout from "./NetworkingTable/NetworkingTableLayout";
import { useNavigate } from "react-router-dom";

import {
  networking_list_thunk,
  networking_Load_addForm_datat,
  NetworkingSliceActions,
} from "../../Store/NetworkingSlice/NetworkingSlice";
import { useDispatch, useSelector } from "react-redux";

import UseRouterAuthentication from "../../Hooks/UseRouterAuthentication";
import useNetworking, { dateObj } from "./useNetworking";
import DateFilterTemplateComponent from "../../UI_templates/DateFilterTemplate/DateFilterTemplateComponent";

const NetworkingLayout = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { fetch_list } = useNetworking();
  const { filter } = useSelector((state) => {
    return state.networking;
  });

  const { isDashboardAccessable, routeAuthentication } =
    UseRouterAuthentication();
  const { auth, access } = useSelector((state) => {
    return state.master;
  });

  useEffect(() => {
    isDashboardAccessable("networking");
    fetch_list(null, null);
    // dispatch(networking_list_thunk());
  }, [auth, access]);

  return (
    <div className=" w-full h-full overflow-y-auto">
      <div className=" w-full h-[50px] flex flex-row justify-between ps-8 pe-8 items-end">
        <div></div>

        {routeAuthentication("networking", "write") && (
          <ButtonComponent
            onClick={() => {
              dispatch(networking_Load_addForm_datat(navigate));
            }}
            className=" w-[215px] h-[36px] bg-mainLinearGradient rounded-[12px] font-inter font-[700] text-[14px] text-[#fff] "
          >
            + Add a Network
          </ButtonComponent>
        )}
      </div>

      <div className=" w-full h-[2rem]"></div>

      <DateFilterTemplateComponent
        fetch={fetch_list}
        updateSlice={NetworkingSliceActions.updateFilterDate}
        dateObj={dateObj}
        customDate="Custom Date"
        date={filter.date}
      />

      <NetworkingTableLayout />
    </div>
  );
};

export default NetworkingLayout;
