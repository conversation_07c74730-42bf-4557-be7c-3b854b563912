import React, { useEffect, useState } from "react";
import Model<PERSON>eaderAndRoute from "../../Utils/UtilsComponents/ModelHeaderAndRoute";

import { useNavigate } from "react-router-dom";
import { routes } from "../../../Config/routesConfig";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";

import { API_ENDPOINTS, api_headers, BaseURL } from "../../../API/APIConfig";
import { PostAPI } from "../../../API/PostAPI";
import { useDispatch, useSelector } from "react-redux";
import {
  networking_Load_addForm_datat,
  NetworkingSliceActions,
} from "../../../Store/NetworkingSlice/NetworkingSlice";

import FormEnginee from "../../../UI_Enginee/Form/FormEnginee";
import { formData_parser } from "../../../Dashboard_API_calling_and_formatting/Form_Formate_and_API";
import Spinner from "../../../BasicUIElements/Spinner";
import UseRouterAuthentication from "../../../Hooks/UseRouterAuthentication";
import useNetworking from "../useNetworking";
import { vaildator_Form_function } from "../../../UI_Enginee/Form/FormEngineeConfig";

const NetworkingAddLayout = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { fetch_list } = useNetworking();

  const [isLoading, setIsLoading] = useState();
  // Enginee Code
  const { add } = useSelector((state) => {
    return state.networking;
  });

  useEffect(() => {
    if (add?.structure?.length === 0) {
      dispatch(networking_Load_addForm_datat());
    }
  }, []);

  const updateFunctionOfForm = (key, value) => {
    let st = {
      ...add.state,
      [key]: { ...add.state[key], value: value },
    };

    dispatch(NetworkingSliceActions?.updateAddState(st));
  };

  const submitHandler = async () => {
    let data = vaildator_Form_function(add.structure, add.state);

    dispatch(NetworkingSliceActions?.updateAddState(data?.validate_Obj));
    if (!data?.isFormValid) {
      return;
    }

    let parsedObj = formData_parser(add.structure, add.state).parser_obj;

    setIsLoading(true);

    let response = await networkingAddThunk(parsedObj);
    setIsLoading(false);

    if (response?.ok) {
      navigate(routes?.networking?.directLink);

      fetch_list();
    }
  };

  //---------------------------------------
  const { routeAuthentication } = UseRouterAuthentication();
  const { auth, access } = useSelector((state) => {
    return state.master;
  });

  useEffect(() => {
    if (!routeAuthentication("networking", "write")) {
      navigate(routes?.networking?.directLink);
    }
  }, [auth, access]);

  return (
    <>
      {add.structure?.length === 0 ? (
        <>
          <Spinner />
        </>
      ) : (
        <div className=" w-full h-full flex flex-row justify-center items-center">
          <div className=" md:w-[424px] w-[80%] max-h-[95vh] h-fit overflow-y-auto bg-[#fff] rounded-[6px] p-4 pb-[2rem] ">
            <ModelHeaderAndRoute
              className="w-full h-fit flex flex-row justify-between ps-4 pe-4 "
              title="Add a Network"
              onClick={() => {
                navigate(routes?.networking.directLink);
              }}
            />

            <FormEnginee
              form={add?.structure}
              formState={add?.state}
              formStateFunction={updateFunctionOfForm}
            />

            <div className=" w-full h-fit mt-[1rem]">
              <div className=" w-full h-fit flex flex-row justify-center gap-[1rem]">
                <ButtonComponent
                  onClick={() => {
                    navigate(routes?.networking.directLink);
                  }}
                  className=" w-[45%] h-[58px] rounded-[6px] border-[#00BD94] border-2 bg-clip-text text-transparent bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] flex flex-row justify-center items-center"
                >
                  Cancel
                </ButtonComponent>
                <ButtonComponent
                  isLoading={isLoading}
                  onClick={submitHandler}
                  className=" w-[45%] h-[58px] rounded-[6px] bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] text-[#fff] flex flex-row justify-center items-center"
                >
                  Add a Network
                </ButtonComponent>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default NetworkingAddLayout;

export const networkingAddThunk = async (body) => {
  let url = new URL(BaseURL?.mainURl + API_ENDPOINTS?.networkingadd);

  body = body;

  const header = {
    ...api_headers,
  };

  let response = await PostAPI(url, body, header);

  return response;
};
