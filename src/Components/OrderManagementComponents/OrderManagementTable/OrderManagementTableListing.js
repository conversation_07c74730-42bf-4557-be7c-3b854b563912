import React from "react";
import IndivOrderManagementTableRow from "./IndivOrderManagementTableRow";
import { useSelector } from "react-redux";

const OrderManagementTableListing = () => {
     const orderListing = useSelector((state) => {
          return state.orderManagement.orderListing;
     });

     return (
          <div className=" w-fit min-h-[200px] pb-[4rem] ">
               {orderListing.map((content) => {
                    return <IndivOrderManagementTableRow content={content} />;
               })}
          </div>
     );
};

export default OrderManagementTableListing;
