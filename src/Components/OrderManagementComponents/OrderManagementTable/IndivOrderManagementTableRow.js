import React, { useState } from "react";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import TableTextContent from "../../Utils/TableStructures/TableTextContent";
import CheckBoxComponent from "../../../BasicUIElements/CheckBoxComponent";
import {
  gettingStockAvailablity,
  orderTableConfig,
  stockAvailablityTypes,
} from "../../../Config/OrderTableConfig";
import edit from "../../../Assest/Utils/edit.png";
import deletething from "../../../Assest/Utils/delete.png";
import TableLinkContent from "../../Utils/TableStructures/TableLinkContent";

import { routes } from "../../../Config/routesConfig";
import { useNavigate } from "react-router-dom";
import ListToggleButton from "../../../BasicUIElements/ListToggleButton";
import UseRouterAuthentication from "../../../Hooks/UseRouterAuthentication";

const dateFormatFunction = (dt) => {
  let date = new Date(dt);

  let fullyear = date.getFullYear();
  let month =
    date.getMonth() + 1 < 10
      ? "0" + (date.getMonth() + 1)
      : date.getMonth() + 1;

  let dateNO = date.getDate();

  let format = fullyear + "-" + month + "-" + dateNO;

  return format;
};

const IndivOrderManagementTableRow = ({
  content = {},
  isSubArray = false,
  isHover = false,
}) => {
  const navigate = useNavigate();
  const { routeAuthentication } = UseRouterAuthentication();

  //------------------------------------------------------------

  //---------------------------------------------------------
  let orderId = content?.orderId;
  let name = content?.name;
  let customerId = content?.customerId;
  let orderDate = dateFormatFunction(content?.orderDate);
  let productDetails = content?.productDetails;
  let batchno = content?.batchNo;
  let quantityText = content?.quantity;
  let stockAvailablity = content?.stockAvailablity;

  let UnavailableQuantity = content?.unavailableQuantity;
  let damagedProducts = content?.damagedProducts ? "Damaged" : "Not Damaged";
  let ETAOfUnavailablity = content?.ETAOfUnavailablity;
  let orderTotal = "₹" + content?.orderTotal;

  let location = content?.location;
  let natureOfOrders = {
    text: content?.orderStatus,
    className:
      " w-[130px] h-[28px] bg-[#CFD3D433] rounded-[8px] font-inter font-[400] text-[12px] text-[#6E7079] flex flex-rwo justify-center items-center",
  };
  let salesPerson = content?.salesPerson;
  let deliveryDate = dateFormatFunction(content?.deliveryDate);
  let paymentReceived = content?.paymentReceived;
  let brand = content?.brand;

  const editClickHandler = () => {};
  const deleteClickHandler = () => {};

  const ordersList = content?.subArray;

  //-------------------------------------------------------------
  // Sub array state management
  const [isSubArrayDisplaying, setIsSubArrayDisplaying] = useState(false);
  const [isHoverState, setIsHoverState] = useState(false);

  let hoverColor = isSubArray ? "bg-[#F0FEFF]" : "bg-[#E0F2F5]";

  return (
    <>
      <div
        className={` w-fit h-[52px] border-b-2 flex flex-row ${
          isHover || isHoverState ? hoverColor : "bg-[#fff]"
        } `}
        key={orderDate}
        onMouseEnter={() => {
          setIsHoverState(true);
        }}
        onMouseLeave={() => {
          setIsHoverState(false);
        }}
      >
        <div className=" w-[60px] h-full flex flex-row justify-center items-center">
          {!isSubArray && (
            <ListToggleButton
              value={isSubArrayDisplaying}
              onClick={() => {
                setIsSubArrayDisplaying((state) => {
                  return !state;
                });
              }}
            />
          )}
        </div>
        <TableTextContent
          size={orderTableConfig[0].className}
          className=" font-[400] font-inter text-[14px] text-[#6E7079]"
        >
          {orderId}
        </TableTextContent>
        <TableTextContent
          size={orderTableConfig[1].className}
          className=" font-[400] font-inter text-[14px] text-[#6E7079]"
        >
          {name}
        </TableTextContent>
        <TableLinkContent
          size={orderTableConfig[2].className}
          className=" font-[700] font-inter text-[14px] text-[#019BA2] hover:underline-offset-4"
          to={routes.customer.directLink + "/" + customerId}
        >
          {customerId}
        </TableLinkContent>

        <TableTextContent
          size={orderTableConfig[3].className}
          className=" font-[700] font-inter text-[14px] text-[#6E7079] "
        >
          {orderDate}
        </TableTextContent>
        <TableTextContent
          size={orderTableConfig[4].className}
          className=" font-[400] font-inter text-[12px] text-[#6E7079] w-full h-full overflow-y-auto pt-1 pb-1 "
        >
          {productDetails}
        </TableTextContent>
        <TableTextContent
          size={orderTableConfig[5].className}
          className=" font-[400] font-inter text-[14px] text-[#6E7079]"
        >
          {batchno}
        </TableTextContent>
        <TableTextContent
          size={orderTableConfig[6].className}
          className=" font-[400] font-inter text-[14px] text-[#6E7079]"
        >
          {quantityText}
        </TableTextContent>
        <TableTextContent
          size={orderTableConfig[7].className}
          className={` font-[400] font-inter text-[14px]f`}
        >
          {stockAvailablity}
        </TableTextContent>
        <TableTextContent
          size={orderTableConfig[8].className}
          className=" font-[400] font-inter text-[14px] text-[#6E7079] w-full flex flex-row justify-center"
        >
          {UnavailableQuantity}
        </TableTextContent>
        {/* <TableTextContent
                         size={orderTableConfig[9].className}
                         className=" font-[400] font-inter text-[14px] text-[#6E7079] w-full flex flex-row justify-center"
                    >
                         {damagedProducts}
                    </TableTextContent> */}

        <TableTextContent
          size={orderTableConfig[9].className}
          className=" font-[400] font-inter text-[14px] text-[#6E7079]"
        >
          {ETAOfUnavailablity}
        </TableTextContent>

        <TableTextContent
          size={orderTableConfig[10].className}
          className=" font-[400] font-inter text-[14px] text-[#6E7079]"
        >
          {orderTotal}
        </TableTextContent>

        <TableTextContent
          size={orderTableConfig[11].className}
          className=" font-[400] font-inter text-[14px] text-[#6E7079]"
        >
          {paymentReceived}
        </TableTextContent>
        <TableTextContent
          size={orderTableConfig[12].className}
          className=" font-[400] font-inter text-[14px] text-[#6E7079]"
        >
          {location}
        </TableTextContent>
        <TableTextContent
          size={orderTableConfig[13].className}
          className=" font-[600] font-inter text-[14px] text-[#000]"
        >
          <p className={natureOfOrders.className}>{natureOfOrders.text}</p>
        </TableTextContent>
        <TableTextContent
          size={orderTableConfig[14].className}
          className=" font-[400] font-inter text-[14px] text-[#6E7079]"
        >
          {salesPerson}
        </TableTextContent>
        <TableTextContent
          size={orderTableConfig[15].className}
          className=" font-[400] font-inter text-[14px] text-[#6E7079]"
        >
          {deliveryDate}
        </TableTextContent>
        <TableTextContent
          size={orderTableConfig[16].className}
          className=" font-[400] font-inter text-[14px] text-[#6E7079]"
        >
          {brand}
        </TableTextContent>
        {/* <div className=" w-[60px] h-full flex flex-row justify-center items-center">
          {routeAuthentication("orders", "edit") && (
            <ButtonComponent
              onClick={editClickHandler}
              className=" w-[24px] h-[24px] flex flex-rwo justify-center items-center "
            >
              <img
                src={edit}
                className=" w-full h-full object-contain"
                alt="edit"
              />
            </ButtonComponent>
          )}
        </div> */}
        {/* <div className=" w-[60px] h-full flex flex-row justify-center items-center">
          {routeAuthentication("orders", "delete") && (
            <ButtonComponent
              onClick={deleteClickHandler}  
              className=" w-[24px] h-[24px] flex flex-rwo justify-center items-center "
            >
              <img
                src={deletething}
                className=" w-full h-full object-contain"
                alt="edit"
              />
            </ButtonComponent>
          )}
        </div> */}
      </div>

      {isSubArrayDisplaying && ordersList ? (
        ordersList.map((item) => {
          return (
            <IndivOrderManagementTableRow
              content={item}
              isSubArray={true}
              isHover={isHoverState}
            />
          );
        })
      ) : (
        <></>
      )}
      {/* <IndivOrderManagementTableRow content={content} /> */}
    </>
  );
};

export default IndivOrderManagementTableRow;
