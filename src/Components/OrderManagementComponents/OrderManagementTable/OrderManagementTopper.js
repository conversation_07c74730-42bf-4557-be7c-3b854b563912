import React from "react";

import SwitchBoxComponent from "../../../BasicUIElements/SwitchBoxComponent";
import { useDispatch, useSelector } from "react-redux";
import {
  orderListFilterFetchThunk,
  OrderManagementSliceActions,
} from "../../../Store/OrderManagementSlice/OrderManagementSlice";
import { orderTableConfig } from "../../../Config/OrderTableConfig";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import { FaDownload } from "react-icons/fa6";
import UseJsObj2CSV from "../../../Hooks/UseJsObj2CSV";
import { parser_table_filter_data } from "../../../UI_Enginee/TableHeaderEnginee/TableHeaderEnginee";

const isTrue = (list, text) => {
  if (!list) {
    return false;
  } else if (list?.length === 0 || list?.length > 1) {
    return false;
  } else if (list[0].id === text) {
    return true;
  }

  return false;
};

const OrderManagementTopper = () => {
  const dispatch = useDispatch();
  const { filter } = useSelector((state) => {
    return state.orderManagement;
  });

  const change = (obj) => {
    let updatedState = {
      ...filter?.state,
      ...obj,
    };

    dispatch(OrderManagementSliceActions.updateFilterState(updatedState));

    let filterObj = parser_table_filter_data(orderTableConfig, updatedState);

    if (filterObj?.isFilter) {
      dispatch(orderListFilterFetchThunk(filterObj?.state));
    } else {
      dispatch(orderListFilterFetchThunk({}));
    }
  };

  //------------------------------------
  const { downloadOrderDashboard } = UseJsObj2CSV();

  return (
    <div className="  w-full  md:h-[45px] h-fit md:gap-[2rem] gap-[0.5rem]  flex md:flex-row flex-col justify-between md:items-center items-start md:ps-8 md:pe-8 ps-4 pe-4 border-b-2">
      <h1 className=" text-[#45464E] font-[500] font-inter text-[16px] flex flex-row flex-shrink-0  ">
        Orders
      </h1>

      <div className=" w-fit h-fit flex flex-row items-center flex-shrink-0 flex-wrap  md:gap-[0.8rem] gap-[0.25rem] gap-y-[0.5rem] mb-[0.5rem] md:mb-0 mt-[0.5rem] md:mt-0 ">
        <div className=" w-fit h-fit flex flex-row flex-shrink-0 items-center gap-[6px] ">
          <h2 className=" text-[14px] font-inter font-[400] text-[#2B2F32]">
            Show all Available
          </h2>
          <SwitchBoxComponent
            value={isTrue(filter?.state?.stock_availability, "Available")}
            onClick={() => {
              change({
                stock_availability: [{ id: "Available", content: "Available" }],
              });
            }}
          />
        </div>
        <div className=" w-fit h-fit flex flex-row flex-shrink-0 items-center gap-[6px] ">
          <h2 className=" text-[14px] font-inter font-[400] text-[#2B2F32]">
            Show only out of stock
          </h2>
          <SwitchBoxComponent
            value={isTrue(filter?.state?.stock_availability, "Out Of Stock")}
            onClick={() => {
              change({
                stock_availability: [
                  { id: "Out Of Stock", content: "Out Of Stock" },
                ],
              });
            }}
          />
        </div>

        <div className=" w-fit h-fit flex flex-row items-center  gap-[0.8rem] md:ps-4 ps-2 ">
          <ButtonComponent
            onClick={downloadOrderDashboard}
            className=" w-fit h-[50px] flex flex-row justify-center items-center gap-[0.25rem]"
          >
            <FaDownload />
          </ButtonComponent>
        </div>
      </div>
    </div>
  );
};

export default OrderManagementTopper;
