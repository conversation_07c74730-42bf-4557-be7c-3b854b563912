import React from "react";
import { orderTableConfig } from "../../../Config/OrderTableConfig";
import { useDispatch, useSelector } from "react-redux";
import {
  OrderManagementSliceActions,
  orderListFilterFetchThunk,
} from "../../../Store/OrderManagementSlice/OrderManagementSlice";
import TableHeaderEnginee, {
  parser_table_filter_data,
} from "../../../UI_Enginee/TableHeaderEnginee/TableHeaderEnginee";

const OrderManagementTableHeader = () => {
  const dispatch = useDispatch();
  const { filter } = useSelector((state) => {
    return state.orderManagement;
  });

  const updateState = (key, value) => {
    let updatedState = {
      ...filter?.state,
      [key]: value,
    };
    
    dispatch(OrderManagementSliceActions.updateFilterState(updatedState));

    let filterObj = parser_table_filter_data(orderTableConfig, updatedState);

    if (filterObj?.isFilter) {
      dispatch(orderListFilterFetchThunk(filterObj?.state));
    } else {
      dispatch(orderListFilterFetchThunk({}));
    }
  };
  return (
    <div className=" w-fit h-[52px] border-b-2 flex flex-row  ">
      <div className=" w-[60px] h-full flex flex-row justify-center items-center"></div>

      <TableHeaderEnginee
        table={orderTableConfig}
        state={filter?.state}
        updateState={updateState}
      />
    </div>
  );
};

export default OrderManagementTableHeader;
