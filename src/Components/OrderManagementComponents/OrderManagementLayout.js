import React, { useEffect } from "react";
import SectionHeaders from "../Utils/SectionHeaders";
import OrderManagementDataBox from "./OrderManagementDataBox";
import OrderManagementTableLayout from "./OrderManagementTable/OrderManagementTableLayout";

import UseRouterAuthentication from "../../Hooks/UseRouterAuthentication";

import { useDispatch } from "react-redux";
import {
  order_list_headerCount_thunk,
  orderFilterListFetchThunk,
  orderListingFetchThunk,
} from "../../Store/OrderManagementSlice/OrderManagementSlice";
import { routes } from "../../Config/routesConfig";
import DashboardNavbar_template from "../../UI_templates/Dashboard_Templates/DashboardNavbar_template";

const OrderManagementLayout = () => {
  const dispatch = useDispatch();
  const { isDashboardAccessable } = UseRouterAuthentication();

  useEffect(() => {
    isDashboardAccessable("orders");
    dispatch(orderFilterListFetchThunk());
    dispatch(orderListingFetchThunk());
    dispatch(order_list_headerCount_thunk());
  }, []);

  return (
    <div className=" w-full h-full flex flex-col ">
      <DashboardNavbar_template
        listLink={routes?.orderManagement?.directLink}
        insightsLink={routes?.orderManagementAnalytics?.directLink}
        dashboard="orders"
      />
      <div className=" flex-1 overflow-y-auto">
        <div className=" w-full h-[70px]  flex flex-row justify-between items-center ps-8 pe-8">
          <SectionHeaders> Orders Summary</SectionHeaders>
        </div>

        <div className=" w-full mt-[2rem] ps-3 mb-[2rem]">
          <OrderManagementDataBox />
        </div>
        <div className=" w-full h-[2rem]"></div>

        <OrderManagementTableLayout />
      </div>
    </div>
  );
};

export default OrderManagementLayout;
