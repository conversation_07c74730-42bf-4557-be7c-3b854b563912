import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { getStartAndEndDate } from "../../Config/DateFilterConfig";
import { PostAPI } from "../../API/PostAPI";
import { API_ENDPOINTS, api_headers, BaseURL } from "../../API/APIConfig";
import { OrderManagementSliceActions } from "../../Store/OrderManagementSlice/OrderManagementSlice";
import { GetAPI } from "../../API/GetAPI";

export let dateObj = [
  {
    id: "Last year",
    content: "Last year",
  },
  {
    id: "Last Month",
    content: "Last Month",
  },
  {
    id: "Last Week",
    content: "Last Week",
  },
  {
    id: "Custom Date",
    content: "Custom Date",
  },
  {
    id: "Till now",
    content: "Till now",
  },
];

const useOrderManagement = () => {
  const dispatch = useDispatch();
  const master = useSelector((state) => {
    return state.master;
  });

  let auth = master?.auth;

  const { analytics } = useSelector((state) => {
    return state.orderManagement;
  });

  const fetchAnalytics = (obj = null) => {
    if (!auth?.id) {
      return;
    }

    if (obj) {
      let customDate = "Custom Date";
      let startDate = obj?.startDate;
      let endDate = obj?.endDate;
      let filter = obj?.filter?.content;

      if (filter === "Last year") {
        let [startD, endD] = getStartAndEndDate(365);
        startDate = new Date(startD);
        endDate = new Date(endD);
      } else if (filter === "Last Month") {
        let [startD, endD] = getStartAndEndDate(30);
        startDate = new Date(startD);
        endDate = new Date(endD);
      } else if (filter === "Last Week") {
        let [startD, endD] = getStartAndEndDate(7);
        startDate = new Date(startD);
        endDate = new Date(endD);
      } else if (filter === "Till now") {
        let [startD, endD] = getStartAndEndDate(3650 + 3650);
        startDate = new Date(startD);
        endDate = new Date(endD);
      } else if (filter === customDate) {
      }

      dispatch(fetch_analytics_thunk(startDate, endDate));
    } else {
      let customDate = "Custom Date";
      let startDate = analytics?.startDate;
      let endDate = analytics?.endDate;
      let filter = analytics?.filter?.content;

      if (filter === "Last year") {
        let [startD, endD] = getStartAndEndDate(365);
        startDate = new Date(startD);
        endDate = new Date(endD);
      } else if (filter === "Last Month") {
        let [startD, endD] = getStartAndEndDate(30);
        startDate = new Date(startD);
        endDate = new Date(endD);
      } else if (filter === "Last Week") {
        let [startD, endD] = getStartAndEndDate(7);
        startDate = new Date(startD);
        endDate = new Date(endD);
      } else if (filter === "Till now") {
        let [startD, endD] = getStartAndEndDate(3650 + 3650);
        startDate = new Date(startD);
        endDate = new Date(endD);
      } else if (filter === customDate) {
      }

      dispatch(fetch_analytics_thunk(startDate, endDate));
    }
  };
  return {
    fetchAnalytics,
  };
};

export default useOrderManagement;

//Analytics
function formatDateToCustomString(dateObj) {
  const year = dateObj.getFullYear();
  const month = (dateObj.getMonth() + 1).toString().padStart(2, "0"); // Months are 0-indexed
  const day = dateObj.getDate().toString().padStart(2, "0");

  return `${year}-${month}-${day}`;
}

const fetch_analytics_thunk = (startDate, endDate) => {
  return async (dispatch) => {
    let url = new URL(BaseURL?.mainURl + API_ENDPOINTS?.orderAnalytics);

    // url =
    //   url.href +
    //   "?startDate=" +
    //   formatDateToCustomString(new Date(startDate)) +
    //   "&tillDate=" +
    //   formatDateToCustomString(new Date(endDate));

    let body = {
      startDate: formatDateToCustomString(new Date(startDate)),
      endDate: formatDateToCustomString(new Date(endDate)),
    };

    let headers = { ...api_headers };

    let response = await PostAPI(url, body, headers);

    let url2 = new URL(BaseURL?.mainURl + API_ENDPOINTS?.orderBrandVsValue);
    let response2 = await GetAPI(url2, headers);

    if (!response2.ok) {
      return;
    }

    if (response?.ok) {
      let responseData = await response?.json();
      let responseData2 = await response2?.json();

      let obj = {
        orderOverview: {
          Total_orders: responseData?.orderOverview?.Total_orders,
          delivered: responseData?.orderOverview?.delivered,
          readyForDelivery: responseData?.orderOverview?.readyForDelivery,
          pendingForStock: responseData?.orderOverview?.pendingForStock,
          damaged: responseData?.orderOverview?.damaged,
        },

        totalOrderValueVsPaymentReceived: {
          totalPaymentReceived:
            responseData?.totalOrderValueVsPaymentReceived
              ?.totalPaymentReceived,
          totalOrderValue:
            responseData?.totalOrderValueVsPaymentReceived?.totalOrderValue,
          paymentOutstanding:
            responseData?.totalOrderValueVsPaymentReceived?.paymentOutstanding,
        },

        averageDeliveryTime:
          responseData?.averageDeliveryTime?.averageDeliveryTimeInDays,

        commitment: {
          commitmentsHonored: responseData?.commitment?.commitmentsHonored,
          commitmentsDelayed: responseData?.commitment?.commitmentsDelayed,
        },

        Orderavailableoutofstock: {
          date: responseData?.Orderavailableoutofstock?.map((item) => {
            return item?.date;
          }),
          orderCount: responseData?.Orderavailableoutofstock?.map((item) => {
            return item.orderCount;
          }),
          Available: responseData?.Orderavailableoutofstock?.map(
            (item) => item?.Available
          ),
          out_of_stock: responseData?.Orderavailableoutofstock?.map(
            (item) => item?.out_of_stock
          ),
        },

        orderByLocation: {
          location: responseData?.orderByLocation?.locationStats?.map(
            (item) => item?.location
          ),
          count: responseData?.orderByLocation?.locationStats?.map(
            (item) => item?.count
          ),
        },

        productVsQuantity: {
          id: responseData?.productVsQuantity?.productQuantity?.map(
            (item) => item?._id
          ),
          totalQuantity: responseData?.productVsQuantity?.productQuantity?.map(
            (item) => item?.totalQuantity
          ),
        },

        OrderSalespersonProductSoldTotalOrder: {
          id: responseData?.OrderSalespersonProductSoldTotalOrder?.Salesperson_ProductSold_TotalOrder.map(
            (item) => item?._id
          ),
          totalQuantity:
            responseData?.OrderSalespersonProductSoldTotalOrder?.Salesperson_ProductSold_TotalOrder.map(
              (item) => item?.totalQuantity
            ),
          totalOrder:
            responseData?.OrderSalespersonProductSoldTotalOrder?.Salesperson_ProductSold_TotalOrder.map(
              (item) => item?.totalOrder
            ),
        },

        SalespersonProductSoldTotalPaymentReceived: {
          id: responseData?.SalespersonProductSoldTotalPaymentReceived?.Salesperson_ProductSold_TotalPaymentReceived?.map(
            (item) => item?._id
          ),
          totalQuantity:
            responseData?.SalespersonProductSoldTotalPaymentReceived?.Salesperson_ProductSold_TotalPaymentReceived?.map(
              (item) => item?.totalQuantity
            ),
          Total_Payment_Received:
            responseData?.SalespersonProductSoldTotalPaymentReceived?.Salesperson_ProductSold_TotalPaymentReceived?.map(
              (item) => item?.Total_Payment_Received
            ),
        },

        brandsValue: {
          brand: responseData2?.brandsValue?.map((item) => {
            return item?._id;
          }),
          value: responseData2?.brandsValue?.map((item) => {
            return item?.value;
          }),
        },
      };

      dispatch(OrderManagementSliceActions?.updateAnalyticsContent(obj));
    }
  };
};
