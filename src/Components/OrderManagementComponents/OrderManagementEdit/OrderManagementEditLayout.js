import React, { useState } from "react";
import ModelHeaderAndRoute from "../../Utils/UtilsComponents/ModelHeaderAndRoute";
import InputComponentWithErrorMessage from "../../../BasicUIElements/InputComponentWithErrorMessage";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import SelectComponent from "../../../BasicUIElements/SelectComponent";
import { useNavigate } from "react-router-dom";

const opt = [
     {
          id: "34",
          content: "Plumbing",
     },
     {
          id: "22",
          content: "Servicer",
     },
     {
          id: "33",
          content: "Constructor",
     },
     {
          id: "34",
          content: "Owner",
     },
];

const OrderManagementEditLayout = () => {
     const navigate = useNavigate()
     const [state, setState] = useState({
          id: "34",
          content: "Plumbing",
     });
     return (
          <div className=" w-[424px] max-h-[95vh] h-fit overflow-y-auto bg-[#fff] rounded-[6px] p-4">
               <ModelHeaderAndRoute
                    className="w-full h-fit flex flex-row justify-between ps-4 pe-4 "
                    title="Edit"
                    onClick={() => {
                         navigate("..")
                    }}
               />
               <h1 className=" pt-2 pb-2 font-[500] font-inter text-[#8B8D97] text-[16px] ps-6">
                    Lead Information
               </h1>

               <div className=" w-full h-fit flex flex-col items-center  gap-[1rem]">
                    <div className=" w-fit h-fit flex flex-col ">
                         <InputComponentWithErrorMessage
                              type="text"
                              name="name"
                              placeholder="Enter a Name"
                              errorMessage="Enter a First name "
                              value="Joead"
                              onChange={() => {}}
                              onBlur={() => {}}
                              isValid={false}
                              isTouched={false}
                              className="w-[375px] h-[52px] ps-5  border border-[#D8E1F2] rounded-[6px] text-[16px] text-[#53545C] font-semibold placeholder:font-poppins placeholder:text-[16px] placeholder:font-[400] placeholder:text-[#ABAFB1] "
                         />
                    </div>
                    <div className=" w-fit h-fit flex flex-col ">
                         <InputComponentWithErrorMessage
                              type="email"
                              name="email"
                              placeholder="Enter a Email Address"
                              errorMessage="Enter a Email Address "
                              value="<EMAIL>"
                              onChange={() => {}}
                              onBlur={() => {}}
                              isValid={false}
                              isTouched={false}
                              className="w-[375px] h-[52px] ps-5  border border-[#D8E1F2] rounded-[6px] text-[16px] text-[#53545C] font-semibold placeholder:font-poppins placeholder:text-[16px] placeholder:font-[400] placeholder:text-[#ABAFB1] "
                         />
                    </div>

                    <div className=" w-full h-fit flex flex-row justify-center">
                         <InputComponentWithErrorMessage
                              type="number"
                              name="number"
                              placeholder="Enter a Number"
                              errorMessage="Enter a Number"
                              value=""
                              onChange={() => {}}
                              onBlur={() => {}}
                              isValid={false}
                              isTouched={false}
                              className="w-[190px] h-[36px] ps-5  border border-[#D8E1F2] bg-[#EFF1F999] rounded-[6px] text-[16px] text-[#53545C] font-semibold placeholder:font-poppins placeholder:text-[16px] placeholder:font-[400] placeholder:text-[#ABAFB1] "
                         />
                    </div>

                    <div className=" w-fit h-fit flex flex-col ">
                         <InputComponentWithErrorMessage
                              type="text"
                              name="location"
                              placeholder="location"
                              errorMessage="please enter location"
                              value="Seelanaikenpatti, Salem|"
                              onChange={() => {}}
                              onBlur={() => {}}
                              isValid={false}
                              isTouched={false}
                              className="w-[375px] h-[52px] ps-5  border border-[#D8E1F2] rounded-[6px] text-[16px] text-[#53545C] font-semibold placeholder:font-poppins placeholder:text-[16px] placeholder:font-[400] placeholder:text-[#ABAFB1] "
                         />
                    </div>

                    <div className=" w-fit h-fit flex flex-col ">
                         <InputComponentWithErrorMessage
                              type="text"
                              name="Assigned Sales Representative"
                              placeholder="Assigned Sales Representative"
                              errorMessage="please enter location"
                              value="Rajesh"
                              onChange={() => {}}
                              onBlur={() => {}}
                              isValid={false}
                              isTouched={false}
                              className="w-[375px] h-[52px] ps-5  border border-[#D8E1F2] rounded-[6px] text-[16px] text-[#53545C] font-semibold placeholder:font-poppins placeholder:text-[16px] placeholder:font-[400] placeholder:text-[#ABAFB1] "
                         />
                    </div>

                    <div className=" w-full h-fit ">
                         <SelectComponent
                              value={state}
                              updateFunction={setState}
                              optionarr={opt}
                              className=" w-[375px] h-[48px] rounded-[4px] border-[1px] border-[#D8E1F2] text-[#53545C] text-[16px] font-[500] font-inter ps-5 pe-5"
                              textClassName="text-[#53545C] text-[16px] font-[500] font-inter"
                         />
                    </div>

                    <div className=" w-full h-fit flex flex-row justify-center">
                         <textarea
                              value="John Doe expressed interest in large ceramic floor tiles. Mentioned upcoming home renovation project."
                              className=" w-[375px] h-[100px] rounded-[8px] border-[1px] p-2 text-[#53545C] text-[16px]"
                         />
                    </div>

                    <div className=" w-full h-fit flex flex-row justify-center gap-[1rem]">
                         <ButtonComponent className=" w-[45%] h-[58px] rounded-[6px] border-[#00BD94] border-2 bg-clip-text text-transparent bg-mainLinearGradient text-[20px] font-inter font-[500] flex flex-row justify-center items-center">
                              Cancel
                         </ButtonComponent>
                         <ButtonComponent className=" w-[45%] h-[58px] rounded-[6px] bg-mainLinearGradient text-[20px] font-inter font-[500] text-[#fff] flex flex-row justify-center items-center">
                              Update
                         </ButtonComponent>
                    </div>
               </div>
          </div>
     );
};

export default OrderManagementEditLayout;
