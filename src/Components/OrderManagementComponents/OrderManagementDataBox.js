import React from "react";
import DataStructureBoxHeader from "../Utils/DataStuctureBoxes/DataStructureBoxHeader";

import shoppingcart from "../../Assest/Utils/Shoppingcart.png";
import { useSelector } from "react-redux";
import HeaderDataEnginee from "../../UI_Enginee/HeaderData/HeaderDataEnginee";

const OrderManagementDataBox = () => {
  const { headerData } = useSelector((state) => {
    return state?.orderManagement;
  });

  let eachPerLine = 5;
  let widthPerTitle = 150;

  return (
    <div className=" w-full h-fit flex flex-row flex-shrink-0 overflow-x-auto md:ps-0 md:pe-4 ps-4 pe-4 pb-[1rem]">
      <div
        className={` w-[${
          widthPerTitle * eachPerLine
        }px] h-fit flex flex-col flex-shrink-0 justify-between border-2 p-3 rounded-[10px]`}
      >
        <DataStructureBoxHeader logo={shoppingcart}></DataStructureBoxHeader>

        <HeaderDataEnginee headerData={headerData} eachPerLine={eachPerLine} />
      </div>
    </div>
  );
};

export default OrderManagementDataBox;
