import React, { useState } from "react";
import DataStructureBoxHeader from "../Utils/DataStuctureBoxes/DataStructureBoxHeader";
import SelectComponent from "../../BasicUIElements/SelectComponent";
import IndivIdualDataFieldLayout from "../Utils/DataStuctureBoxes/IndivIdualDataFieldLayout";

import bagImage from "../../Assest/Utils/Bag.png";
import userImage from "../../Assest/Utils/UserLogo.png";

const opt = [
     {
          id: "",
          content: "this week",
     },
     {
          id: "",
          content: "this month",
     },
     {
          id: "",
          content: "this year",
     },
     {
          id: "",
          content: "this month",
     },
];

const CustomerDataBox = () => {
     const [state, setState] = useState({
          id: "",
          content: "this week",
     });
     return (
          <div className=" w-full h-fit flex flex-row justify-evenly mt-[3rem] flex-shrink-0 overflow-x-auto md:ps-2 md:pe-0 ps-4 pe-4 pb-[1rem] gap-[1rem]">
               <div className=" md:w-[60%] w-[450px] h-[145px] flex flex-col flex-shrink-0 justify-between border-2 p-3 rounded-[10px]">
                    <DataStructureBoxHeader logo={userImage}>
                         <SelectComponent
                              value={state}
                              updateFunction={setState}
                              optionarr={opt}
                              className=" w-[120px] h-[35px] text-[#BEC0CA]"
                         />
                    </DataStructureBoxHeader>

                    <div className=" w-full h-[60px] flex flex-row justify-between ">
                         <IndivIdualDataFieldLayout
                              heading="All Orders"
                              stat="1,240"
                         />
                         <IndivIdualDataFieldLayout
                              heading="Delivered"
                              stat="1,181"
                              statColor="green"
                         />

                         <IndivIdualDataFieldLayout
                              heading="Ready for delivery"
                              stat="40"
                              statColor="blue"
                         />
                         <IndivIdualDataFieldLayout
                              heading="Out for Delivery"
                              stat="234"
                              statColor="green"
                         />

                         <div></div>
                    </div>
               </div>
               <div className=" md:w-[38%] w-[300px] h-[145px] flex flex-col flex-shrink-0 justify-between border-2 p-3 rounded-[10px]">
                    <DataStructureBoxHeader logo={bagImage}>
                         <SelectComponent
                              value={state}
                              updateFunction={setState}
                              optionarr={opt}
                              className=" w-[120px] h-[35px] text-[#BEC0CA]"
                         />
                    </DataStructureBoxHeader>

                    <div className=" w-full h-[60px] flex flex-row justify-between ">
                         <IndivIdualDataFieldLayout
                              heading="All Orders"
                              stat="1,240"
                         />
                         <IndivIdualDataFieldLayout
                              heading="Delivered"
                              stat="1,181"
                              statColor="green"
                         />

                         <div></div>
                    </div>
               </div>
          </div>
     );
};

export default CustomerDataBox;
