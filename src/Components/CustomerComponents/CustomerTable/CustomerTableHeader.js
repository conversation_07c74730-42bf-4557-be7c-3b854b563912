import React from "react";
import CheckBoxComponent from "../../../BasicUIElements/CheckBoxComponent";
import TableIndivHeader from "../../Utils/TableStructures/TableIndivHeader";
import { customerTableConfig } from "../../../Config/CustomerTableConfig";

const CustomerTableHeader = () => {
     return (
          <div className=" w-fit h-[52px] border-b-2 flex flex-row  ">
               {/* <div className=" w-[60px] h-full flex flex-row justify-center items-center">
                    <CheckBoxComponent value={false} onClick={() => {}} />
               </div> */}
               {customerTableConfig.map((content) => {
                    return (
                         <TableIndivHeader className={content?.className}>
                              {content?.title}
                         </TableIndivHeader>
                    );
               })}
          </div>
     );
};

export default CustomerTableHeader;
