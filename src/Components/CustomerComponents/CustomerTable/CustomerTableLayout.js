import React from "react";
import CustomerTableTopper from "./CustomerTableTopper";
import CustomerTableHeader from "./CustomerTableHeader";
import CustomerTableListing from "./CustomerTableListing";
import { useSelector } from "react-redux";

const CustomerTableLayout = () => {
  const { searchedCustomerList } = useSelector((state) => {
    return state.customer;
  });

  return (
    <>
      <div className="  flex-col flex-shrink-0 flex z-[5]">
        <CustomerTableTopper />
        <div className=" w-full h-fit flex flex-col overflow-auto">
          <div className=" w-full h-fit flex flex-col gap-[1rem] ">
            <CustomerTableHeader />
            <CustomerTableListing content={searchedCustomerList} />
          </div>
        </div>
      </div>
    </>
  );
};

export default CustomerTableLayout;
