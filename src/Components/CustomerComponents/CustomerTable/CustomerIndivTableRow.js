import React from "react";
import TableTextContent from "../../Utils/TableStructures/TableTextContent";
import CheckBoxComponent from "../../../BasicUIElements/CheckBoxComponent";
import { customerTableConfig } from "../../../Config/CustomerTableConfig";
import TableLinkContent from "../../Utils/TableStructures/TableLinkContent";
import { routes } from "../../../Config/routesConfig";

import edit from "../../../Assest/Utils/edit.png";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import { useDispatch } from "react-redux";
import {
  customerPageViewMode,
  customerSliceActions,
} from "../../../Store/CustomerSlices/CustomerSlice";
import { customerEditSliceActions } from "../../../Store/CustomerSlices/CustomerEditSlice";
import UseRouterAuthentication from "../../../Hooks/UseRouterAuthentication";

const dateFormatFunction = (dt) => {
  let date = new Date(dt);

  let fullyear = date.getFullYear();
  let month =
    date.getMonth() + 1 < 10
      ? "0" + (date.getMonth() + 1)
      : date.getMonth() + 1;

  let dateNO = date.getDate();

  let format = fullyear + "-" + month + "-" + dateNO;

  return format;
};

const CustomerIndivTableRow = ({ content }) => {
  const dispatch = useDispatch();
  const { routeAuthentication } = UseRouterAuthentication();

  const customerId = content.id;
  const customerName = content?.customerName;
  const emailText = content?.email;
  const phoneNo = content?.phoneNumber;
  const orderNo = content?.orderCount;
  const orderAmount = content?.totalOrder;
  const customerSince = dateFormatFunction(content?.customerSince);
  const lastPurchase = dateFormatFunction(content?.lastPurchase);
  const ifReferred = content?.ifReferred;
  const referredCustomerName = content?.referredCustomerName;
  const referredCustomerId = content?.referredCustomerId;
  const finalJobLink = content?.finalJobLink;

  const editClickHandler = () => {
    dispatch(
      customerEditSliceActions.updateContent({
        id: customerId,
        referredCustomerId: {
          id: content?.referredCustomerId,
          content: content?.referredCustomerId,
        },
        referredCustomerName: content?.referredCustomerName,
        ifReferred: {
          id: content?.ifReferred,
          content: content?.ifReferred,
        },
        finalJobLink: content?.finalJobLink,
      })
    );
    dispatch(customerSliceActions.updateUIPageMode(customerPageViewMode.edit));
  };

  return (
    <div className=" w-fit h-[52px] border-b-2 flex flex-row hover:bg-[#F0FEFF]  ">
      {/* <div className=" w-[60px] h-full flex flex-row justify-center items-center">
                    <CheckBoxComponent value={false} onClick={() => {}} />
               </div> */}
      <TableLinkContent
        size={customerTableConfig[0].className}
        className=" font-[700] font-inter text-[14px] text-[#019BA2] hover:underline-offset-4"
        to={routes.customer.directLink + "/" + customerId}
      >
        {customerId}
      </TableLinkContent>
      <TableTextContent
        size={customerTableConfig[1].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {customerName}
      </TableTextContent>
      <TableTextContent
        size={customerTableConfig[2].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        <div className=" w-full flex flex-row gap-[0.30rem] ">
          {emailText}
          {/* <CopyButtonComponent text={emailText} /> */}
        </div>
      </TableTextContent>
      <TableTextContent
        size={customerTableConfig[3].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        <div className=" w-full flex flex-row gap-[0.30rem] ">
          {phoneNo}
          {/* <CopyButtonComponent text={phoneNo} /> */}
        </div>
      </TableTextContent>
      <TableTextContent
        size={customerTableConfig[4].className}
        className=" w-full text-center font-[400] font-inter  text-[14px] text-[#6E7079]"
      >
        <div className=" flex flex-row justify-start text-center">
          {orderNo}
        </div>
      </TableTextContent>
      <TableTextContent
        size={customerTableConfig[5].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {orderAmount}
      </TableTextContent>
      <TableTextContent
        size={customerTableConfig[6].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {customerSince}
      </TableTextContent>
      <TableTextContent
        size={customerTableConfig[7].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {lastPurchase}
      </TableTextContent>
      <TableTextContent
        size={customerTableConfig[8].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {false && (
          <div className="ps-2 pe-2 pt-1 pb-1 bg-[#32936F29] rounded-[6px]  text-[#519C66] ">
            Active
          </div>
        )}
        {true && (
          <div className="ps-2 pe-2 pt-1 pb-1 bg-[#AB1917]  bg-opacity-10 rounded-[6px]  text-[#AB1917] ">
            In-Active
          </div>
        )}
      </TableTextContent>
      <TableTextContent
        size={customerTableConfig[9].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {ifReferred}
      </TableTextContent>
      <TableTextContent
        size={customerTableConfig[10].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {referredCustomerName}
      </TableTextContent>
      <TableTextContent
        size={customerTableConfig[11].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {referredCustomerId}
      </TableTextContent>
      <TableTextContent
        size={customerTableConfig[12].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {finalJobLink}
      </TableTextContent>

      <div className=" w-[60px] h-full flex flex-row justify-center items-center">
        {routeAuthentication("customers", "edit") && (
          <ButtonComponent
            onClick={editClickHandler}
            className=" w-[24px] h-[24px] flex flex-rwo justify-center items-center "
          >
            <img
              src={edit}
              className=" w-full h-full object-contain"
              alt="edit"
            />
          </ButtonComponent>
        )}
      </div>
    </div>
  );
};

export default CustomerIndivTableRow;
