import React from "react";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import { FaDownload } from "react-icons/fa6";
import UseJsObj2CSV from "../../../Hooks/UseJsObj2CSV";

const CustomerTableTopper = () => {
  const { downloadCustomerDashboard } = UseJsObj2CSV();
  return (
    <div className=" min-w-full w-fit  h-[45px]  flex flex-row justify-between items-center ps-8 pe-8 border-b-2 ">
      <h1 className=" text-[#45464E] font-[600] font-inter text-[16px] flex flex-row flex-shrink-0 ">
        Customers
      </h1>

      <div className=" w-fit h-full flex flex-row items-center  gap-[0.8rem]   ">
        <ButtonComponent
          onClick={downloadCustomerDashboard}
          className=" w-fit h-[50px] flex flex-row justify-center items-center gap-[0.25rem]"
        >
          <FaDownload />
        </ButtonComponent>
      </div>
    </div>
  );
};

export default CustomerTableTopper;
