import React, { useEffect, useState } from "react";
import SectionHeaders from "../Utils/SectionHeaders";
import ButtonComponent from "../../BasicUIElements/ButtonComponent";
import SearchBar from "../../BasicUIElements/SearchBar";
import { useDispatch, useSelector } from "react-redux";
import { customerSliceActions } from "../../Store/CustomerSlices/CustomerSlice";

function isSubarrayPresent(fullString, subArrayString) {
  // Check if the subArrayString is a substring of fullString
  return fullString?.includes(subArrayString);
}

const CustomerHeader = () => {
  const dispatch = useDispatch();
  const { customerList } = useSelector((state) => {
    return state.customer;
  });

  const [searchInput, setSearchInput] = useState("");

  useEffect(() => {
    let array = [];

    for (let item of customerList) {
      if (
        isSubarrayPresent(item?.id?.toLowerCase(), searchInput?.toLowerCase())
      ) {
        array.push(item);
      }
    }

    dispatch(customerSliceActions.updateSearchedCustomerList(array));
  }, [searchInput]);

  return (
    <div className=" w-full h-[50px] flex flex-row justify-between ps-8 pe-8 items-end">
      <SectionHeaders> Customer Summary</SectionHeaders>

      {/* <ButtonComponent className=" w-[205px] h-[36px] bg-mainLinearGradient rounded-[12px] font-inter font-[700] text-[14px] text-[#fff] ">
                    + Add a New Customer
               </ButtonComponent> */}
      <SearchBar
        className="relative w-[176px] h-[29px] rounded-[4px] border-[1px]"
        isLogoAtFront={false}
        value={searchInput}
        onChange={(e) => {
          setSearchInput(e.target.value);
        }}
      />
    </div>
  );
};

export default CustomerHeader;
