import React, { useEffect, useState } from "react";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import SelectComponent from "../../../BasicUIElements/SelectComponent";
import InputComponentWithErrorMessage from "../../../BasicUIElements/InputComponentWithErrorMessage";
import ModelHeaderAndRoute from "../../Utils/UtilsComponents/ModelHeaderAndRoute";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
  customerPageViewMode,
  customerSliceActions,
} from "../../../Store/CustomerSlices/CustomerSlice";
import { Customer_IfReferred } from "../../../Config/CustomerTableConfig";
import CustomerEditSlice, {
  customerEditSliceActions,
  customerEditThunk,
} from "../../../Store/CustomerSlices/CustomerEditSlice";
import EnterOrSelectComponent from "../../../BasicUIElements/EnterOrSelectComponent";
import UseRouterAuthentication from "../../../Hooks/UseRouterAuthentication";
import { routes } from "../../../Config/routesConfig";

const CustomerEditLayout = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { auth, access, ui } = useSelector((state) => {
    return state.master;
  });
  const customerEdit = useSelector((state) => {
    return state.customerEdit;
  });

  const content = customerEdit.content;
  const customerUI = customerEdit.ui;

  const updateClickHandler = () => {
    dispatch(customerEditThunk(content));
  };

  const { routeAuthentication } = UseRouterAuthentication();
  useEffect(() => {
    if (!routeAuthentication("customers", "edit")) {
      navigate(routes?.customer?.directLink);
    }
  }, [auth, access]);

  return (
    <div className=" md:w-[424px] w-[340px] max-h-[95vh] h-fit overflow-y-auto bg-[#fff] rounded-[6px] p-4 pb-[4rem] ">
      <ModelHeaderAndRoute
        className="w-full h-fit flex flex-row justify-between ps-4 pe-4 "
        title="Edit"
        onClick={() => {
          dispatch(
            customerSliceActions.updateUIPageMode(customerPageViewMode.view)
          );
        }}
      />
      <h1 className=" pt-2 pb-2 font-[500] font-inter text-[#8B8D97] text-[16px] ps-6">
        Customer Information
      </h1>

      <div className=" w-full h-fit flex flex-col items-center  gap-[1rem]">
        <div className=" w-full h-fit flex flex-row justify-center ">
          <SelectComponent
            className=" md:w-[375px] w-[300px] h-[52px] flex flex-row justify-between rounded-[8px] ps-5  text-[#53545C]"
            textClassName="text-[16px] font-montserrat font-semibold text-[#53545C]"
            optionarr={Customer_IfReferred}
            updateFunction={(e) => {
              dispatch(customerEditSliceActions.updateContentIfReferred(e));
            }}
            value={content?.ifReferred}
          />
        </div>
        <div className=" w-fit h-fit flex flex-col ">
          <InputComponentWithErrorMessage
            type="text"
            name="Customer Name"
            placeholder="Customer Name"
            errorMessage=""
            value={content?.referredCustomerName}
            onChange={(e) => {
              dispatch(
                customerEditSliceActions.updateContentReferredCustomerName(
                  e.target.value
                )
              );
            }}
            onBlur={() => {}}
            isValid={false}
            isTouched={false}
            className="w-[375px] h-[52px] ps-5  border border-[#D8E1F2] rounded-[6px] text-[16px] text-[#53545C] font-semibold placeholder:font-poppins placeholder:text-[16px] placeholder:font-[400] placeholder:text-[#ABAFB1] "
          />
        </div>

        <div className=" w-full h-fit flex flex-row justify-center ">
          <EnterOrSelectComponent
            optionarr={ui.customerList}
            value={content?.referredCustomerId}
            updateFunction={(e) => {
              dispatch(
                customerEditSliceActions.updateContentReferredCustomerId(e)
              );
            }}
          />
        </div>

        <div className=" w-fit h-fit flex flex-col ">
          <InputComponentWithErrorMessage
            type="text"
            name="Final Job LInk"
            placeholder="Final Job Link"
            errorMessage=""
            value={content?.finalJobLink}
            onChange={(e) => {
              dispatch(
                customerEditSliceActions.updateContentFinalJobLink(
                  e.target.value
                )
              );
            }}
            onBlur={() => {}}
            isValid={false}
            isTouched={false}
            className="w-[375px] h-[52px] ps-5  border border-[#D8E1F2] rounded-[6px] text-[16px] text-[#53545C] font-semibold placeholder:font-poppins placeholder:text-[16px] placeholder:font-[400] placeholder:text-[#ABAFB1] "
          />
        </div>

        <div className=" w-full h-fit flex flex-row justify-center gap-[1rem]">
          <ButtonComponent
            onClick={() => {
              dispatch(
                customerSliceActions.updateUIPageMode(customerPageViewMode.view)
              );
            }}
            className=" w-[45%] h-[58px] rounded-[6px] border-[#00BD94] border-2 bg-clip-text text-transparent bg-mainLinearGradient text-[20px] font-inter font-[500] flex flex-row justify-center items-center"
          >
            Cancel
          </ButtonComponent>
          <ButtonComponent
            onClick={updateClickHandler}
            isLoading={customerUI.updateButtonIsLoading}
            className=" w-[45%] h-[58px] rounded-[6px] bg-mainLinearGradient text-[20px] font-inter font-[500] text-[#fff] flex flex-row justify-center items-center"
          >
            Update
          </ButtonComponent>
        </div>
      </div>
    </div>
  );
};

export default CustomerEditLayout;
