import React, { useEffect } from "react";
import CustomerHeader from "./CustomerHeader";
import CustomerDataBox from "./CustomerDataBox";
import CustomerTableLayout from "./CustomerTable/CustomerTableLayout";
import { useDispatch, useSelector } from "react-redux";
import { customerListFetchThunk } from "../../Store/CustomerSlices/CustomerSlice";
import UseRouterAuthentication from "../../Hooks/UseRouterAuthentication";

const CustomerLayout = () => {
  const dispatch = useDispatch();
  const { auth, access } = useSelector((state) => {
    return state.master;
  });
  const { isDashboardAccessable } = UseRouterAuthentication();

  useEffect(() => {
    isDashboardAccessable("customers");
    dispatch(customerListFetchThunk());
  }, [auth, access]);

  return (
    <div className=" w-full h-full overflow-y-auto">
      <CustomerHeader />
      {/* <CustomerDataBox /> */}
      <div className=" w-full h-[2rem]"></div>
      <CustomerTableLayout />
    </div>
  );
};

export default CustomerLayout;
