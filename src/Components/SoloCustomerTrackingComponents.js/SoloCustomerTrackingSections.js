import React from "react";
import { RxDividerVertical } from "react-icons/rx";
import { formatDate_taskManagement } from "../../Config/TaskManagementConfig";
import TrackingOrderDate_Template from "../../UI_templates/UI_template_tracking/TrackingOrderDate_Template";

function formatDate(dateObj) {
  const options = { month: "short", day: "numeric", year: "numeric" };
  return formatDate_taskManagement(new Date(dateObj));
}

const func = (content) => {
  const data = [
    {
      title: "Order Confirm",
      date: formatDate(new Date(content?.orderDate)),
      isCompleted: true,
    },
    {
      title: "Ready for Delivery",
      date: formatDate(new Date(content?.orderDate)),
      isCompleted:
        content?.status === "Ready for Delivery" ||
        content?.status === "Deliverd" ||
        content?.status === "Out for Delivery" ||
        content?.status === "Out For Delivery",
    },
    {
      title: "Out For Delivery",
      date: formatDate(new Date(content?.orderDate)),
      isCompleted:
        content?.status === "Deliverd" ||
        content?.status === "Out for Delivery" ||
        content?.status === "Out For Delivery",
    },

    {
      title: "Delivery Date",
      date: formatDate(new Date(content?.orderDate)),
      isCompleted: content?.status === "Deliverd",
    },
  ];

  return data;
};

const SoloCustomerTrackingSections = ({ content = {}, index = 0 }) => {
  const featureArray = ["", "", ""];

  const OrderData = func(content);

  return (
    <div className=" w-full h-fit mb-[2rem] mt-[2rem] ">
      {/* Heading  */}
      <div className=" w-full flex flex-row justify-between">
        <div className=" flex flex-col gap-[1rem]">
          <h1 className=" font-[400] font-poppins md:text-[20px] text-[14px] text-[#475467]">
            {index + 1}. {content?.product}
          </h1>

          <div className="w-fit h-[30px] flex flex-row items-center gap-[0.5rem]">
            {featureArray.map((items, index) => {
              return (
                <>
                  <p className=" font-[400] font-inter md:text-[16px] text-[12px]  text-[#667085]">
                    {items}
                  </p>
                  {featureArray.length - 1 !== index && (
                    <p>
                      <RxDividerVertical size={22} color="#667085" />
                    </p>
                  )}
                </>
              );
            })}
          </div>
        </div>

        <div className=" flex flex-col items-end gap-[1rem] pe-8">
          <h1 className=" font-[600] font-inter md:text-[20px] text-[14px] text-[#475467]">
            ₹{content?.total}
          </h1>
          <p className=" md:text-[16px] text-[12px] font-[400] font-inter text-[#667085]">
            Oty: {content?.quantity}
          </p>
        </div>
      </div>

      {/* Statistics Section */}

      <div className=" w-full h-fit flex flex-row justify-start md:gap-[1rem] gap-[0.5rem] md:mt-[3rem] mt-[1.5rem] md:mb-[3rem] mb-[2rem]">
        <div className=" flex flex-row gap-[0.5rem]">
          <h1 className=" font-[400] font-inter text-[#667085] md:text-[18px] text-[14px]">
            Order date:
          </h1>
          <h2 className=" font-[600] font-inter md:text-[18px] text-[14px] text-[#1D2939]">
            {formatDate_taskManagement(new Date(content?.orderDate))}
          </h2>
        </div>

        <p className=" md:block hidden">
          <RxDividerVertical size={26} color="#667085" />
        </p>
        <p className=" md:hidden ">
          <RxDividerVertical size={20} color="#667085" />
        </p>

        <div className=" flex flex-row gap-[0.5rem]">
          <h1 className=" font-[400] font-inter text-[#12B76A] md:text-[18px] text-[14px]">
            Estimated delivery:
          </h1>
          <h2 className=" font-[500] font-inter text-[18px] text-[#12B76A]">
            {/* Feb 16, 2024 */}
          </h2>
        </div>
      </div>

      <TrackingOrderDate_Template content={OrderData} />
    </div>
  );
};

export default SoloCustomerTrackingSections;
