import React, { useEffect } from "react";
import SoloCustomerTrackingSections from "./SoloCustomerTrackingSections";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchCustomerBill,
  singleCustomerBillTrackingSliceActions,
} from "../../Store/CustomerSlices/singleCustomerBillTracking";
import { useParams } from "react-router-dom";
import TrackingHeader_Template from "../../UI_templates/UI_template_tracking/TrackingHeader_Template";
import TrackingDivisor_Template from "../../UI_templates/UI_template_tracking/TrackingDivisor_Template";
import TrackingOrderSummary_template from "../../UI_templates/UI_template_tracking/TrackingOrderSummary_template";

const SoloCustomerTrackingLayout = () => {
  const content = useSelector((state) => {
    return state?.singlecustomerbilltracking;
  });

  let { customerid, billid } = useParams();
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(singleCustomerBillTrackingSliceActions.updateReset());
    dispatch(
      singleCustomerBillTrackingSliceActions.updateBillIdCustomerId({
        customerId: customerid,
        billId: billid,
      })
    );
    dispatch(fetchCustomerBill(customerid, billid));
  }, []);

  return (
    <div className=" w-full h-fit ps-6 pe-6  flex flex-col gap-[2rem]">
      <TrackingHeader_Template content={content} />
      <TrackingDivisor_Template />
      {content?.orders.map((item, index) => {
        return (
          <>
            <SoloCustomerTrackingSections content={item} index={index} />
            <TrackingDivisor_Template />
          </>
        );
      })}

      <TrackingOrderSummary_template content={content} />
    </div>
  );
};

export default SoloCustomerTrackingLayout;
