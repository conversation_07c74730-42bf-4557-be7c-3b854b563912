import React, { useRef } from "react";
import ButtonComponent from "../../BasicUIElements/ButtonComponent";

const DatePickerComponents = ({
     className = " relative w-[375px] h-[52px] rounded-[8px] bg-[#F6F7FB] flex flex-row justify-start gap-[1rem]",
     value = new Date(),
     onChange = () => {},
     title = " ",
     isLogoPresent = true,
     canPick = true,
}) => {
     const dateInputRef = useRef();

     const dateElementClick = () => {
          if (!canPick) return;
          dateInputRef.current.showPicker();
     };
     value = new Date(value);
     let dateText = value.getDate();
     if (dateText < 10) {
          dateText = "0" + dateText;
     }
     let monthText = value.getMonth() + 1;
     if (monthText < 10) {
          monthText = "0" + monthText;
     }
     let year = value.getFullYear();

     return (
          <>
               <ButtonComponent
                    onClick={dateElementClick}
                    className={className}
               >
                    {isLogoPresent && (
                         <div className=" w-[60px] h-full flex flex-col justify-center items-center ">
                              <svg
                                   width="21"
                                   height="22"
                                   viewBox="0 0 21 22"
                                   fill="none"
                                   xmlns="http://www.w3.org/2000/svg"
                              >
                                   <path
                                        d="M1.59375 8.40421H19.4177"
                                        stroke="#5E6366"
                                        stroke-width="1.5"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                   />
                                   <path
                                        d="M14.9429 12.3097H14.9522"
                                        stroke="#5E6366"
                                        stroke-width="1.5"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                   />
                                   <path
                                        d="M10.5054 12.3097H10.5147"
                                        stroke="#5E6366"
                                        stroke-width="1.5"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                   />
                                   <path
                                        d="M6.05622 12.3097H6.06549"
                                        stroke="#5E6366"
                                        stroke-width="1.5"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                   />
                                   <path
                                        d="M14.9429 16.1962H14.9522"
                                        stroke="#5E6366"
                                        stroke-width="1.5"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                   />
                                   <path
                                        d="M10.5054 16.1962H10.5147"
                                        stroke="#5E6366"
                                        stroke-width="1.5"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                   />
                                   <path
                                        d="M6.05622 16.1962H6.06549"
                                        stroke="#5E6366"
                                        stroke-width="1.5"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                   />
                                   <path
                                        d="M14.5452 1V4.29078"
                                        stroke="#5E6366"
                                        stroke-width="1.5"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                   />
                                   <path
                                        d="M6.46711 1V4.29078"
                                        stroke="#5E6366"
                                        stroke-width="1.5"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                   />
                                   <path
                                        fill-rule="evenodd"
                                        clip-rule="evenodd"
                                        d="M14.7383 2.5791H6.27096C3.33427 2.5791 1.5 4.21504 1.5 7.22213V16.2718C1.5 19.3261 3.33427 20.9999 6.27096 20.9999H14.729C17.675 20.9999 19.5 19.3545 19.5 16.3474V7.22213C19.5092 4.21504 17.6842 2.5791 14.7383 2.5791Z"
                                        stroke="#5E6366"
                                        stroke-width="1.5"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                   />
                              </svg>
                         </div>
                    )}

                    <div className=" w-fit h-full flex flex-col justify-evenly">
                         <h1 className=" font-inter font-[400]  text-[12px]  text-[#5E6366] ">
                              {title}
                         </h1>
                         <h2 className=" font-inter font-[400] text-[16px] text-[#ABAFB1]">
                              {dateText}/{monthText}/{year}
                         </h2>
                    </div>
               </ButtonComponent>
               <input
                    ref={dateInputRef}
                    type="date"
                    value={value}
                    onChange={onChange}
                    className=" absolute opacity-0 "
               />
          </>
     );
};

export default DatePickerComponents;
