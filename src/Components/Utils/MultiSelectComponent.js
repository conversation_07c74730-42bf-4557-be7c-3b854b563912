import React, { useState } from "react";
import ButtonComponent from "../../BasicUIElements/ButtonComponent";
import LeftArrowSVG from "../../Assest/SVG/LeftArrowSVG";
import CheckBoxComponent from "../../BasicUIElements/CheckBoxComponent";
import { FaXmark } from "react-icons/fa6";

const SelectedComponent = ({ text = " ", onClick = () => {} }) => {
     return (
          <div className=" w-fit h-[30px] flex flex-row items-center rounded-[8px] gap-[1rem] ps-1 pe-1 border-2 text-[#5E6366] text-[16px] z-[1000] ">
               <div>{text.content}</div>
               <div className=" w-[20px] h-full flex flex-col justify-center ">
                    <ButtonComponent
                         onClick={() => {
                              onClick(text, true);
                         }}
                         className=" w-[18px] h-[18px] rounded-[9px] flex flex-row justify-center items-center border-2 border-[#ABAFB1]"
                    >
                         <FaXmark />
                    </ButtonComponent>
               </div>
          </div>
     );
};

const OptionComponent = ({
     text,
     isSelected = () => {},
     onClickHandler = () => {},
}) => {
     let isSelect = isSelected(text);
     const onclick = () => {
          onClickHandler(text, isSelect);
     };
     return (
          <ButtonComponent
               onClick={onclick}
               className=" w-full h-[40px] flex flex-row ps-4 pe-4 items-center border-b-2 "
          >
               <CheckBoxComponent value={isSelect} />
               <div className=" flex-1 ">
                    <div className=" w-full h-full flex flex-row ps-3  text-[#2B2F32] font-inter text-[16px]">
                         {text.content}
                    </div>
               </div>
          </ButtonComponent>
     );
};

const MultiSelectComponent = ({
     className = "w-[360px] h-[58px] border-2 rounded-[8px] flex flex-row justify-between items-center ps-4 pe-4 relative",
     optionarr = [1, 2, 3, 4, 5, 6, 7],
     children,
     value = [],
     title = "Assingee",
     updatedFunction = () => {},
}) => {
     // model display none to block
     const [isModelOpen, setIsModeOpen] = useState(false);

     const toggleIsModelOpen = () => {
          setIsModeOpen((state) => {
               return !state;
          });
     };

     const isSelected = (textObj) => {
          for (let i = 0; i < value.length; i++) {
               if (textObj.id === value[i].id) {
                    return true;
               }
          }
          return false;
     };

     const onclickOptionHandler = (textObj, isSelect) => {
          toggleIsModelOpen();
          let array = [];
          if (isSelect) {
               array = value.filter((state) => textObj.id !== state.id);
          } else {
               array = [...value, textObj];
          }
          updatedFunction(array);
     };

     return (
          <ButtonComponent onClick={toggleIsModelOpen} className={className}>
               <h1 className=" flex-1 truncate text-start flex flex-row gap-[1rem] flex-wrap  pt-[0.5rem] pb-[0.5rem]">
                    {value.length === 0 && title}
                    {value.map((item) => {
                         return (
                              <SelectedComponent
                                   onClick={onclickOptionHandler}
                                   text={item}
                              />
                         );
                    })}
               </h1>
               {children}
               <div
                    style={{
                         rotate: "270deg",
                    }}
                    className="w-fit h-fit"
               >
                    <div
                         className={` w-fit h-full  ${
                              isModelOpen ? "rotate-180" : "rotate-0"
                         } `}
                    >
                         <LeftArrowSVG />
                    </div>
               </div>
               {isModelOpen && (
                    <div className=" absolute w-full h-fit max-h-[200px] left-0 top-[100%] z-[100000] rounded-[8px] bg-white border-2 overflow-y-scroll">
                         {optionarr.map((opt) => {
                              return (
                                   <OptionComponent
                                        text={opt}
                                        isSelected={isSelected}
                                        onClickHandler={onclickOptionHandler}
                                   />
                              );
                         })}
                    </div>
               )}
          </ButtonComponent>
     );
};

export default MultiSelectComponent;
