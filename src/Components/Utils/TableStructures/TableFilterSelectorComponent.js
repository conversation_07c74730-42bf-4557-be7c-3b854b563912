import React, { useState } from "react";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";

import sort from "../../../Assest/Utils/sort.png";
import { TiTick } from "react-icons/ti";

const Option = ({
  content = {},
  onClick = () => {},
  checkIsTickContent = () => {},
}) => {
  let isTick = checkIsTickContent(content);

  const clickHandler = () => {
    onClick(content, isTick);
  };

  return (
    <ButtonComponent
      onClick={clickHandler}
      className=" w-full h-[30px] border-b-2 flex flex-row items-center z-[500000000]  "
    >
      <div className=" w-[25px] h-full flex flex-row justify-center flex-shrink-0 items-center ">
        {isTick && <TiTick size={20} />}
      </div>

      <div className=" flex-1  text-nowrap">{content?.content}</div>
    </ButtonComponent>
  );
};

const TableFilterSelectorComponent = ({
  title = "",
  className = " w-[150px] h-[30px]",
  optionarr = [],
  valueArray = [],
  updateFunction = () => {},
}) => {
  const [isActive, setIsActive] = useState(false);

  const checkIsTickContent = (content) => {
    for (let i = 0; i < valueArray.length; i++) {
      let value = valueArray[i];

      if (value.id.toUpperCase() === content.id.toUpperCase()) {
        return true;
      }
    }

    return false;
  };

  const optionClickHandler = (content, isTick) => {
    updateFunction(content, isTick);
  };
  return (
    <ButtonComponent
      onClick={() => {
        setIsActive((state) => {
          return !state;
        });
      }}
      className={className}
    >
      <div>{title}</div>
      <div className=" w-[24px] h-[24px] flex flex-row justify-center items-center ">
        <img
          src={sort}
          className=" w-full h-full object-contain"
          alt="logo in table header"
        />
      </div>

      {isActive && (
        <div className=" bg-[#fff] absolute z-[500000] w-full top-[101%] left-[0%] border-2 max-h-[170px] h-fit overflow-y-auto overflow-hidden ">
          {optionarr.map((state) => {
            return (
              <Option
                content={state}
                onClick={optionClickHandler}
                checkIsTickContent={checkIsTickContent}
              />
            );
          })}
        </div>
      )}
    </ButtonComponent>
  );
};

export default TableFilterSelectorComponent;
