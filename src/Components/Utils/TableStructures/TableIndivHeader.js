import React from "react";

import sort from "../../../Assest/Utils/sort.png";

const TableIndivHeader = ({
     className = "w-[180px] h-full ",
     children = <></>,
     isLogo = false,
}) => {
     return (
          <div
               className={` ${className} flex flex-row justify-start items-center gap-[0.35rem]  `}
          >
               <h1 className="font-inter font-[400] text-[14px] text-[#2C2D33]">
                    {children}
               </h1>
               {isLogo && (
                    <div className=" w-[24px] h-[24px] flex flex-row justify-start items-center ">
                         <img
                              src={sort}
                              className=" w-full h-full object-contain"
                              alt="logo in table header"
                         />
                    </div>
               )}
          </div>
     );
};

export default TableIndivHeader;
