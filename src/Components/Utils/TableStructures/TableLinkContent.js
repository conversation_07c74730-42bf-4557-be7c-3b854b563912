import React from "react";
import { Link } from "react-router-dom";

const TableLinkContent = ({
     to,
     children = "",
     size = "",
     className = " font-[400] font-inter text-[14px] text-[#6E7079]",
}) => {
     return (
          <div
               className={
                    size + " flex flex-row justify-start items-center ps-3 "
               }
          >
               <Link to={to} className={className}>
                    {children}
               </Link>
          </div>
     );
};

export default TableLinkContent;
