import React from "react";
import DataStructureBoxImageContainer from "./Utils/DataStructureBoxImageContainer";

const DataStructureBoxHeader = ({ logo, children }) => {
     return (
          <div className=" w-full h-[50px] flex flex-row justify-between  ">
               {logo ? (
                    <DataStructureBoxImageContainer logo={logo} />
               ) : (
                    <div></div>
               )}

               <div>{children}</div>
          </div>
     );
};

export default DataStructureBoxHeader;
