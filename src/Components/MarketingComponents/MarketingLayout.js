import React, { useEffect, useState } from "react";
import ButtonComponent from "../../BasicUIElements/ButtonComponent";
import SalesAndMarketingTableLayout from "../SalesAndMarketingComponents/SalesAndMarketingTable/SalesAndMarketingTableLayout";
import SectionHeaders from "../Utils/SectionHeaders";
import MarketingDataBox from "./MarketingDataBox";
import { useDispatch, useSelector } from "react-redux";
import {
  marketing_headerdata_thunk,
  marketingSliceActions,
} from "../../Store/MarketingSlice/MarketingSlices";
import { useNavigate } from "react-router-dom";
import { routes } from "../../Config/routesConfig";
import { sales_load_addForm_thunk } from "../../Store/SalesAndMarketingSlice/SalesAndMarketingSlice";
import MarketingTableLayout from "./MarketingTable/MarketingTableLayout";
import UseMarketing, { dateObj } from "../../Hooks/UseMarketing";
import UseRouterAuthentication from "../../Hooks/UseRouterAuthentication";
import DashboardNavbar_template from "../../UI_templates/Dashboard_Templates/DashboardNavbar_template";
import DateFilterTemplateComponent from "../../UI_templates/DateFilterTemplate/DateFilterTemplateComponent";

const MarketingLayout = () => {
  const { list, headerList, filter } = useSelector((state) => {
    return state.marketing;
  });

  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { auth, access } = useSelector((state) => {
    return state.master;
  });

  const { fetchList, fetchAnalytics, fetch_list, loadTableHeaderData } =
    UseMarketing();
  const { isDashboardAccessable, routeAuthentication } =
    UseRouterAuthentication();

  useEffect(() => {
    isDashboardAccessable("marketing");
    // fetchList();
    fetch_list();
    fetchAnalytics();
    dispatch(marketing_headerdata_thunk(auth));
    loadTableHeaderData();
  }, [auth, access]);

  return (
    <div className=" w-full h-full flex flex-col">
      <DashboardNavbar_template
        listLink={routes?.marketing?.directLink}
        insightsLink={routes?.marketingAnalytics?.directLink}
        dashboard="marketing"
        subpages={[
          { title: "Walk In", path: routes?.marketingWalkin.directLink },
        ]}
      />

      <div className="  w-full h-fit ps-4 flex md:flex-row-reverse flex-col  pe-4  flex-nowrap overflow-y-auto justify-between flex-shrink-0 gap-[1rem] mt-[3rem]">
        {routeAuthentication("marketing", "write") && (
          <ButtonComponent
            onClick={() => {
              // dispatch(sales_load_addForm_thunk());
              navigate("?page=" + routes?.marketingAdd?.searchParams);
            }}
            className=" w-[215px] h-[36px] bg-mainLinearGradient flex-shrink-0 rounded-[12px] font-inter font-[700] text-[14px] text-[#fff] "
          >
            + Add a New Marketing Lead
          </ButtonComponent>
        )}
        <div className=" w-fit flex flex-col gap-[1.5rem] ">
          <SectionHeaders>Marketing Summary</SectionHeaders>
          <MarketingDataBox headerData={headerList} />
        </div>
      </div>
      <div
        onScroll={() => {
          console.log("alsdfjk");
        }}
        className=" flex-1 overflow-hidden relative"
      >
        <div id="marketingtable" className=" w-full h-full  overflow-auto">
          {/* <SalesAndMarketingTableLayout list={list} /> */}
          <DateFilterTemplateComponent
            fetch={fetch_list}
            updateSlice={marketingSliceActions.updateFilterDate}
            dateObj={dateObj}
            customDate="Custom Date"
            date={filter?.date}
          />

          <MarketingTableLayout list={list} />
        </div>
      </div>
    </div>
  );
};

export default MarketingLayout;
