import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { SalesAndMarketingSliceActions } from "../../../Store/SalesAndMarketingSlice/SalesAndMarketingSlice";
import {
  formData_parser,
  formEnginee_Formatee_Structure_and_State,
} from "../../../Dashboard_API_calling_and_formatting/Form_Formate_and_API";
import { salesAndMarketingAddALeadFunction } from "../../SalesAndMarketingComponents/SalesAndMarketing_AddLead/SalesAndMarketingAddLeadLayout";
import { routes } from "../../../Config/routesConfig";
import ModelHeaderAndRoute from "../../Utils/UtilsComponents/ModelHeaderAndRoute";
import FormEnginee from "../../../UI_Enginee/Form/FormEnginee";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import { useNavigate } from "react-router-dom";
import UseSalesAndMarketing from "../../../Hooks/UseSalesAndMarketing";
import UseMarketing, {
  fetch_marketing_add_form_enginee,
} from "../../../Hooks/UseMarketing";
import UseRouterAuthentication from "../../../Hooks/UseRouterAuthentication";
import { marketingSliceActions } from "../../../Store/MarketingSlice/MarketingSlices";
import { API_ENDPOINTS, BaseURL, defaultHeaders } from "../../../API/APIConfig";
import { PostAPI } from "../../../API/PostAPI";
import toast from "react-hot-toast";
import { vaildator_Form_function } from "../../../UI_Enginee/Form/FormEngineeConfig";

const MarketingAddLayout = () => {
  const dispatch = useDispatch();

  const fetchList_sales = UseSalesAndMarketing().fetch_list;
  const fetchList_marketing = UseMarketing().fetch_list;

  const { routeAuthentication } = UseRouterAuthentication();
  const { auth, access } = useSelector((state) => {
    return state.master;
  });
  useEffect(() => {
    if (!routeAuthentication("marketing", "write")) {
      navigate(routes?.marketing?.directLink);
    }
  }, [auth, access]);

  const { add } = useSelector((state) => {
    return state.marketing;
  });

  const navigate = useNavigate();

  const [isLoading, setIsLoading] = useState(false);

  const [state, setState] = useState({});
  const [structure, setStructure] = useState([]);

  useEffect(() => {
    setState({});
    setStructure([]);
    // Setting structure and state if not added
    const fetch = async () => {
      let response = await fetch_marketing_add_form_enginee();
      let ss = formEnginee_Formatee_Structure_and_State(response);
      dispatch(marketingSliceActions.updateStructureAndState(ss));
      // console.log(ss.structure);
      setState(ss.state);
      setStructure(ss.structure);
    };

    // Setting structure and state if not added
    if (add.structure?.length === 0) {
      // dispatch(sales_load_addForm_thunk());
      fetch();
    } else {
      setState(add.state);
    }
  }, []);

  const updateFunctionOfForm = (key, value) => {
    setState((state) => {
      return { ...state, [key]: { ...state[key], value: value } };
    });
    // dispatch(SalesAndMarketingSliceActions?.updateState(updateState));
  };

  // Submit Handler
  const submitHandler = async () => {
    let vaildate = vaildator_Form_function(structure, state);

    setState(vaildate?.validate_Obj);

    console.log(vaildate?.validate_Obj);

    if (!vaildate?.isFormValid) {
      toast.error("Fill all mandatory fields to add the marketing lead", {
        position: "top-right",
      });
      return;
    }
    // dispatch(
    //   SalesAndMarketingSliceActions?.updateState(vaildate?.validate_Obj)
    // );
    // if (!vaildate?.isFormValid) {
    //   return;
    // }

    let parser_obj = formData_parser(structure, state);

    // Convert the RE_VISIT_DATE from array of objects to expected format for backend
    let transformedData = { ...parser_obj.parser_obj };
    
    // Handle the new revisit date format
    if (transformedData.RE_VISIT_DATE && Array.isArray(transformedData.RE_VISIT_DATE)) {
      // Extract dates and remarks into separate arrays as expected by the API
      const dates = transformedData.RE_VISIT_DATE.map(item => item.date);
      const remarks = transformedData.RE_VISIT_DATE.map(item => item.remark || "");
      
      // Set the arrays as the server expects
      transformedData.RE_VISIT_DATE = dates;
      transformedData.RE_VISIT_REMARKS = remarks;
    }

    let obj = {
      ...transformedData,
      assignedBy: auth?.id,
      lead_status: "New",
    };

    setIsLoading(true);
    let toast_id = toast.loading("Posting", { position: "top-right" });
    let response = await marketingAddALeadFunction(obj, auth);

    let responsedata = await response.json();

    if (response?.ok) {
      toast.success("Added", { id: toast_id });
    } else {
      toast.error(responsedata?.error, { id: toast_id });
    }

    setIsLoading(false);

    if (response?.ok) {
      fetchList_sales();
      fetchList_marketing();
      closeModal();
    }
  };

  // Close Modal
  const closeModal = () => {
    navigate(routes?.marketing?.directLink);
    dispatch(marketingSliceActions.updateState({}));
    // dispatch(SalesAndMarketingSliceActions?.updateState({}));
  };

  return (
    <div className=" md:w-[424px] w-[340px] max-h-[95vh] h-fit overflow-y-auto bg-[#fff] rounded-[6px] p-4 pb-[1rem] ">
      {/* HEADER  */}
      <ModelHeaderAndRoute
        className="w-full h-fit flex flex-row justify-between ps-4 pe-4 "
        title="Add a Marketing Lead"
        onClick={() => {
          closeModal();
        }}
      />

      {/* Form Enginee */}
      <FormEnginee
        form={structure}
        formStateFunction={updateFunctionOfForm}
        formState={state}
      />

      {/* BUTTON COMPONENT  */}
      <div className=" w-full h-fit mt-[1rem]">
        <div className=" w-full h-fit flex flex-row justify-center gap-[1rem]">
          <ButtonComponent
            onClick={() => {
              closeModal();
            }}
            className=" w-[45%] h-[58px] rounded-[6px] border-[#00BD94] border-2 bg-clip-text text-transparent bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] flex flex-row justify-center items-center"
          >
            Cancel
          </ButtonComponent>
          <ButtonComponent
            onClick={submitHandler}
            isLoading={isLoading}
            className=" w-[45%] h-[58px] rounded-[6px] bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] text-[#fff] flex flex-row justify-center items-center"
          >
            Add a Lead
          </ButtonComponent>
        </div>
      </div>
    </div>
  );
};

export default MarketingAddLayout;

const marketingAddALeadFunction = async (content, auth) => {
  const url = new URL(
    BaseURL.mainURl + API_ENDPOINTS?.salesAndMarketingAddALead
  );

  const bodyObj = content;

  let headers = {
    ...defaultHeaders,
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": ["POST", "GET", "OPTIONS", "DELETE", "PUT"],
    "Access-Control-Allow-Headers": [
      "append",
      "delete",
      "entries",
      "foreach",
      "get",
      "has",
      "keys",
      "set",
      "values",
      "Authorization",
    ],

    redirect: "follow",

    Authorization: auth.token,
  };

  const response = await PostAPI(url, bodyObj, headers);

  return response;
};
