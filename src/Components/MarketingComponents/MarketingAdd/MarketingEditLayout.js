import React, { useEffect, useState } from "react";
import { API_ENDPOINTS, api_headers, BaseURL } from "../../../API/APIConfig";
import { PostAPI } from "../../../API/PostAPI";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import UseSalesAndMarketing from "../../../Hooks/UseSalesAndMarketing";
import UseMarketing, { fetch_marketing_edit_form_enginee } from "../../../Hooks/UseMarketing";
import UseRouterAuthentication from "../../../Hooks/UseRouterAuthentication";
import {
  formData_parser,
  formEnginee_edit_Formatee_Structure_and_State,
} from "../../../Dashboard_API_calling_and_formatting/Form_Formate_and_API";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import ModelHeaderAndRoute from "../../Utils/UtilsComponents/ModelHeaderAndRoute";
import Spinner from "../../../BasicUIElements/Spinner";
import FormEnginee from "../../../UI_Enginee/Form/FormEnginee";
import { marketingSliceActions } from "../../../Store/MarketingSlice/MarketingSlices";
import { routes } from "../../../Config/routesConfig";
import toast from "react-hot-toast";

const MarketingEditLayout = () => {
  const [searchParams] = useSearchParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { fetch_list } = UseSalesAndMarketing();
  const marketingFetchList = UseMarketing().fetchList;

  const { edit } = useSelector((state) => {
    return state.salesAndMarketing;
  });

  const { routeAuthentication } = UseRouterAuthentication();
  const { auth, access } = useSelector((state) => {
    return state.master;
  });
  useEffect(() => {
    if (!routeAuthentication("marketing", "edit")) {
      navigate(routes?.marketing?.directLink);
    }
  }, [auth, access]);

  const [state, setState] = useState({});
  const [isContentLoading, setIsContentLoading] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    let id = searchParams?.get("id");

    let fetch = async () => {
      setIsContentLoading(true);

      try {
        console.log("Fetching marketing edit form for ID:", id);
        let result = await fetch_marketing_edit_form_enginee(id);
        console.log("Edit form result:", result);

        if (!result?.structure || !Array.isArray(result.structure) || result.structure.length === 0) {
          console.error("No valid structure returned from API");
          toast.error("Could not load the edit form. Please try again later.");
          setIsContentLoading(false);
          return false;
        }

        // Extract the structure from the result before passing it to formEnginee_edit_Formatee_Structure_and_State
        // The function expects an array, but result is an object with a structure property
        let ss = formEnginee_edit_Formatee_Structure_and_State(result.structure);
        console.log("Formatted structure and state:", ss);

        setState(ss.state);
        dispatch(
          marketingSliceActions.updateEditStructureAndState({
            ...ss,
            id: id,
          })
        );

        setIsContentLoading(false);

        if (ss.structure?.length === 0) {
          return false;
        } else {
          return true;
        }
      } catch (error) {
        console.error("Error fetching marketing edit form:", error);
        toast.error("An error occurred while loading the edit form. Please try again later.");
        setIsContentLoading(false);
        return false;
      }
    };

    if (id !== edit?.id) {
      fetch();
    } else {
      setState(edit.state);
    }
  }, [edit.id]);

  const updateFunctionOfForm = (key, value) => {
    setState((state) => {
      return { ...state, [key]: { ...state[key], value: value } };
    });
  };

  const closeModal = () => {
    navigate(routes?.marketing?.directLink);
  };

  const clickHandler = async () => {
    let parser_obj = formData_parser(edit.structure, state);
    
    // Convert the RE_VISIT_DATE from array of objects to expected format for backend
    let transformedData = { ...parser_obj.parser_obj };
    
    // Handle the new revisit date format
    if (transformedData.RE_VISIT_DATE && Array.isArray(transformedData.RE_VISIT_DATE)) {
      // Extract dates and remarks into separate arrays as expected by the API
      const dates = transformedData.RE_VISIT_DATE.map(item => item.date);
      const remarks = transformedData.RE_VISIT_DATE.map(item => item.remark || "");
      
      // Set the arrays as the server expects
      transformedData.RE_VISIT_DATE = dates;
      transformedData.RE_VISIT_REMARKS = remarks;
    }

    setIsLoading(true);

    let toast_id = toast.loading("Updating", { position: "top-right" });

    let response = await marketing_edit(
      transformedData,
      searchParams?.get("id")
    );

    if (response?.ok) {
      fetch_list();
      marketingFetchList();
      closeModal();
      toast.success("Updated", { id: toast_id });
    } else {
      let responsedata = await response.json();
      toast.error(responsedata?.error, { id: toast_id });
    }
    setIsLoading(false);
  };

  return (
    <>
      {isContentLoading ? (
        <Spinner />
      ) : (
        <div className=" md:w-[424px] w-[340px] max-h-[95vh] h-fit overflow-y-auto bg-[#fff] rounded-[6px] p-4 pb-[4rem] ">
          <ModelHeaderAndRoute
            className="w-full h-fit flex flex-row justify-between ps-4 pe-4 "
            title="Edit The Lead"
            onClick={() => {
              closeModal();
            }}
          />

          <FormEnginee
            form={edit?.structure}
            formStateFunction={updateFunctionOfForm}
            formState={state}
          />

          <div className=" w-full h-fit mt-[1rem]">
            <div className=" w-full h-fit flex flex-row justify-center gap-[1rem]">
              <ButtonComponent
                onClick={() => {
                  closeModal();
                }}
                className=" w-[45%] h-[58px] rounded-[6px] border-[#00BD94] border-2 bg-clip-text text-transparent bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] flex flex-row justify-center items-center"
              >
                Cancel
              </ButtonComponent>
              <ButtonComponent
                onClick={clickHandler}
                isLoading={isLoading}
                className=" w-[45%] h-[58px] rounded-[6px] bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] text-[#fff] flex flex-row justify-center items-center"
              >
                Edit The Lead
              </ButtonComponent>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
export default MarketingEditLayout;

const marketing_edit = async (content, id) => {
  let url = new URL(
    BaseURL?.mainURl + API_ENDPOINTS?.salesAndMarketingEdit + "/" + id
  );

  let body = content;

  let headers = { ...api_headers };

  let response = await PostAPI(url, body, headers, "PUT");

  return response;
};
