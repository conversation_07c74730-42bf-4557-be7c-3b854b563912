import React from "react";
import ModelComponents from "../../../BasicUIElements/ModelComponents";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import { HiXMark } from "react-icons/hi2";

const dateFormatFunction = (dt) => {
  // Check if dt is a valid date
  if (!dt || isNaN(dt.getTime())) {
    return "Invalid date";
  }

  let date = new Date(dt);

  let fullyear = date.getFullYear();
  let month =
    date.getMonth() + 1 < 10
      ? "0" + (date.getMonth() + 1)
      : date.getMonth() + 1;

  let dateNO = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();

  let format = fullyear + "-" + month + "-" + dateNO;

  return format;
};

let list_text = (list = []) => {
  if (list?.length === 0) return "-";

  if (list?.length === 1) return list[0];

  let str = list[0];

  for (let i = 1; i < list?.length; i++) {
    str = str + ", " + list[i];
  }

  return str;
};

const TextCom = ({ title = "", text = "" }) => {
  return (
    <div className=" w-full h-fit flex flex-col gap-[0.25rem]">
      <h1 className="font-inter font-[400] text-[14px] text-[#2C2D33]">
        {title}
      </h1>
      <p className="font-[400] font-inter text-[14px] text-[#6E7079]">
        {text ? text : "------------------------"}
      </p>
    </div>
  );
};

const MarketingTableExpend = ({
  closeModal = () => {},
  title = "",
  content,
  editFunction = () => {},
}) => {
  return (
    <ModelComponents>
      <div className=" w-[424px] max-h-[95vh] h-fit  flex flex-col items-center gap-[0rem] bg-[#fff] rounded-[6px] p-2 z-[150]">
        <div
          className="w-full h-fit flex flex-row justify-between ps-4 pe-4"
          onClick={closeModal}
        >
          <h1 className="  font-poppins font-[400] text-[16px]">{title}</h1>
          <ButtonComponent
            onClick={closeModal}
            className=" w-[24px] h-[24px] rounded-[6px] bg-[#DBF0F2] flex flex-row justify-center items-center"
          >
            <HiXMark size={"20"} />
          </ButtonComponent>
        </div>

        <div className="w-[90%] h-[90%] overflow-auto flex flex-col gap-[1rem]  pt-[1rem] pb-[1rem]">
          {/* -------------------- */}
          <div className=" w-full h-fit flex flex-col gap-[0.25rem]">
            <h1 className="font-inter font-[400] text-[14px] text-[#2C2D33]">
              {"Owner Name"}
            </h1>
            <p className="font-[400] font-inter text-[14px] text-[#6E7079]">
              {content?.OWNER_NAME}
            </p>
          </div>
          {/* -------------------- */}
          <div className=" w-full h-fit flex flex-col gap-[0.25rem]">
            <h1 className="font-inter font-[400] text-[14px] text-[#2C2D33]">
              {"Contact Number 1"}
            </h1>
            <p className="font-[400] font-inter text-[14px] text-[#6E7079]">
              {content?.CONTACT_NO_1}
            </p>
          </div>
          {/* -------------------- */}
          <div className=" w-full h-fit flex flex-col gap-[0.25rem]">
            <h1 className="font-inter font-[400] text-[14px] text-[#2C2D33]">
              {"Contact Number 2"}
            </h1>
            <p className="font-[400] font-inter text-[14px] text-[#6E7079]">
              {content?.CONTACT_NO_2
                ? content?.CONTACT_NO_2
                : "------------------------"}
            </p>
          </div>
          {/* -------------------- */}
          <div className=" w-full h-fit flex flex-col gap-[0.25rem]">
            <h1 className="font-inter font-[400] text-[14px] text-[#2C2D33]">
              {"Contact Number 3"}
            </h1>
            <p className="font-[400] font-inter text-[14px] text-[#6E7079]">
              {content?.CONTACT_NO_3
                ? content?.CONTACT_NO_3
                : "------------------------"}
            </p>
          </div>
          {/* -------------------- */}
          <div className=" w-full h-fit flex flex-col gap-[0.25rem]">
            <h1 className="font-inter font-[400] text-[14px] text-[#2C2D33]">
              {"Contact Number 4"}
            </h1>
            <p className="font-[400] font-inter text-[14px] text-[#6E7079]">
              {content?.CONTACT_NO_4
                ? content?.CONTACT_NO_4
                : "------------------------"}
            </p>
          </div>
          {/* -------------------- */}
          <div className=" w-full h-fit flex flex-col gap-[0.25rem]">
            <h1 className="font-inter font-[400] text-[14px] text-[#2C2D33]">
              {"Lead Status"}
            </h1>
            <p className="font-[400] font-inter text-[14px] text-[#6E7079]">
              {content?.LEAD_STATUS}
            </p>
          </div>
          {/* -------------------- */}
          <div className=" w-full h-fit flex flex-col gap-[0.25rem]">
            <h1 className="font-inter font-[400] text-[14px] text-[#2C2D33]">
              {"AREA"}
            </h1>
            <p className="font-[400] font-inter text-[14px] text-[#6E7079]">
              {content?.AREA}
            </p>
          </div>
          {/* -------------------- */}
          <div className=" w-full h-fit flex flex-col gap-[0.25rem]">
            <h1 className="font-inter font-[400] text-[14px] text-[#2C2D33]">
              {"LANDMARK"}
            </h1>
            <p className="font-[400] font-inter text-[14px] text-[#6E7079]">
              {content?.LANDMARK}
            </p>
          </div>
          {/* -------------------- */}
          <div className=" w-full h-fit flex flex-col gap-[0.25rem]">
            <h1 className="font-inter font-[400] text-[14px] text-[#2C2D33]">
              {"Location"}
            </h1>
            <p className="font-[400] font-inter text-[14px] text-[#6E7079]">
              {content?.location || content?.LOCATION || "------------------------"}
            </p>
          </div>
          {/* -------------------- */}
          <div className=" w-full h-fit flex flex-col gap-[0.25rem]">
            <h1 className="font-inter font-[400] text-[14px] text-[#2C2D33]">
              {"engineer_name"}
            </h1>
            <p className="font-[400] font-inter text-[14px] text-[#6E7079]">
              {content?.engineer_name}
            </p>
          </div>
          {/* -------------------- */}
          <div className=" w-full h-fit flex flex-col gap-[0.25rem]">
            <h1 className="font-inter font-[400] text-[14px] text-[#2C2D33]">
              {"ARCH_FIRM_NAME"}
            </h1>
            <p className="font-[400] font-inter text-[14px] text-[#6E7079]">
              {content?.ARCH_FIRM_NAME}
            </p>
          </div>
          {/* -------------------- */}
          <TextCom title="ARCH_NAME" text={content?.ARCH_NAME} />
          {/* -------------------- */}
          <TextCom title="ARCH_NUMBER" text={content?.ARCH_NUMBER} />
          {/* -------------------- */}
          <TextCom title="PLUMBER_NAME" text={content?.PLUMBER_NAME} />
          {/* -------------------- */}
          <TextCom title="PLUMBER_NUMBER" text={content?.PLUMBER_NUMBER} />

          {/* -------------------- */}
          <TextCom title="KEY_PERSON_NAME" text={content?.KEY_PERSON_NAME} />
          {/* -------------------- */}
          <TextCom
            title="KEY_PERSON_NUMBER"
            text={content?.KEY_PERSON_NUMBER}
          />
          {/* -------------------- */}
          <TextCom title="No_of_Bathrooms" text={content?.No_of_Bathrooms} />
          {/* -------------------- */}
          <TextCom
            title="Tiles_Requirement"
            text={content?.Tiles_Requirement}
          />

          <div className=" w-full h-fit flex flex-col gap-[0.25rem]">
            <h1 className="font-inter font-[400] text-[14px] text-[#2C2D33]">
              {"Follow Up Date 1"}
            </h1>
            <p className="font-[400] font-inter text-[14px] text-[#6E7079]">
              {dateFormatFunction(new Date(content?.FOLLOW_UP_DATE_1))}
            </p>
          </div>
          {/* -------------------- */}
          {/* <div className=" w-full h-fit flex flex-col gap-[0.25rem]">
            <h1 className="font-inter font-[400] text-[14px] text-[#2C2D33]">
              {"Follow Up Date 2"}
            </h1>
            <p className="font-[400] font-inter text-[14px] text-[#6E7079]">
              {dateFormatFunction(new Date(content?.FOLLOW_UP_DATE_2))}
            </p>
          </div> */}

          {/* -------------------- */}
          <TextCom
            title="Follow Up Date 2"
            text={dateFormatFunction(new Date(content?.FOLLOW_UP_DATE_2))}
          />
          {/* -------------------- */}
          <TextCom
            title="TELECALLING_REMARKS"
            text={dateFormatFunction(new Date(content?.TELECALLING_REMARKS))}
          />
          {/* Single revisit date section */}
          <div className="w-full h-fit flex flex-col gap-[0.25rem]">
            <h1 className="font-inter font-[400] text-[14px] text-[#2C2D33]">
              REVISIT DETAILS
            </h1>
            <div className="font-[400] font-inter text-[14px] text-[#6E7079]">
              {content?.RE_VISIT_DATE && content.RE_VISIT_DATE.length > 0 ? (
                <div className="space-y-3">
                  {content.RE_VISIT_DATE.map((dateItem, index) => {
                    // Handle both formats: string dates and object with date property
                    const dateValue = typeof dateItem === 'object' && dateItem.date ? dateItem.date : dateItem;
                    // Get corresponding remark - handle both array of remarks and object format
                    const remarkValue = typeof dateItem === 'object' && dateItem.remark 
                      ? dateItem.remark 
                      : (Array.isArray(content.RE_VISIT_REMARKS) && content.RE_VISIT_REMARKS[index]) 
                        ? content.RE_VISIT_REMARKS[index] 
                        : null;
                    
                    return (
                      <div key={index} className="p-2 border rounded-md bg-gray-50">
                        <div className="font-medium">Visit {index + 1}: {dateFormatFunction(new Date(dateValue))}</div>
                        <div className="mt-1 pl-2 border-l-2 border-gray-300">
                          {remarkValue || "No remarks for this visit"}
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                "No revisit dates"
              )}
            </div>
          </div>
          {/* Old revisit remarks section - removing as it's now combined with dates */}
          {/* <TextCom
            title="REVISIT REMARKS"
            text={content?.RE_VISIT_REMARKS || "No revisit remarks"}
          /> */}
          {/* Old revisit date fields (removed) */}
          {/* -------------------- */}
          {/* <TextCom
            title="RE_VISIT_DATE_1"
            text={dateFormatFunction(new Date(content?.RE_VISIT_DATE_1))}
          />
          <TextCom
            title="RE_VISIT_DATE_2"
            text={dateFormatFunction(new Date(content?.RE_VISIT_DATE_2))}
          />
          <TextCom
            title="RE_VISIT_DATE_3"
            text={dateFormatFunction(new Date(content?.RE_VISIT_DATE_3))}
          />
          <TextCom
            title="RE_VISIT_DATE_4"
            text={dateFormatFunction(new Date(content?.RE_VISIT_DATE_4))}
          /> */}
          {/* -------------------- */}
        </div>

        <div className=" w-full h-fit">
          <div className=" w-full h-fit flex flex-row justify-center gap-[1rem] mt-[1rem]">
            <ButtonComponent
              onClick={editFunction}
              className=" w-[45%] h-[42px] rounded-[6px] border-[#00BD94] hover:text-[16px] duration-200 border-[1px] bg-clip-text text-transparent bg-mainLinearGradient md:text-[14px] text-[12px] font-inter font-[500] flex flex-row justify-center items-center"
            >
              Edit
            </ButtonComponent>
            <ButtonComponent
              onClick={closeModal}
              className=" w-[45%] h-[42px] rounded-[6px] border-[#00BD94] hover:text-[16px] duration-200 border-[1px] bg-clip-text text-transparent bg-mainLinearGradient md:text-[14px] text-[12px] font-inter font-[500] flex flex-row justify-center items-center"
            >
              Close
            </ButtonComponent>
          </div>
        </div>
      </div>
    </ModelComponents>
  );
};

export default MarketingTableExpend;
