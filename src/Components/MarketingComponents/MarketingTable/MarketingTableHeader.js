import React, { useEffect, useRef, useState } from "react";
import TableHeaderEngineeWrapper from "../../../UI_Enginee/TableHeaderEnginee/TableHeaderEngineeWrapper";
import TableHeaderEnginee, {
  parser_table_filter_data,
  table_header_state_update,
} from "../../../UI_Enginee/TableHeaderEnginee/TableHeaderEnginee";
import { marketingSliceActions } from "../../../Store/MarketingSlice/MarketingSlices";
import { useDispatch, useSelector } from "react-redux";
import UseMarketing from "../../../Hooks/UseMarketing";

const MarketingTableHeader = () => {
  const dispatch = useDispatch();

  const { filter, table } = useSelector((state) => {
    return state.marketing;
  });

  const { fetchList, fetchWithNewFilter, fetch_list } = UseMarketing();

  // State update
  useEffect(() => {
    let state = table_header_state_update(table?.header, filter?.state);
    dispatch(marketingSliceActions.updateFilterState(state?.state));
  }, []);

  const updateState = (key, value) => {
    let updatedState = {
      ...filter?.state,
      [key]: value,
    };
    dispatch(marketingSliceActions.updateFilterState(updatedState));

    fetch_list(updatedState, null);

    // let filterObj = parser_table_filter_data(
    //   salesAndMarketingTableConfig,
    //   updatedState
    // );

    // if (filterObj?.isFilter) {
    //   fetchWithNewFilter(filterObj.state);
    // } else {
    //   fetchWithNewFilter({});
    // }
  };

  // --------------------------------------------------------

  const elementRef = useRef(null);
  const [isFixed, setIsFixed] = useState(false);
  const [leftOffset, setLeftOffset] = useState(0);

  useEffect(() => {
    const container = document.getElementById("marketingtable");
    const sub_container = document.getElementById("marketingtable");

    const handleScroll = () => {
      if (elementRef.current) {
        const { top } = elementRef.current.getBoundingClientRect();
        const containerTop = container.getBoundingClientRect().top;
        setIsFixed(top <= containerTop); // If the element's top is out of view, make it fixed
      }

      const offsetLeft = -sub_container.scrollLeft;
      setLeftOffset(offsetLeft);
    };

    if (container) {
      container.addEventListener("scroll", handleScroll);
    }
    if (sub_container) {
      sub_container.addEventListener("scroll", handleScroll);
    }

    return () => {
      if (container) {
        container.removeEventListener("scroll", handleScroll);
      }
      if (sub_container) {
        sub_container.removeEventListener("scroll", handleScroll);
      }
    };
  }, []);
  // --------------------------------------------------------

  return (
    <>
      <div ref={elementRef} className=" w-full h-[1px] border-2"></div>
      <div
        style={{
          top: isFixed ? "0px" : "auto",
          left: isFixed ? `${leftOffset}px` : "auto",
        }}
        className={`w-fit h-[52px] bg-white z-[2000] border-2 flex flex-row ${
          isFixed ? "  absolute top-0 " : "  static"
        }`}
      >
        <div className=" w-[60px] h-full  flex flex-row justify-center items-center"></div>

        <TableHeaderEnginee
          table={table?.header}
          state={filter?.state}
          updateState={updateState}
        />
        <div className=" w-[60px] h-full flex flex-row justify-center items-center"></div>
      </div>
    </>
  );
};

export default MarketingTableHeader;
