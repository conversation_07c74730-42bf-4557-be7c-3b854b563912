import React from "react";
import UseJsObj2CSV from "../../../Hooks/UseJsObj2CSV";
import { FaDownload } from "react-icons/fa6";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";

const MarketingTableTopper = ({ state = "", setState = () => {} }) => {
  const { downloadMarketingDashboard } = UseJsObj2CSV();
  return (
    <div className=" w-[95vw] h-fit flex flex-row justify-between ps-4 pe-6">
      <div>Marketing List</div>

      <div className=" w-fit h-full flex flex-row items-center  gap-[0.8rem]   ">
        <div className=" w-[200px] flex flex-col items-center relative">
          <input
            value={state}
            type={"text"}
            placeholder={"Search"}
            onChange={(event) => {
              setState(event.target.value);
            }}
            className=" w-full h-[38px] rounded-[6px] ps-5  border border-[#D8E1F2] text-[16px] font-poppins text-[#53545C] placeholder:font-poppins placeholder:text-[16px] placeholder:font-[400] placeholder:text-[#ABAFB1]"
          />
        </div>
        <ButtonComponent
          onClick={downloadMarketingDashboard}
          className=" w-fit h-[50px] flex flex-row justify-center items-center gap-[0.25rem]"
        >
          <FaDownload />
        </ButtonComponent>
      </div>
    </div>
  );
};

export default MarketingTableTopper;
