import React, { useEffect, useState } from "react";
import MarketingTableTopper from "./MarketingTableTopper";
import MarketingTableHeader from "./MarketingTableHeader";
import MarketingTableListing from "./MarketingTableListing";
import MarketingTableFilterTab from "./MarketingTableFilterTab";

function isSubarrayPresent(str2, str1) {
  // Check if the subArrayString is a substring of fullString
  return str2
    ?.toString()
    ?.toLowerCase()
    ?.includes(str1?.toString()?.toLowerCase());
}

const MarketingTableLayout = ({ list = [] }) => {
  const [searchState, setSearchState] = useState("");

  const update = (e) => {
    setSearchState(e);
  };

  list = list?.filter((item) => {
    return isSubarrayPresent(item?.OWNER_NAME, searchState);
  });

  const [isFixed, setIsFixed] = useState(false);
  const [leftOffset, setLeftOffset] = useState(0);

  useEffect(() => {
    const sub_container = document.getElementById("marketingtable");

    const handleScroll = () => {
      const offsetLeft = sub_container.scrollLeft;
      setLeftOffset(offsetLeft);
    };

    if (sub_container) {
      sub_container.addEventListener("scroll", handleScroll);
    }

    return () => {
      if (sub_container) {
        sub_container.removeEventListener("scroll", handleScroll);
      }
    };
  }, []);
  // --------------------------------------------------------

  return (
    <>
      <>
        <div className=" w-[2500px] overflow-hidden h-[100px] relative ">
          <div
            style={{
              left: `${leftOffset}px`,
            }}
            className=" w-screen absolute   "
          >
            <MarketingTableTopper state={searchState} setState={update} />
            <MarketingTableFilterTab />
          </div>
        </div>

        <div id={"marketingtablexaxis"} className="">
          <MarketingTableHeader />
          <MarketingTableListing list={list} />
        </div>
      </>
      {/* <div className=" flex flex-col flex-shrink-0 md:hidden">
        <div className=" w-full h-fit flex flex-col overflow-x-auto">
          <MarketingTableTopper state={searchState} setState={update} />
          <MarketingTableFilterTab />
          <div className=" w-full h-fit  flex flex-col ">
            <MarketingTableHeader />
            <MarketingTableListing list={list} />
          </div>
        </div>
      </div> */}
    </>
  );
};

export default MarketingTableLayout;
