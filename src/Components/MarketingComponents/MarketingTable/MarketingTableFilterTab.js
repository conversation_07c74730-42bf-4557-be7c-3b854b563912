import React from "react";
import { marketingSliceActions } from "../../../Store/MarketingSlice/MarketingSlices";
import UseMarketing from "../../../Hooks/UseMarketing";
import { useDispatch, useSelector } from "react-redux";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";

const Selected = ({ text = "All", onClick }) => {
  return (
    <ButtonComponent
      onClick={onClick}
      className=" w-fit h-[48px] border-b-[4px] border-[#019BA2] ps-6 pe-6 font-poppins text-[16px] text-[#019BA2] font-[400]"
    >
      {text}
    </ButtonComponent>
  );
};

const Upselected = ({ text = "", onClick }) => {
  return (
    <ButtonComponent
      onClick={onClick}
      className=" w-fit h-[48px] border-b-[4px]  border-opacity-0 ps-6 pe-6 font-poppins text-[16px]  font-[400]"
    >
      {text}
    </ButtonComponent>
  );
};

// Helper Function
const isAll = (content = []) => {
  // If no status and more than one status are selected, then all should light up
  if (content?.length <= 0) {
    return true;
  }
  return false;
};

const isCompleted = (content = [], id) => {
  // If only completed status is picked,
  if (content?.length == 1 && content[0]?.id === id) {
    return true;
  }
  return false;
};

const MarketingTableFilterTab = () => {
  const dispatch = useDispatch();

  const { filter } = useSelector((state) => {
    return state.marketing;
  });
  const { fetch_list } = UseMarketing();

  const updateState = (key, value) => {
    let updatedState = {
      ...filter?.state,
      [key]: value,
    };
    dispatch(marketingSliceActions.updateFilterState(updatedState));

    fetch_list(updatedState, null);
  };
  return (
    <div className="min-w-full  w-full overflow-auto flex flex-row justify-between ps-6 pe-6 ">
      <div className=" w-fit flex flex-row ">
        {isAll(filter?.state?.LEAD_STATUS) ? (
          <Selected onClick={() => {}} text="All" />
        ) : (
          <Upselected
            onClick={() => {
              updateState("LEAD_STATUS", []);
            }}
            text="All"
          />
        )}

        {isCompleted(filter?.state.LEAD_STATUS, "NEW LEAD") ? (
          <Selected onClick={() => {}} text="NEW LEAD" />
        ) : (
          <Upselected
            onClick={() => {
              updateState("LEAD_STATUS", [
                { id: "NEW LEAD", content: "NEW LEAD" },
              ]);
            }}
            text="NEW LEAD"
          />
        )}
        {isCompleted(filter?.state.LEAD_STATUS, "QUOTE SENT") ? (
          <Selected onClick={() => {}} text="QUOTE SENT" />
        ) : (
          <Upselected
            onClick={() => {
              updateState("LEAD_STATUS", [
                { id: "QUOTE SENT", content: "QUOTE SENT" },
              ]);
            }}
            text="QUOTE SENT"
          />
        )}
        {isCompleted(filter?.state.LEAD_STATUS, "IN FOLLOW") ? (
          <Selected onClick={() => {}} text="IN FOLLOW" />
        ) : (
          <Upselected
            onClick={() => {
              updateState("LEAD_STATUS", [
                { id: "IN FOLLOW", content: "IN FOLLOW" },
              ]);
            }}
            text="IN FOLLOW"
          />
        )}
        {isCompleted(filter?.state.LEAD_STATUS, "DROPPED") ? (
          <Selected onClick={() => {}} text="DROPPED" />
        ) : (
          <Upselected
            onClick={() => {
              updateState("LEAD_STATUS", [
                { id: "DROPPED", content: "DROPPED" },
              ]);
            }}
            text="DROPPED"
          />
        )}
        {isCompleted(filter?.state.LEAD_STATUS, "ORDER CONFIRM") ? (
          <Selected onClick={() => {}} text="ORDER CONFIRM" />
        ) : (
          <Upselected
            onClick={() => {
              updateState("LEAD_STATUS", [
                { id: "ORDER CONFIRM", content: "ORDER CONFIRM" },
              ]);
            }}
            text="ORDER CONFIRM"
          />
        )}
        {/* {isCompleted(filter?.state.LEAD_STATUS, "WALK IN") ? (
          <Selected onClick={() => {}} text="WALK IN" />
        ) : (
          <Upselected
            onClick={() => {
              updateState("LEAD_STATUS", [
                { id: "WALK IN", content: "WALK IN" },
              ]);
            }}
            text="WALK IN"
          />
        )}
        {isCompleted(filter?.state.LEAD_STATUS, "FIELD VISIT") ? (
          <Selected onClick={() => {}} text="FIELD VISIT" />
        ) : (
          <Upselected
            onClick={() => {
              updateState("LEAD_STATUS", [
                { id: "FIELD VISIT", content: "FIELD VISIT" },
              ]);
            }}
            text="FIELD VISIT"
          />
        )} */}
      </div>
    </div>
  );
};

export default MarketingTableFilterTab;
