import React, { useState } from "react";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import { salesAndMarketingTableConfig } from "../../../Config/SalesAndMarketingTableConfig";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import UseUserData from "../../../Hooks/UseUserData";
import TableTextContent from "../../Utils/TableStructures/TableTextContent";
import edit from "../../../Assest/Utils/edit.png";
import {
  SalesAndMarketingSliceActions,
} from "../../../Store/SalesAndMarketingSlice/SalesAndMarketingSlice";
import { fetch_marketing_edit_form_enginee } from "../../../Hooks/UseMarketing";
import { formEnginee_edit_Formatee_Structure_and_State } from "../../../Dashboard_API_calling_and_formatting/Form_Formate_and_API";
import { routes } from "../../../Config/routesConfig";
import TableList_array_template from "../../../UI_templates/TableList_array_template";
import UseRouterAuthentication from "../../../Hooks/UseRouterAuthentication";
import TableTextAreaTextModelDisplay from "../../../UI_Enginee/TableListEnginee/TableTextAreaTextModelDisplay";
import { returnUserNameWIthID_tickets } from "../../../Config/TicketingConfig";
import MarketingTableExpend from "./MarketingTableExpend";

const dateFormatFunction = (dt) => {
  let date = new Date(dt);

  let fullyear = date.getFullYear();
  let month =
    date.getMonth() + 1 < 10
      ? "0" + (date.getMonth() + 1)
      : date.getMonth() + 1;

  let dateNO = date.getDate();

  let format = fullyear + "-" + month + "-" + dateNO;

  return format;
};

const getAssigneeString = (array, userlist) => {
  let arrayf = [];
  for (let i = 0; i < array?.length; i++) {
    arrayf.push(returnUserNameWIthID_tickets(array[i], userlist));
  }

  return arrayf;
};

const MarketingTableIndivRow = ({ content }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { routeAuthentication } = UseRouterAuthentication();

  const { getUsernameWithid, userList } = UseUserData();

  let lead_sales_person = getAssigneeString(
    content?.LEAD_SALES_PERSON,
    userList
  );

  let visited_by = getAssigneeString(content?.VISITED_BY, userList);

  const [editLoading, setEditLoading] = useState(false);

  const editClickHandler = async () => {
    setEditLoading(true);
    try {
      let result = await fetch_marketing_edit_form_enginee(content?._id);

      // Debug logs to see what's coming from the backend
      console.log("Edit form result:", result);
      console.log("Result structure:", result?.structure);

      if (!result?.structure || !Array.isArray(result.structure) || result.structure.length === 0) {
        console.error("No valid structure returned from API");
        alert("Could not load the edit form. Please try again later.");
        setEditLoading(false);
        return;
      }

      // Extract the structure from the result before passing it to formEnginee_edit_Formatee_Structure_and_State
      // The function expects an array, but result is an object with a structure property
      let ss = formEnginee_edit_Formatee_Structure_and_State(result.structure);

      console.log("Formatted structure and state:", ss);

      dispatch(
        SalesAndMarketingSliceActions.updateEditStructureAndState({
          ...ss,
          id: content?._id,
        })
      );

      navigate(
        "?page=" +
          routes?.salesAndMarketingEditLead?.searchParams +
          "&id=" +
          content?._id
      );
    } catch (error) {
      console.error("Error in editClickHandler:", error);
      alert("An error occurred while loading the edit form. Please try again later.");
    } finally {
      setEditLoading(false);
    }
  };

  const deleteClickHandler = () => {};

  //----------------------------------------------------------------------------------------------------------------

  const [isLoading, setIsLoading] = useState(false);
  const auth = useSelector((state) => {
    return state.master.auth;
  });
  const { filter } = useSelector((state) => {
    return state.salesAndMarketing;
  });

  const updateLeadStatus = async (e) => {};

  //---------------------------------------------------------------------------------------------------------------

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [onHover, setOnHover] = useState(false);

  return (
    <div
      className=" w-fit h-[52px] border-b-2 flex flex-row hover:bg-[#F0FEFF]  "
      key={content?._id}
      onMouseEnter={() => {
        setOnHover(true);
      }}
      onMouseLeave={() => {
        setOnHover(false);
      }}
    >
      <div className=" w-[60px] h-full flex flex-row justify-center items-center">
        {routeAuthentication("marketing", "edit") && (
          <ButtonComponent
            onClick={editClickHandler}
            isLoading={editLoading}
            className=" w-[24px] h-[24px] flex flex-rwo justify-center items-center "
          >
            <img
              src={edit}
              className=" w-full h-full object-contain"
              alt="edit"
            />
          </ButtonComponent>
        )}
      </div>

      <TableTextContent
        size={salesAndMarketingTableConfig[0]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.OWNER_NAME}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[1]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.CONTACT_NO_1}
      </TableTextContent>
      {/* <TableTextContent
        size={salesAndMarketingTableConfig[2]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.CONTACT_NO_2}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[3]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.CONTACT_NO_3}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[4]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.CONTACT_NO_4}
      </TableTextContent> */}
      <TableTextContent
        size={salesAndMarketingTableConfig[2]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {dateFormatFunction(content?.DATE_OF_VISIT)}
      </TableTextContent>
      <TableList_array_template
        list={visited_by}
        className={salesAndMarketingTableConfig[3]?.className}
      />
      {/* <TableTextContent
        size={salesAndMarketingTableConfig[6]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {getUsernameWithid()}
      </TableTextContent> */}
      <TableTextContent
        size={salesAndMarketingTableConfig[4]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.LEAD_STATUS}
      </TableTextContent>
      <TableList_array_template
        list={content?.LEAD_TO_COMPANY}
        className={salesAndMarketingTableConfig[5]?.className}
      />
      <TableList_array_template
        list={lead_sales_person}
        className={salesAndMarketingTableConfig[6]?.className}
      />
      {/* <TableTextContent
        size={salesAndMarketingTableConfig[9]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {getUsernameWithid(content?.LEAD_SALES_PERSON)}
      </TableTextContent> */}
      <TableTextContent
        size={salesAndMarketingTableConfig[7]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.LEAD_TYPE}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[8]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.CITY}
      </TableTextContent>
      {/* <TableTextContent
        size={salesAndMarketingTableConfig[12]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.AREA}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[13]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.LANDMARK}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[14]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.engineer_name}
      </TableTextContent> */}
      {/* <TableTextContent
        size={salesAndMarketingTableConfig[15]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.engineer_number}
      </TableTextContent> */}
      {/* <TableTextContent
        size={salesAndMarketingTableConfig[15]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.ARCH_FIRM_NAME}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[16]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.ARCH_NAME}
      </TableTextContent> */}
      {/* <TableTextContent
        size={salesAndMarketingTableConfig[18]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.ARCH_NUMBER}
      </TableTextContent> */}
      {/* <TableTextContent
        size={salesAndMarketingTableConfig[17]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.PLUMBER_NAME}
      </TableTextContent> */}
      {/* <TableTextContent
        size={salesAndMarketingTableConfig[20]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.PLUMBER_NUMBER}
      </TableTextContent> */}
      {/* <TableTextContent
        size={salesAndMarketingTableConfig[18]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.KEY_PERSON_NAME}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[19]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.KEY_PERSON_NUMBER}
      </TableTextContent> */}
      <TableList_array_template
        list={content?.BRAND_TO_PROMOTE}
        className={salesAndMarketingTableConfig[9]?.className}
      />
      {/* <TableTextContent
        size={salesAndMarketingTableConfig[21]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.No_of_Bathrooms}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[22]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.Tiles_Requirement}
      </TableTextContent> */}
      <TableTextAreaTextModelDisplay
        title="Lead Person Remarks"
        size={salesAndMarketingTableConfig[10]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.LEAD_PERSON_REMARKS}
      </TableTextAreaTextModelDisplay>
      <TableTextContent
        size={salesAndMarketingTableConfig[11]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.FOLLOW_UP_DATE_1}
      </TableTextContent>

      <TableTextContent
        size={salesAndMarketingTableConfig[12]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.unit}
      </TableTextContent>

      {/* <TableTextContent
        size={salesAndMarketingTableConfig[25]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.FOLLOW_UP_DATE_2}
      </TableTextContent> */}
      {/* <TableTextContent
        size={salesAndMarketingTableConfig[26]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.TELECALLING_REMARKS}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[27]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.RE_VISIT_DATE_1}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[28]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.RE_VISIT_DATE_2}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[29]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.RE_VISIT_DATE_3}
      </TableTextContent> */}
      {/* <TableTextContent
        size={salesAndMarketingTableConfig[30]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.RE_VISIT_DATE_4}
      </TableTextContent> */}
      <div className=" w-[60px] h-full flex flex-row justify-center items-center">
        {onHover && (
          <ButtonComponent
            onClick={() => {
              setIsModalOpen(true);
            }}
            className=" w-fit h-[24px] font-poppins bg-gray-300 ps-1 rounded-[10px] pe-1 flex flex-roo justify-center items-center "
          >
            Expand
          </ButtonComponent>
        )}
      </div>
      {isModalOpen && (
        <MarketingTableExpend
          title={content?.OWNER_NAME}
          content={content}
          closeModal={() => {
            setOnHover(false);
            setIsModalOpen(false);
          }}
          editFunction={() => {
            editClickHandler();
            setOnHover(false);
            setIsModalOpen(false);
          }}
        />
      )}
      {/* <div className=" w-[60px] h-full flex flex-row justify-center items-center">
                      <ButtonComponent
                           onClick={deleteClickHandler}
                           className=" w-[24px] h-[24px] flex flex-rwo justify-center items-center "
                      >
                           <img
                                src={deletething}
                                className=" w-full h-full object-contain"
                                alt="edit"
                           />
                      </ButtonComponent>
                 </div> */}
    </div>
  );
};

export default MarketingTableIndivRow;
