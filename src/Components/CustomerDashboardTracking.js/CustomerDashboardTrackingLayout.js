import React from "react";
import TrackingHeader_Template from "../../UI_templates/UI_template_tracking/TrackingHeader_Template";
import { useSearchParams } from "react-router-dom";
import { routes } from "../../Config/routesConfig";
import { useSelector } from "react-redux";
import TrackingOrderSummary_template from "../../UI_templates/UI_template_tracking/TrackingOrderSummary_template";
import TrackingDivisor_Template from "../../UI_templates/UI_template_tracking/TrackingDivisor_Template";
import CustomerDashboardTrackingSections from "./CustomerDashboardTrackingSections";

const CustomerDashboardTrackingLayout = ({ content = {} }) => {
  // Header Data
  let [parmas] = useSearchParams();
  let billId = parmas.get(routes?.customerDashboardTracking?.searchParams);
  const { auth } = useSelector((state) => {
    return state.master;
  });
  let headerData = { billId: billId, customerId: auth?.id };
  return (
    <div className=" w-full h-fit ps-6 pe-6  flex flex-col gap-[2rem]">
      <TrackingHeader_Template content={headerData} />
      <TrackingDivisor_Template />

      {content?.orders.map((item, index) => {
        return (
          <>
            <CustomerDashboardTrackingSections content={item} index={index} />
            <TrackingDivisor_Template />
          </>
        );
      })}

      <TrackingOrderSummary_template
        content={{
          totalList: content?.orderSummary,
          total: content?.orderTotal,
        }}
      />
    </div>
  );
};

export default CustomerDashboardTrackingLayout;
