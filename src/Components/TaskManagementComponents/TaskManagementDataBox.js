import React, { useState } from "react";

// UI Components
import SelectComponent from "../../BasicUIElements/SelectComponent";
import IndivIdualDataFieldLayout from "../Utils/DataStuctureBoxes/IndivIdualDataFieldLayout";

const TaskManagementDataBox = () => {
     const [state, setState] = useState({
          id: "",
          content: "01-Jan-2023 - 30-Jan-2024",
     });
     return (
          <div className=" w-full h-fit flex flex-row flex-nowrap overflow-x-auto md:p-3 ps-8 pe-8 pb-[2rem]">
               <div className=" lg:w-[735px] md:w-[600px] w-[450px] h-[145px] flex flex-col pe-3 flex-shrink-0 justify-center border-2 ps-3 rounded-[10px] ">
                    <div className=" w-full h-[50px] flex flex-row justify-end">
                         <div>
                              <SelectComponent
                                   value={state}
                                   updateFunction={setState}
                                   //    optionarr={opt}
                                   className=" w-[120px] h-[35px] text-[#BEC0CA]"
                              />
                         </div>
                    </div>
                    <div className=" w-full h-[60px] flex flex-row justify-between ">
                         <IndivIdualDataFieldLayout
                              heading="Total Tasks"
                              stat={"100"}
                         />
                         <IndivIdualDataFieldLayout
                              heading="Open Task"
                              stat={"100"}
                              statColor="black"
                         />

                         <IndivIdualDataFieldLayout
                              heading="Over due"
                              stat={"100"}
                              statColor="black"
                         />
                         <IndivIdualDataFieldLayout
                              heading="Completed"
                              stat={"100"}
                              statColor="black"
                         />

                         <div></div>
                    </div>
               </div>
          </div>
     );
};

export default TaskManagementDataBox;
