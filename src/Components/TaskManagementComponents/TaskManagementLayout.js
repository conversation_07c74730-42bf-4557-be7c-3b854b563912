import React, { useEffect } from "react";

// Layout
import TaskManagementDataBox from "./TaskManagementDataBox";
import ButtonComponent from "../../BasicUIElements/ButtonComponent";
import TaskMangementToggleComponent from "./TaskMangementToggleComponent";
import TaskManagementTableLayout from "./TaskManagementTable/TaskManagementTableLayout";

// State management Config
import {
  fetch_list_taskmanagement,
  mode_taskManagement,
  taskManagement_add_form,
  taskmanagement_filter_middleware,
} from "../../Store/TaskManagement/taskManagementSlice";

// Hooks
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { routes } from "../../Config/routesConfig";
import TaskManagementGridLayout from "./TaskManagementGrid/TaskManagementGridLayout";
import { fetch_Grid_list_taskmanagement } from "../../Store/TaskManagement/TaskmanagementGridSlice";
import UseRouterAuthentication from "../../Hooks/UseRouterAuthentication";

const TaskManagementLayout = () => {
  const navigate = useNavigate();
  const { ui, list } = useSelector((state) => {
    return state.taskManagement;
  });

  const { auth, access } = useSelector((state) => {
    return state.master;
  });

  const { isDashboardAccessable, routeAuthentication } =
    UseRouterAuthentication();

  const dispatch = useDispatch();
  useEffect(() => {
    isDashboardAccessable("taskManagement");
    dispatch(taskmanagement_filter_middleware({}));
    // dispatch(fetch_list_taskmanagement());
    dispatch(fetch_Grid_list_taskmanagement());
  }, [auth, access]);

  return (
    <div className=" w-full h-full  overflow-y-auto pt-[2rem]">
      <div className=" w-full h-fit flex flex-row justify-between md:pe-4 pe-2">
        <div></div>
        {routeAuthentication("taskManagement", "write") && (
          <ButtonComponent
            onClick={() => {
              dispatch(taskManagement_add_form(navigate));
            }}
            className=" w-[205px] h-[36px] bg-mainLinearGradient rounded-[12px] font-inter font-[700] text-[14px] text-[#fff] "
          >
            + Add a Task
          </ButtonComponent>
        )}
      </div>
      <div className=" w-full md:ps-4 md:pe-4 ps-2 pe-2  flex flex-row">
        <TaskManagementDataBox />
      </div>

      <div className=" w-full flex flex-row ps-[2rem] mt-[1.5rem] ">
        <TaskMangementToggleComponent mode={ui.mode} />
      </div>

      {mode_taskManagement?.list === ui.mode && (
        <TaskManagementTableLayout list={list} />
      )}
      {mode_taskManagement?.grid === ui.mode && <TaskManagementGridLayout />}
    </div>
  );
};

export default TaskManagementLayout;
