import React, { useEffect, useMemo, useState } from "react";

// Layout
import ModelHeaderAndRoute from "../../Utils/UtilsComponents/ModelHeaderAndRoute";

// UI
import InputComponentWithErrorMessage from "../../../BasicUIElements/InputComponentWithErrorMessage";
import SelectComponent from "../../../BasicUIElements/SelectComponent";
import MultiSelectComponent from "../../Utils/MultiSelectComponent";
import DatePickerComponents from "../../Utils/DatePickerComponents";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";

// Hooks
import { useNavigate } from "react-router-dom";
import {
  department_dropdown_taskmanagement,
  formatDate_taskManagement,
  project_dropdown_taskmanagement,
} from "../../../Config/TaskManagementConfig";

// Custom Hooks
import UseUserData from "../../../Hooks/UseUserData";
import { useDispatch, useSelector } from "react-redux";

import { API_ENDPOINTS, BaseURL, defaultHeaders } from "../../../API/APIConfig";
import { PostAPI } from "../../../API/PostAPI";
import {
  taskManagement_add_form,
  taskmanagement_filter_middleware,
  taskManagementSliceActions,
} from "../../../Store/TaskManagement/taskManagementSlice";
import { taskmanagement_grid_filter_middleware } from "../../../Store/TaskManagement/TaskmanagementGridSlice";
import FormEnginee from "../../../UI_Enginee/Form/FormEnginee";
import { formData_parser } from "../../../Dashboard_API_calling_and_formatting/Form_Formate_and_API";
import UseRouterAuthentication from "../../../Hooks/UseRouterAuthentication";
import { routes } from "../../../Config/routesConfig";
import { vaildator_Form_function } from "../../../UI_Enginee/Form/FormEngineeConfig";
import toast from "react-hot-toast";

export const default_project_taskmanagement_add = {
  id: "Project",
  content: "Project",
};

export const default_department_taskmanagement_add = {
  id: "Department",
  content: "Department",
};

const TaskManagementAddLayout = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { getUserList } = UseUserData();
  let userList = getUserList();

  const taskManagement = useSelector((state) => {
    return state?.taskManagement;
  });

  let add = taskManagement?.add;

  let list_filter = taskManagement?.filter;
  const taskManagementGrid = useSelector((state) => {
    return state?.taskManagementGrid;
  });
  let grid_filter = taskManagementGrid?.filter;

  const { routeAuthentication } = UseRouterAuthentication();
  const { auth, access } = useSelector((state) => {
    return state.master;
  });

  useEffect(() => {
    if (!routeAuthentication("networking", "write")) {
      navigate(routes?.taskManagement?.directLink);
    }
  }, [auth, access]);

  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (add.structure?.length === 0) {
      dispatch(taskManagement_add_form());
    }
  }, []);

  const updateFunctionOfForm = (key, value) => {
    let updateState = {
      ...add.state,
      [key]: { ...add.state[key], value: value },
    };
    dispatch(taskManagementSliceActions.updateAddState(updateState));
  };

  const closeModal = () => {
    navigate("..");
  };

  const submitHandler = async () => {
    let vaildate = vaildator_Form_function(add.structure, add.state);
    dispatch(taskManagementSliceActions.updateAddState(vaildate?.validate_Obj));

    if (!vaildate?.isFormValid) {
      toast.error("Fill all mandatory fields to add the task management", {
        position: "top-right",
      });
      return;
    }

    let parser_obj = formData_parser(add.structure, vaildate?.validate_Obj);
    let toast_id = toast.loading("Creating", { position: "top-right" });
    let obj = {
      ...parser_obj.parser_obj,
      Status: "Open",
      Assigned_By: auth.id,
      createdAt: new Date(),
    };

    setIsLoading(true);
    let response = await create_taskmanagement_function(obj, auth);
    let responsedata = await response.json();
    if (response?.ok) {
      closeModal();
      toast.success("Created", { id: toast_id });
    } else {
      toast.error(responsedata?.error, { id: toast_id });
    }
    setIsLoading(false);
  };

  return (
    <div className=" md:w-[424px] w-[340px] max-h-[95vh] h-fit overflow-y-auto bg-[#fff] rounded-[6px] p-4 pb-[2rem] flex flex-col gap-[1rem] ">
      <ModelHeaderAndRoute
        className="w-full h-fit flex flex-row justify-between ps-4 pe-4 "
        title="Create a Task"
        onClick={() => {
          closeModal();
        }}
      />

      <FormEnginee
        form={add?.structure}
        formState={add.state}
        formStateFunction={updateFunctionOfForm}
      />
      <div className=" w-full h-fit ">
        <div className=" w-full h-fit flex flex-row justify-center gap-[1rem]">
          <ButtonComponent
            onClick={() => {
              closeModal();
            }}
            className=" w-[45%] h-[58px] rounded-[6px] border-[#00BD94] border-2 bg-clip-text text-transparent bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] flex flex-row justify-center items-center"
          >
            Cancel
          </ButtonComponent>
          <ButtonComponent
            onClick={submitHandler}
            isLoading={isLoading}
            className=" w-[45%] h-[58px] rounded-[6px] bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] text-[#fff] flex flex-row justify-center items-center"
          >
            Create
          </ButtonComponent>
        </div>
      </div>
    </div>
  );
};

//   // STATE
//   const [assignee, setAssignee] = useState([]);
//   const [taskDescription, setTaskDescription] = useState("");
//   const [project, setProject] = useState(default_project_taskmanagement_add);
//   const [department, setDepartment] = useState(
//     default_department_taskmanagement_add
//   );
//   const [dueDate, setDueDate] = useState(new Date());

//   const [isLoading, setIsLoading] = useState(false);

//   const createClickHandler = async () => {
//     // dispatch(
//     //      create_taskmanagement_thunk(
//     //           content,
//     //           auth,
//     //           list_filter,
//     //           grid_filter,
//     //           navigate
//     //      )
//     // );

//     if (
//       taskDescription === "" ||
//       project?.id === default_project_taskmanagement_add?.id ||
//       department?.id === default_department_taskmanagement_add?.id ||
//       assignee?.length === 0
//     ) {
//       return;
//     }

//     let body = {
//       Task_Description: taskDescription,
//       Project: project.id,
//       Department: department.id,
//       Assignee: assignee.map((item) => item.id),
//       Assigned_By: auth.id,
//       Dead_line_date: formatDate_taskManagement(new Date(dueDate)),
//       Status: "Open",
//       createdAt: new Date(),
//     };

//     setIsLoading(true);

//     let response = await create_taskmanagement_function(body, auth);

//     if (response?.ok) {
//       dispatch(taskmanagement_filter_middleware(list_filter));
//       dispatch(taskmanagement_grid_filter_middleware(grid_filter));
//       navigate("..");
//     }

//     setIsLoading(false);
//   };

//   return (
//     <div className=" md:w-[424px] w-[340px] max-h-[95vh] h-fit overflow-y-auto bg-[#fff] rounded-[6px] p-4 pb-[2rem]  ">
//       <ModelHeaderAndRoute
//         className="w-full h-fit flex flex-row justify-between ps-4 pe-4 "
//         title="Create a Task"
//         onClick={() => {
//           closeModal();
//         }}
//       />
//       <div className=" w-full h-fit flex flex-col gap-[1.2rem] mt-[1.5rem]">
//         <div className=" w-full h-fit flex flex-col">
//           <h1 className=" text-[12px] font-inter  font-[400] text-[#5E6366] md:ps-4 ps-2 ">
//             Task Description
//           </h1>
//           <div className=" w-full h-fit flex flex-row justify-center ">
//             <InputComponentWithErrorMessage
//               type="text"
//               name="Task Description"
//               placeholder="Task Description"
//               errorMessage=""
//               value={taskDescription}
//               onChange={(e) => {
//                 setTaskDescription(e.target.value);
//               }}
//               onBlur={() => {}}
//               isValid={true}
//               isTouched={false}
//               className="md:w-[375px] w-[300px] h-[52px] ps-5  border border-[#D8E1F2] rounded-[6px] text-[16px] text-[#53545C] font-semibold placeholder:font-poppins placeholder:text-[16px] placeholder:font-[400] placeholder:text-[#ABAFB1] "
//             />
//           </div>
//         </div>
//         <div className=" w-full h-fit flex flex-row justify-center ">
//           <MultiSelectComponent
//             className={
//               "md:w-[375px] w-[300px] min-h-[52px] h-fit border-2 rounded-[8px] flex flex-row justify-between items-center ps-4 pe-4 relative"
//             }
//             optionarr={userList}
//             value={assignee}
//             updatedFunction={(e) => {
//               setAssignee(e);
//             }}
//           />
//         </div>
//         <div className=" w-full h-fit flex flex-row justify-center ">
//           <SelectComponent
//             className=" md:w-[375px] w-[300px] h-[52px] flex flex-row justify-between rounded-[8px] ps-5  text-[#53545C]"
//             textClassName="text-[16px] font-montserrat font-semibold text-[#53545C]"
//             optionarr={department_dropdown_taskmanagement}
//             value={department}
//             updateFunction={(e) => {
//               setDepartment(e);
//             }}
//           />
//         </div>
//         <div className=" w-full h-fit flex flex-row justify-center ">
//           <SelectComponent
//             className=" md:w-[375px] w-[300px] h-[52px] flex flex-row justify-between rounded-[8px] ps-5  text-[#53545C]"
//             textClassName="text-[16px] font-montserrat font-semibold text-[#53545C]"
//             optionarr={project_dropdown_taskmanagement}
//             value={project}
//             updateFunction={(e) => {
//               setProject(e);
//             }}
//           />
//         </div>

//         <div className=" w-full h-fit flex flex-row justify-center ">
//           <DatePickerComponents
//             title="Due Date"
//             className=" relative md:w-[375px] w-[300px] h-[52px] rounded-[8px] bg-[#F6F7FB] flex flex-row justify-start gap-[1rem]"
//             value={dueDate}
//             onChange={(e) => {
//               setDueDate(e.target.value);
//             }}
//           />
//         </div>
//         <div className=" w-full h-fit ">
//           <div className=" w-full h-fit flex flex-row justify-center gap-[1rem]">
//             <ButtonComponent
//               onClick={() => {
//                 closeModal();
//               }}
//               className=" w-[45%] h-[58px] rounded-[6px] border-[#00BD94] border-2 bg-clip-text text-transparent bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] flex flex-row justify-center items-center"
//             >
//               Cancel
//             </ButtonComponent>
//             <ButtonComponent
//               onClick={createClickHandler}
//               isLoading={isLoading}
//               className=" w-[45%] h-[58px] rounded-[6px] bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] text-[#fff] flex flex-row justify-center items-center"
//             >
//               Create
//             </ButtonComponent>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

export default TaskManagementAddLayout;

const create_taskmanagement_function = async (content, auth) => {
  let url = new URL(BaseURL.mainURl + API_ENDPOINTS.taskManagementCreate);

  let body = content;

  const headers = {
    ...defaultHeaders,
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": ["POST", "GET", "OPTIONS", "DELETE", "PUT"],
    "Access-Control-Allow-Headers": [
      "append",
      "delete",
      "entries",
      "foreach",
      "get",
      "has",
      "keys",
      "set",
      "values",
      "Authorization",
    ],

    redirect: "follow",

    Authorization: auth.token,
  };

  const response = await PostAPI(url, body, headers);

  return response;
};
