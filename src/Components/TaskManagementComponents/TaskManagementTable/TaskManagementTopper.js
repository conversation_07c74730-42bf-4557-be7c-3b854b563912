import React from "react";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import { useDispatch, useSelector } from "react-redux";
import {
  taskmanagement_filter_middleware,
  taskManagementSliceActions,
} from "../../../Store/TaskManagement/taskManagementSlice";
import {
  taskmanagement_completed_status,
  taskmanagement_opentask_status,
  taskmanagement_overdue_status,
} from "../../../Config/TaskManagementConfig";
import { FaDownload } from "react-icons/fa6";
import UseJsObj2CSV from "../../../Hooks/UseJsObj2CSV";

const Selected = ({ text = "All", onClick }) => {
  return (
    <ButtonComponent
      onClick={onClick}
      className=" w-fit h-[48px] border-b-[4px] border-[#019BA2] ps-6 pe-6 font-poppins text-[16px] text-[#019BA2] font-[400]"
    >
      {text}
    </ButtonComponent>
  );
};

const Upselected = ({ text = "", onClick }) => {
  return (
    <ButtonComponent
      onClick={onClick}
      className=" w-fit h-[48px] border-b-[4px]  border-opacity-0 ps-6 pe-6 font-poppins text-[16px]  font-[400]"
    >
      {text}
    </ButtonComponent>
  );
};

// Helper Function
const isAll = (content = []) => {
  // If no status and more than one status are selected, then all should light up
  if (content?.length == 0 || content?.length > 1) {
    return true;
  }
  return false;
};

const isCompleted = (content = []) => {
  // If only completed status is picked,
  if (
    content?.length == 1 &&
    content[0]?.id === taskmanagement_completed_status.id
  ) {
    return true;
  }
  return false;
};

const isOpen = (content = []) => {
  // If only open status is picked,
  if (
    content?.length == 1 &&
    content[0]?.id === taskmanagement_opentask_status.id
  ) {
    return true;
  }
  return false;
};

const isOverDue = (content) => {
  // If only overdue status is picked,
  if (
    content?.length == 1 &&
    content[0]?.id === taskmanagement_overdue_status.id
  ) {
    return true;
  }
  return false;
};

const TaskManagementTopper = ({ state = "", setState = () => {} }) => {
  const { downloadTaskmanagementDashboard } = UseJsObj2CSV();
  const dispatch = useDispatch();
  const { filter } = useSelector((state) => {
    return state?.taskManagement;
  });

  const updateFilter = (obj) => {
    dispatch(taskManagementSliceActions.updateFilterStatus(obj));
    dispatch(taskmanagement_filter_middleware({ ...filter, status: obj }));
  };

  return (
    <div className="min-w-full w-fit flex flex-row justify-between ps-6 pe-6 ">
      <div className=" w-fit flex flex-row ">
        {isAll(filter.status) ? (
          <Selected onClick={() => {}} text="All" />
        ) : (
          <Upselected
            onClick={() => {
              updateFilter([]);
            }}
            text="All"
          />
        )}

        {isCompleted(filter.status) ? (
          <Selected onClick={() => {}} text="Completed" />
        ) : (
          <Upselected
            onClick={() => {
              updateFilter([taskmanagement_completed_status]);
            }}
            text="Completed"
          />
        )}

        {isOpen(filter.status) ? (
          <Selected onClick={() => {}} text="Open" />
        ) : (
          <Upselected
            onClick={() => {
              updateFilter([taskmanagement_opentask_status]);
            }}
            text="Open"
          />
        )}

        {isOverDue(filter?.status) ? (
          <Selected onClick={() => {}} text="Opendue" />
        ) : (
          <Upselected
            onClick={() => {
              updateFilter([taskmanagement_overdue_status]);
            }}
            text="Opendue"
          />
        )}
      </div>

      <div className=" w-fit h-full flex flex-row items-center  gap-[0.8rem]   ">
        <div className=" w-[200px] flex flex-col items-center relative">
          <input
            value={state}
            type={"text"}
            placeholder={"Search"}
            onChange={(event) => {
              setState(event.target.value);
            }}
            className=" w-full h-[38px] rounded-[6px] ps-5  border border-[#D8E1F2] text-[16px] font-poppins text-[#53545C] placeholder:font-poppins placeholder:text-[16px] placeholder:font-[400] placeholder:text-[#ABAFB1]"
          />
        </div>
        <ButtonComponent
          onClick={downloadTaskmanagementDashboard}
          className=" w-fit h-[50px] flex flex-row justify-center items-center gap-[0.25rem]"
        >
          <FaDownload />
        </ButtonComponent>
      </div>
    </div>
  );
};

export default TaskManagementTopper;
