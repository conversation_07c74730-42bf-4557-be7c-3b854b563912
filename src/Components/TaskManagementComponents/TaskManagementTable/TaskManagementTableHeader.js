import React from "react";

// Layout
import TableIndivHeader from "../../Utils/TableStructures/TableIndivHeader";
import TableFilterSelectorComponent from "../../Utils/TableStructures/TableFilterSelectorComponent";

// Config
import {
  department_dropdown_taskmanagement,
  project_dropdown_taskmanagement,
  status_dropdown_taskmanagement,
  taskManagementTableConfig,
} from "../../../Config/TaskManagementConfig";

// Slices
import {
  taskmanagement_filter_middleware,
  taskManagementSliceActions,
} from "../../../Store/TaskManagement/taskManagementSlice";

// Hooks
import { useDispatch, useSelector } from "react-redux";
import UseUserData from "../../../Hooks/UseUserData";

const TaskManagementTableHeader = () => {
  const { getUserList } = UseUserData();
  const dispatch = useDispatch();
  const { filter } = useSelector((state) => {
    return state.taskManagement;
  });
  return (
    <div className=" w-fit h-[52px] border-b-2 flex flex-row  pe-8 ps-[2rem] ">
      <TableIndivHeader className={taskManagementTableConfig[0].className}>
        {taskManagementTableConfig[0].title}
      </TableIndivHeader>
      <TableIndivHeader className={taskManagementTableConfig[1].className}>
        <TableFilterSelectorComponent
          className="  relative w-[180px] h-[40px] border-2 flex flex-row justify-between items-center ps-2 pe-2"
          title={taskManagementTableConfig[1].title}
          optionarr={department_dropdown_taskmanagement}
          valueArray={filter?.department}
          updateFunction={(c, isTick) => {
            let array = [];
            if (isTick) {
              array = filter.department.filter((state) => state.id !== c.id);
            } else {
              array = [...filter.department, c];
            }
            dispatch(taskManagementSliceActions.updateFilterDepartment(array));
            dispatch(
              taskmanagement_filter_middleware({
                ...filter,
                department: array,
              })
            );
          }}
        />
      </TableIndivHeader>
      <TableIndivHeader className={taskManagementTableConfig[2].className}>
        <TableFilterSelectorComponent
          className="  relative w-[150px] h-[40px] border-2 flex flex-row justify-between items-center ps-2 pe-2"
          title={taskManagementTableConfig[2].title}
          optionarr={project_dropdown_taskmanagement}
          valueArray={filter?.project}
          updateFunction={(c, isTick) => {
            let array = [];
            if (isTick) {
              array = filter.project.filter((state) => state.id !== c.id);
            } else {
              array = [...filter.project, c];
            }

            dispatch(taskManagementSliceActions.updateFilterProject(array));
            dispatch(
              taskmanagement_filter_middleware({
                ...filter,
                project: array,
              })
            );
          }}
        />
      </TableIndivHeader>
      <TableIndivHeader className={taskManagementTableConfig[3].className}>
        <TableFilterSelectorComponent
          className="  relative w-[150px] h-[40px] border-2 flex flex-row justify-between items-center ps-2 pe-2"
          title={taskManagementTableConfig[3].title}
          optionarr={getUserList()}
          valueArray={filter?.assingee}
          updateFunction={(c, isTick) => {
            let array = [];
            if (isTick) {
              array = filter.assingee.filter((state) => state.id !== c.id);
            } else {
              array = [...filter.assingee, c];
            }

            dispatch(taskManagementSliceActions.updateFilterAssingee(array));
            dispatch(
              taskmanagement_filter_middleware({
                ...filter,
                assingee: array,
              })
            );
          }}
        />
      </TableIndivHeader>
      <TableIndivHeader className={taskManagementTableConfig[4].className}>
        {taskManagementTableConfig[4].title}
      </TableIndivHeader>
      <TableIndivHeader className={taskManagementTableConfig[5].className}>
        {taskManagementTableConfig[5].title}
      </TableIndivHeader>
      <TableIndivHeader className={taskManagementTableConfig[6].className}>
        <TableFilterSelectorComponent
          className="  relative w-[150px] h-[40px] border-2 flex flex-row justify-between items-center ps-2 pe-2"
          title={taskManagementTableConfig[6].title}
          optionarr={status_dropdown_taskmanagement}
          valueArray={filter?.status}
          updateFunction={(c, isTick) => {
            let array = [];
            if (isTick) {
              array = filter.status.filter((state) => state.id !== c.id);
            } else {
              array = [...filter.status, c];
            }

            dispatch(taskManagementSliceActions.updateFilterStatus(array));
            dispatch(
              taskmanagement_filter_middleware({
                ...filter,
                status: array,
              })
            );
          }}
        />
      </TableIndivHeader>
      <TableIndivHeader className={taskManagementTableConfig[7].className}>
        {taskManagementTableConfig[7].title}
      </TableIndivHeader>
    </div>
  );
};

export default TaskManagementTableHeader;
