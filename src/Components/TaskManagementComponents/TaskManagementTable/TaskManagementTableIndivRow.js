import React, { useState } from "react";

// Config
import {
  formatDate_taskManagement,
  status_dropdown_taskmanagement,
  taskmanagement_completed_status,
  taskmanagement_opentask_status,
  taskmanagement_overdue_status,
  taskManagementTableConfig,
} from "../../../Config/TaskManagementConfig";
import { findContentObj } from "../../../Config/ServiceComplaintsTableConfig";

// UI layout Component
import TableTextContent from "../../Utils/TableStructures/TableTextContent";
import Spinner from "../../../BasicUIElements/Spinner";
import SelectComponent from "../../../BasicUIElements/SelectComponent";

// Custom Hook
import UseUserData from "../../../Hooks/UseUserData";
import {
  fetch_list_taskmanagement,
  taskmanagement_filter_middleware,
} from "../../../Store/TaskManagement/taskManagementSlice";
import { API_ENDPOINTS, BaseURL, defaultHeaders } from "../../../API/APIConfig";
import { useDispatch, useSelector } from "react-redux";
import { PostAPI } from "../../../API/PostAPI";
import { fetch_Grid_list_taskmanagement } from "../../../Store/TaskManagement/TaskmanagementGridSlice";
import TableTextAreaTextModelDisplay from "../../../UI_Enginee/TableListEnginee/TableTextAreaTextModelDisplay";

// Helper function for status color code

export const selectColorCode_taskManagmenet = (content) => {
  if (content.id === taskmanagement_opentask_status.id) {
    return " bg-[#EB7F00] text-[#EB7F00] bg-opacity-20 ";
  }
  if (content.id === taskmanagement_overdue_status.id) {
    return " bg-[#FE464B] bg-opacity-20 text-[#FE464B] ";
  }
  if (content.id === taskmanagement_completed_status.id) {
    return " bg-[#B5DD5F] bg-opacity-25 text-[#B5DD5F]";
  }
  return "bg-[#758694] bg-opacity-[25%]";
};

const TaskManagementTableIndivRow = ({ content = {} }) => {
  const { getUsernameWithid, stringOfUsernameWithIdArray } = UseUserData();
  const dispatch = useDispatch();
  let taskDescription = content?.taskDescription;
  let department = content?.department;
  let project = content?.project;
  let assignee = stringOfUsernameWithIdArray(content?.assignee);
  let assignedBy = getUsernameWithid(content?.assignedBy);
  let dueDate = formatDate_taskManagement(new Date(content?.dueDate));
  let completedDate = formatDate_taskManagement(
    new Date(content?.completedDate)
  );

  let status = content?.status;
  let taskStatus = findContentObj(status, status_dropdown_taskmanagement);

  const { filter } = useSelector((state) => {
    return state?.taskManagement;
  });

  // -- Updating task mangement
  const [isLoading, setIsLoading] = useState(false);
  const updateTaskManagement = async (e) => {
    setIsLoading(true);

    const response = await updateStatus_TaskManagement({
      id: content?.id,
      Status: e?.id,
    });

    if (response) {
      dispatch(taskmanagement_filter_middleware(filter));
      // dispatch(fetch_list_taskmanagement());
      dispatch(fetch_Grid_list_taskmanagement());
    }

    setIsLoading(false);
  };

  return (
    <div
      className=" w-fit h-[52px] border-b-2 flex flex-row hover:bg-[#F0FEFF] ps-[2rem]  "
      key={content?.id}
    >
      <TableTextAreaTextModelDisplay
        title="Task Management"
        size={taskManagementTableConfig[0].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {taskDescription}
      </TableTextAreaTextModelDisplay>
      <TableTextContent
        size={taskManagementTableConfig[1].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {department}
      </TableTextContent>
      <TableTextContent
        size={taskManagementTableConfig[2].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {project}
      </TableTextContent>
      <TableTextContent
        size={taskManagementTableConfig[3].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {assignee}
      </TableTextContent>
      <TableTextContent
        size={taskManagementTableConfig[4].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {assignedBy}
      </TableTextContent>
      <TableTextContent
        size={taskManagementTableConfig[5].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {dueDate}
      </TableTextContent>
      <TableTextContent
        size={taskManagementTableConfig[6].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        <div className="flex flex-row items-center">
          <SelectComponent
            className={` w-[140px] h-[30px] bg-[#019BA229] rounded-[10px]  font-[600] text-[14px]  pe-4 ${selectColorCode_taskManagmenet(
              taskStatus
            )}`}
            value={taskStatus}
            optionarr={status_dropdown_taskmanagement}
            updateFunction={(e) => {
              if (taskStatus.id === e.id) return;
              updateTaskManagement(e);
            }}
          >
            <>{isLoading && <Spinner />}</>
          </SelectComponent>
        </div>
      </TableTextContent>
      <TableTextContent
        size={taskManagementTableConfig[7].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {completedDate}
      </TableTextContent>
    </div>
  );
};

export default TaskManagementTableIndivRow;

const updateStatus_TaskManagement = async (content) => {
  let obj = {};

  let id = content?.id;

  obj = {
    ...obj,
    Status: content?.Status,
  };

  let url = new URL(
    BaseURL.mainURl + API_ENDPOINTS.taskManagementUpdate + "/" + id
  );

  let headers = {
    ...defaultHeaders,
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": ["POST", "GET", "OPTIONS", "DELETE", "PUT"],
    "Access-Control-Allow-Headers": [
      "append",
      "delete",
      "entries",
      "foreach",
      "get",
      "has",
      "keys",
      "set",
      "values",
      "Authorization",
    ],

    redirect: "follow",

    // Authorization: token,
  };

  const response = await PostAPI(url, obj, headers, "PUT");

  if (response?.ok) {
    let data = await response.json();

    return true;
  }

  return false;
};
