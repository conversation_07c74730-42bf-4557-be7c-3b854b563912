import React, { useState } from "react";

// Components
import TaskManagementTableHeader from "./TaskManagementTableHeader";
import TaskManagementTableListing from "./TaskManagementTableListing";
import TaskManagementTopper from "./TaskManagementTopper";

function isSubarrayPresent(str2, str1) {
  // Check if the subArrayString is a substring of fullString
  return str2
    ?.toString()
    ?.toLowerCase()
    ?.includes(str1?.toString()?.toLowerCase());
}

const TaskManagementTableLayout = ({ list = [] }) => {
  const [searchState, setSearchState] = useState("");

  const update = (e) => {
    setSearchState(e);
  };

  list = list?.filter((item) => {
    return isSubarrayPresent(item?.taskDescription, searchState);
  });
  return (
    <>
      <div className="  flex-col flex-shrink-0 flex  ">
        <TaskManagementTopper state={searchState} setState={update} />
        <div className=" w-full h-fit flex flex-col overflow-auto  ">
          <div className=" w-full h-fit flex flex-col ">
            <TaskManagementTableHeader />
            <TaskManagementTableListing list={list} />
          </div>
        </div>
      </div>
    </>
  );
};

export default TaskManagementTableLayout;
