import React from "react";

// UI components and SVGs
import ButtonComponent from "../../BasicUIElements/ButtonComponent";
import GridSvg from "../../Assest/SVG/GridSvg";
import ListSVG from "../../Assest/SVG/ListSVG";

// Hooks
import { useDispatch, useSelector } from "react-redux";

// Actions
import {
     mode_taskManagement,
     taskManagementSliceActions,
} from "../../Store/TaskManagement/taskManagementSlice";
import SwitchBoxComponent from "../../BasicUIElements/SwitchBoxComponent";
import { taskmanagement_overdue_status } from "../../Config/TaskManagementConfig";
import {
     taskmanagement_grid_filter_middleware,
     taskmanagement_grid_filter_Thunk,
     taskManagmentGridSliceActions,
} from "../../Store/TaskManagement/TaskmanagementGridSlice";

const isOverDue = (content) => {
     // If only overdue status is picked,
     if (
          content?.length == 1 &&
          content[0]?.id === taskmanagement_overdue_status.id
     ) {
          return true;
     }
     return false;
};

const TaskMangementToggleComponent = ({ mode = "" }) => {
     const dispatch = useDispatch();

     const updateTaskManagementUiMode = (updatemode) => {
          dispatch(taskManagementSliceActions.updateUIMode(updatemode));
     };

     const { filter } = useSelector((state) => {
          return state?.taskManagementGrid;
     });
     const { ui } = useSelector((state) => {
          return state.taskManagement;
     });

     return (
          <div className=" flex flex-row items-center gap-[2rem] ">
               <h1 className=" text-[#45464E] font-inter font-[500]">
                    Tasks List
               </h1>

               <div className=" flex flex-row gap-[0.5rem]">
                    <ButtonComponent
                         onClick={() => {
                              // Grid view
                              updateTaskManagementUiMode(
                                   mode_taskManagement.grid
                              );
                         }}
                         className={` w-[34px] h-[34px] rounded-[6px]  flex flex-row justify-center items-center ${
                              mode === mode_taskManagement.grid
                                   ? "bg-mainLinearGradient"
                                   : "bg-[#C7C7CC]"
                         } `}
                    >
                         <GridSvg />
                    </ButtonComponent>
                    <ButtonComponent
                         onClick={() => {
                              // List view
                              updateTaskManagementUiMode(
                                   mode_taskManagement.list
                              );
                         }}
                         className={` w-[34px] h-[34px] rounded-[6px]  flex flex-row justify-center items-center ${
                              mode === mode_taskManagement.list
                                   ? "bg-mainLinearGradient"
                                   : "bg-[#C7C7CC]"
                         }`}
                    >
                         <ListSVG />
                    </ButtonComponent>
               </div>
               {mode_taskManagement?.grid === ui.mode && (
                    <div className=" ps-8 flex flex-row gap-[1rem] ">
                         <h1 className="  font-inter text-[#2B2F32] text-[14px]">
                              Show Only Overdue tasks
                         </h1>
                         <SwitchBoxComponent
                              onClick={() => {
                                   let array = [];
                                   if (isOverDue(filter?.status)) {
                                        array = [];
                                   } else {
                                        array = [taskmanagement_overdue_status];
                                   }

                                   dispatch(
                                        taskManagmentGridSliceActions.updateFilterStatus(
                                             array
                                        )
                                   );
                                   dispatch(
                                        taskmanagement_grid_filter_middleware({
                                             ...filter,
                                             status: array,
                                        })
                                   );
                              }}
                              value={isOverDue(filter?.status)}
                         />
                    </div>
               )}
          </div>
     );
};

export default TaskMangementToggleComponent;
