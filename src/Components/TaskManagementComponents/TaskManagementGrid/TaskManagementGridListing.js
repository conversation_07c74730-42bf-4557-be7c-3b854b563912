import React from "react";
import TaskManagementGridIndivComponet from "./TaskManagementGridIndivComponet";

const TaskManagementGridListing = ({ list = [] }) => {
     return (
          <div className=" w-full h-fit overflow-x-auto bg-[grey] bg-opacity-5 rounded-[1rem]">
               <div className=" w-fit h-fit flex flex-row  gap-[2rem] pb-[1rem]  bg-opacity-20">
                    {list.map((item) => {
                         return (
                              <TaskManagementGridIndivComponet content={item} />
                         );
                    })}
               </div>
          </div>
     );
};
// bg-[#E3E3E3]
export default TaskManagementGridListing;
