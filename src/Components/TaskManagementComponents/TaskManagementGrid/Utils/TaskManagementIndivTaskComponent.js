import React from "react";
import TickSVG from "../../../../Assest/SVG/TickSVG";
import {
     status_dropdown_taskmanagement,
     taskmanagement_completed_status,
     taskmanagement_opentask_status,
     taskmanagement_overdue_status,
} from "../../../../Config/TaskManagementConfig";
import { findContentObj } from "../../../../Config/ServiceComplaintsTableConfig";
import UseUserData from "../../../../Hooks/UseUserData";

const boxColor = (content) => {
     if (content.id === taskmanagement_opentask_status.id) {
          return " bg-[#EB7F00] bg-opacity-20 ";
     }
     if (content.id === taskmanagement_overdue_status.id) {
          return " bg-[#FE464B] bg-opacity-20 ";
     }
     if (content.id === taskmanagement_completed_status.id) {
          return " bg-[#B5DD5F] bg-opacity-20";
     }
     return "bg-[#758694] bg-opacity-[25%]";
};

const statusColor = (content) => {
     if (content.id === taskmanagement_opentask_status.id) {
          return " bg-[#F1BD6C] text-[#1E1F21] ";
     }
     if (content.id === taskmanagement_overdue_status.id) {
          return " bg-[#FF7E7E]  text-[#1E1F21]  ";
     }
     if (content.id === taskmanagement_completed_status.id) {
          return "  bg-[#27C200]  text-[white]  ";
     }
     return "bg-[#758694] text-[white]";
};

const TaskManagementIndivTaskComponent = ({ content }) => {
     let status = content?.status;
     let taskStatus = findContentObj(status, status_dropdown_taskmanagement);
     const { getUsernameWithid } = UseUserData();
     let assignee = getUsernameWithid(content?.assignedBy);
     return (
          <div
               className={` w-[90%] h-fit rounded-[10px]  flex flex-col justify-between p-2 gap-[0.5rem]  ${boxColor(
                    taskStatus
               )} `}
          >
               <div className=" w-full  h-fit flex flex-row justify-between">
                    <div className=" w-[30px] h-fit  flex flex-row justify-center">
                         <TickSVG content={taskStatus} />
                    </div>
                    <div className="pt-0 flex-1 text-[#333333] font-lato font-[400] text-[14px] ">
                         {content.taskDescription}
                    </div>
               </div>

               <div className=" w-full min-h-[30px] flex flex-row flex-shrink-0 justify-start items-center gap-[1rem]  ">
                    <div className=" w-fit h-fit ">
                         <div
                              className={`font-lato text-[12px]  ps-2 pe-2 pt-1 pb-1 rounded-[8px]  ${statusColor(
                                   taskStatus
                              )} `}
                         >
                              {status}
                         </div>
                    </div>
                    <div className=" w-fit flex flex-row flex-wrap items-center font-lato text-[#333333] text-[12px] text-wrap ">
                         Assignee: {assignee}
                    </div>
               </div>
          </div>
     );
};

export default TaskManagementIndivTaskComponent;
