import React from "react";
import TaskManagementGridListing from "./TaskManagementGridListing";
import TaskManagementGridButton from "./TaskManagementGridButton";
import { useSelector } from "react-redux";

const TaskManagementGridLayout = () => {
     const { gridList } = useSelector((item) => {
          return item.taskManagementGrid;
     });
     return (
          <div className=" w-full h-fit flex flex-col md:ps-6 md:pe-6 mt-[1rem]  ">
               <TaskManagementGridListing list={gridList} />
               <TaskManagementGridButton />
          </div>
     );
};

export default TaskManagementGridLayout;
