import React from "react";
import TaskManagementIndivTaskComponent from "./Utils/TaskManagementIndivTaskComponent";

const TaskManagementGridIndivComponet = ({ content }) => {
     return (
          <div className=" w-[300px] h-[550px] flex flex-col flex-shrink-0">
               <div className=" w-full h-[48px] flex flex-row flex-shrink-0  justify-center items-center font-lato font-[600] text-[#565557]">
                    {formatDate(new Date(content.date))}
               </div>

               <div className=" w-full  h-fit flex flex-col gap-[1.5rem] items-center overflow-y-auto mb-[1rem]">
                    {content?.list?.map((item) => {
                         return (
                              <TaskManagementIndivTaskComponent
                                   content={item}
                              />
                         );
                    })}
               </div>
          </div>
     );
};

export default TaskManagementGridIndivComponet;

function formatDate(date) {
     // Array of month names
     const monthNames = [
          "January",
          "February",
          "March",
          "April",
          "May",
          "June",
          "July",
          "August",
          "September",
          "October",
          "November",
          "December",
     ];

     // Get the day, month, and year from the date object
     const day = date.getDate();
     const month = monthNames[date.getMonth()];
     const year = date.getFullYear();

     // Construct the formatted date string
     const formattedDate = `${day}-${month}-${year}`;

     return formattedDate;
}
