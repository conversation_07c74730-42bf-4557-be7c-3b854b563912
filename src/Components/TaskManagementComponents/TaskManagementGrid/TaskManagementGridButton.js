import React from "react";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import { useDispatch } from "react-redux";
import { taskManagmentGridSliceActions } from "../../../Store/TaskManagement/TaskmanagementGridSlice";

const TaskManagementGridButton = () => {
     const dispatch = useDispatch();
     return (
          <div className=" w-full h-[100px] flex flex-row justify-between ps-6 pe-6 mt-[2rem]  ">
               <div></div>
               <div className=" w-fit h-fit flex flex-row gap-[2rem] ">
                    <ButtonComponent
                         onClick={() => {
                              dispatch(
                                   taskManagmentGridSliceActions.updateWeek(
                                        "previous"
                                   )
                              );
                         }}
                         className=" w-[150px] h-[45px] border-2 rounded-[8px] border-[#00BD94] text-[#00BD94] font-inter font-[400] flex flex-row justify-center items-center hover:bg-mainLinearGradient hover:border-transparent hover:text-[white] duration-200"
                    >
                         Previous
                    </ButtonComponent>
                    <ButtonComponent
                         onClick={() => {
                              dispatch(
                                   taskManagmentGridSliceActions.updateWeek(
                                        "next"
                                   )
                              );
                         }}
                         className=" w-[150px] h-[45px] border-2 rounded-[8px] border-[#00BD94] text-[#00BD94] font-inter font-[400] flex flex-row justify-center items-center hover:bg-mainLinearGradient hover:border-transparent hover:text-[white]  duration-200"
                    >
                         Next
                    </ButtonComponent>
               </div>
          </div>
     );
};

export default TaskManagementGridButton;
