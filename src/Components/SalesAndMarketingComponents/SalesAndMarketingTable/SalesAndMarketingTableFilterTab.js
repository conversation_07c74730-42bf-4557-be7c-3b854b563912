import React from "react";

import { useDispatch, useSelector } from "react-redux";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import UseSalesAndMarketing from "../../../Hooks/UseSalesAndMarketing";
import { SalesAndMarketingSliceActions } from "../../../Store/SalesAndMarketingSlice/SalesAndMarketingSlice";

const Selected = ({ text = "All", onClick }) => {
  return (
    <ButtonComponent
      onClick={onClick}
      className=" w-fit h-[48px] border-b-[4px] border-[#019BA2] ps-6 pe-6 font-poppins text-[16px] text-[#019BA2] font-[400]"
    >
      {text}
    </ButtonComponent>
  );
};

const Upselected = ({ text = "", onClick }) => {
  return (
    <ButtonComponent
      onClick={onClick}
      className=" w-fit h-[48px] border-b-[4px]  border-opacity-0 ps-6 pe-6 font-poppins text-[16px]  font-[400]"
    >
      {text}
    </ButtonComponent>
  );
};

// Helper Function
const isAll = (content = []) => {
  // If no status and more than one status are selected, then all should light up
  if (content?.length <= 0) {
    return true;
  }
  return false;
};

const isCompleted = (content = [], id) => {
  // If only completed status is picked,
  if (content?.length == 1 && content[0]?.id === id) {
    return true;
  }
  return false;
};

const SalesAndMarketingTableFilterTab = () => {
  const dispatch = useDispatch();

  const { filter } = useSelector((state) => {
    return state.salesAndMarketing;
  });
  const { fetch_list } = UseSalesAndMarketing();

  const updateState = (key, value) => {
    let updatedState = {
      ...filter?.state,
      [key]: value,
    };
    dispatch(SalesAndMarketingSliceActions.updateFilterState(updatedState));

    fetch_list(updatedState, null);
  };

  let array = ["NEW", "POTENTIAL", "DROPPED", "CLOSED"];
  return (
    <div className="min-w-full  w-full overflow-auto flex flex-row justify-between ps-6 pe-6 ">
      <div className=" w-fit flex flex-row ">
        {isAll(filter?.state?.LEAD_STATUS) ? (
          <Selected onClick={() => {}} text="All" />
        ) : (
          <Upselected
            onClick={() => {
              updateState("LEAD_STATUS", []);
            }}
            text="All"
          />
        )}

        {array?.map((item) => {
          return (
            <>
              {isCompleted(filter?.state.LEAD_STATUS, item) ? (
                <Selected onClick={() => {}} text={item} />
              ) : (
                <Upselected
                  onClick={() => {
                    updateState("LEAD_STATUS", [{ id: item, content: item }]);
                  }}
                  text={item}
                />
              )}
            </>
          );
        })}
      </div>
    </div>
  );
};

export default SalesAndMarketingTableFilterTab;
