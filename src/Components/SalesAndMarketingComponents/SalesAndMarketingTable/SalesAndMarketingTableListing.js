import React from "react";
import SalesAndMarketingTableInDivRow from "./SalesAndMarketingTableInDivRow";
import { useSelector } from "react-redux";

const SalesAndMarketingTableListing = ({ list = [] }) => {
  let obj = {
    OWNER_NAME: "<PERSON><PERSON><PERSON>",
    CONTACT_NO_1: "6382921488",
    CONTACT_NO_2: "6382921488",
    CONTACT_NO_3: "6382921488",
    CONTACT_NO_4: "6382921488",
    DATE_OF_VISIT: "18-09-2024",
    VISITED_BY: "<PERSON><PERSON><PERSON> P",
    LEAD_STATUS: "QUOTE SENT",
    LEAD_TO_COMPANY: "Vishnu",
    LEAD_TYPE: "INDIVIDUAL RESIDENT",
    CITY: "Coimbatore",
    AREA: "Kalapatti",
    LANDMARK: "Dr N.G.P arts and science college",
    engineer_name: "<PERSON><PERSON><PERSON>",
    engineer_number: "67858341029",
    ARCH_FIRM_NAME: "Dharan Groups",
    arch_name: "Dip",
    arch_number: "7867923453",
    PLUMBER_NAME: "Elango",
    PLUMBER_NUMBER: "67858341029",
    KEY_PERSON_NAME: "Mithun",
    KEY_PERSON_NUMBER: "7867923453",
    BRAND_TO_PROMOTE: "DURAVIT",
    LEAD_PERSON_REMARKS: "Goood looking",
    FOLLOW_UP_DATE_1: "20-09-2024",
    FOLLOW_UP_DATE_2: "21-09-2024",
    TELECALLING_REMARKS: "Not interested",
    RE_VISIT_DATE_1: "17-09-2024",
    RE_VISIT_DATE_2: "18-09-2024",
    RE_VISIT_DATE_3: "19-09-2024",
    RE_VISIT_DATE_4: "20-09-2024",
  };

  let list_dummy = [obj, obj];
  return (
    <div className=" w-fit h-fit min-h-[250px] mb-[5rem] ">
      {list.map((state) => {
        return <SalesAndMarketingTableInDivRow content={state} />;
      })}
    </div>
  );
};

export default SalesAndMarketingTableListing;
