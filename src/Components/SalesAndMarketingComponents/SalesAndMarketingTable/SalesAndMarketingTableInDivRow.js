import React, { useState } from "react";
import TableTextContent from "../../Utils/TableStructures/TableTextContent";
import { salesAndMarketingTableConfig } from "../../../Config/SalesAndMarketingTableConfig";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import edit from "../../../Assest/Utils/edit.png";

import { routes } from "../../../Config/routesConfig";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { SalesAndMarketingSliceActions } from "../../../Store/SalesAndMarketingSlice/SalesAndMarketingSlice";

import { API_ENDPOINTS, BaseURL } from "../../../API/APIConfig";
import { PostAPI } from "../../../API/PostAPI";
import {
  fetch_sales_edit_form_enginee,
  fetch_sales_lead_edit_form_enginee,
} from "../../../Dashboard_API_calling_and_formatting/sales_API";
import { formEnginee_edit_Formatee_Structure_and_State } from "../../../Dashboard_API_calling_and_formatting/Form_Formate_and_API";
import UseUserData from "../../../Hooks/UseUserData";
import TableList_array_template from "../../../UI_templates/TableList_array_template";
import UseRouterAuthentication from "../../../Hooks/UseRouterAuthentication";
import TableTextAreaTextModelDisplay from "../../../UI_Enginee/TableListEnginee/TableTextAreaTextModelDisplay";
import { returnUserNameWIthID_tickets } from "../../../Config/TicketingConfig";

const dateFormatFunction = (dt) => {
  let date = new Date(dt);

  let fullyear = date.getFullYear();
  let month =
    date.getMonth() + 1 < 10
      ? "0" + (date.getMonth() + 1)
      : date.getMonth() + 1;

  let dateNO = date.getDate();

  let format = fullyear + "-" + month + "-" + dateNO;

  return format;
};

const getAssigneeString = (array, userlist) => {
  let arrayf = [];
  for (let i = 0; i < array?.length; i++) {
    arrayf.push(returnUserNameWIthID_tickets(array[i], userlist));
  }

  return arrayf;
};

const SalesAndMarketingTableInDivRow = ({ content }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { routeAuthentication } = UseRouterAuthentication();

  const [editLoading, setEditLoading] = useState(false);

  const { getUsernameWithid, userList } = UseUserData();

  let lead_sales_person = getAssigneeString(
    content?.LEAD_SALES_PERSON,
    userList
  );

  let visited_by = getAssigneeString(content?.VISITED_BY, userList);

  const editClickHandler = async () => {
    // dispatch(sales_load_editform_thunk(content?._id, navigate));

    setEditLoading(true);
    let result = await fetch_sales_lead_edit_form_enginee(content?._id);
    let ss = formEnginee_edit_Formatee_Structure_and_State(result);
    dispatch(
      SalesAndMarketingSliceActions.updateEditStructureAndState({
        ...ss,
        id: content?._id,
      })
    );
    navigate(
      "?page=" +
        routes?.salesAndMarketingEditLead?.searchParams +
        "&id=" +
        content?._id
    );
    setEditLoading(false);
  };

  const deleteClickHandler = () => {};

  //----------------------------------------------------------------------------------------------------------------

  const [isLoading, setIsLoading] = useState(false);
  const auth = useSelector((state) => {
    return state.master.auth;
  });
  const { filter } = useSelector((state) => {
    return state.salesAndMarketing;
  });

  const updateLeadStatus = async (e) => {};

  //---------------------------------------------------------------------------------------------------------------
  return (
    <div
      className=" w-fit h-[52px] border-b-2 flex flex-row hover:bg-[#F0FEFF]  "
      key={content?._id}
    >
      <div className=" w-[60px] h-full flex flex-row justify-center items-center">
        {routeAuthentication("sales", "write") && (
          <ButtonComponent
            onClick={editClickHandler}
            isLoading={editLoading}
            className=" w-[24px] h-[24px] flex flex-rwo justify-center items-center "
          >
            <img
              src={edit}
              className=" w-full h-full object-contain"
              alt="edit"
            />
          </ButtonComponent>
        )}
      </div>
      {/* {salesAndMarketingTableConfig?.map((item) => {
        return (
          <TableTextContent
            size={item?.className}
            className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
          >
            {content[item?.key]}
          </TableTextContent>
        );
      })} */}
      <TableTextContent
        size={salesAndMarketingTableConfig[0]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.OWNER_NAME}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[1]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.CONTACT_NO_1}
      </TableTextContent>
      {/* <TableTextContent
        size={salesAndMarketingTableConfig[2]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.CONTACT_NO_2}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[3]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.CONTACT_NO_3}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[4]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.CONTACT_NO_4}
      </TableTextContent> */}
      <TableTextContent
        size={salesAndMarketingTableConfig[2]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {dateFormatFunction(new Date(content?.DATE_OF_VISIT))}
      </TableTextContent>
      <TableList_array_template
        list={visited_by}
        className={salesAndMarketingTableConfig[3]?.className}
      />
      <TableTextContent
        size={salesAndMarketingTableConfig[4]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.LEAD_STATUS}
      </TableTextContent>

      <TableList_array_template
        list={content?.LEAD_TO_COMPANY}
        className={salesAndMarketingTableConfig[5]?.className}
      />
      <TableList_array_template
        list={lead_sales_person}
        className={salesAndMarketingTableConfig[6]?.className}
      />
      {/* <TableTextContent
        size={salesAndMarketingTableConfig[9]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {getUsernameWithid(content?.LEAD_SALES_PERSON)}
      </TableTextContent> */}
      <TableTextContent
        size={salesAndMarketingTableConfig[7]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.LEAD_TYPE}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[8]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.CITY}
      </TableTextContent>
      {/* <TableTextContent
        size={salesAndMarketingTableConfig[12]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.AREA}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[13]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.LANDMARK}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[14]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.engineer_name}
      </TableTextContent> */}
      {/* <TableTextContent
        size={salesAndMarketingTableConfig[15]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.engineer_number}
      </TableTextContent> */}
      {/* <TableTextContent
        size={salesAndMarketingTableConfig[9]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.ARCH_FIRM_NAME}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[10]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.ARCH_NAME}
      </TableTextContent> */}
      {/* <TableTextContent
        size={salesAndMarketingTableConfig[18]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.ARCH_NUMBER}
      </TableTextContent> */}
      {/* <TableTextContent
        size={salesAndMarketingTableConfig[11]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.PLUMBER_NAME}
      </TableTextContent> */}
      {/* <TableTextContent
        size={salesAndMarketingTableConfig[20]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.PLUMBER_NUMBER}
      </TableTextContent> */}
      {/* <TableTextContent
        size={salesAndMarketingTableConfig[12]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.KEY_PERSON_NAME}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[13]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.KEY_PERSON_NUMBER}
      </TableTextContent> */}

      <TableList_array_template
        list={content?.BRAND_TO_PROMOTE}
        className={salesAndMarketingTableConfig[9]?.className}
      />
      {/* <TableTextContent
        size={salesAndMarketingTableConfig[15]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.No_of_Bathrooms}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[16]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.Tiles_Requirement}
      </TableTextContent> */}

      <TableTextAreaTextModelDisplay
        title="Lead Person Remarks"
        size={salesAndMarketingTableConfig[10]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.LEAD_PERSON_REMARKS}
      </TableTextAreaTextModelDisplay>
      <TableTextContent
        size={salesAndMarketingTableConfig[11]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.FOLLOW_UP_DATE_1}
      </TableTextContent>
      {/* Show revisit dates in a comma-separated list */}
      {content?.RE_VISIT_DATE && content.RE_VISIT_DATE.length > 0 && (
        <TableTextContent
          size={salesAndMarketingTableConfig[12]?.className}
          className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
        >
          {content.RE_VISIT_DATE.map((date, index) => {
            return index === 0 ? dateFormatFunction(new Date(date)) : ", " + dateFormatFunction(new Date(date));
          })}
        </TableTextContent>
      )}

      {/* Show revisit remarks */}
      <TableTextAreaTextModelDisplay
        title="Revisit Remarks"
        size={salesAndMarketingTableConfig[12]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.RE_VISIT_REMARKS || "No revisit remarks"}
      </TableTextAreaTextModelDisplay>

      {/* Show location */}
      <TableTextContent
        size={salesAndMarketingTableConfig[12]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.location || content?.LOCATION || ""}
      </TableTextContent>
      {/* Old commented code for reference */}
      {/* <TableTextContent
        size={salesAndMarketingTableConfig[25]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.FOLLOW_UP_DATE_2}
      </TableTextContent> */}
      {/* <TableTextContent
        size={salesAndMarketingTableConfig[26]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.TELECALLING_REMARKS}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[27]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.RE_VISIT_DATE_1}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[28]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.RE_VISIT_DATE_2}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[29]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.RE_VISIT_DATE_3}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[30]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.RE_VISIT_DATE_4}
      </TableTextContent> */}

      {/* <TableTextContent
                    size={salesAndMarketingTableConfig[0]?.className}
                    className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
               >
                    {leadId}
               </TableTextContent>
               <TableTextContent
                    size={salesAndMarketingTableConfig[1]?.className}
                    className=" font-[400] font-inter text-[14px] text-[#6E7079]"
               >
                    {leadName}
               </TableTextContent>
               <TableTextContent
                    size={salesAndMarketingTableConfig[2]?.className}
                    className=" font-[400] font-inter text-[14px] text-[#6E7079]"
               >
                    {emailAddress}
               </TableTextContent>
               <TableTextContent
                    size={salesAndMarketingTableConfig[3]?.className}
                    className=" font-[400] font-inter text-[14px] text-[#6E7079]"
               >
                    {phoneno}
               </TableTextContent>
               <TableTextContent
                    size={salesAndMarketingTableConfig[4]?.className}
                    className=" font-[400] font-inter text-[14px] text-[#6E7079]"
               >
                    {locationText}
               </TableTextContent>
               <TableTextContent
                    size={salesAndMarketingTableConfig[5]?.className}
                    className=" font-[400] font-inter text-[14px] text-[#6E7079]"
               >
                    {fieldVisitDate}
               </TableTextContent>
               <TableTextContent
                    size={salesAndMarketingTableConfig[6]?.className}
                    className=" font-[400] font-inter text-[14px] text-[#6E7079]"
               >
                    {salesRep}
               </TableTextContent>
               <TableTextContent
                    size={salesAndMarketingTableConfig[7]?.className}
                    className=" font-[400] font-inter text-[14px] text-[#6E7079] w-full h-full flex flex-row justify-center items-center"
               >
                    {siteState}
               </TableTextContent>
               <TableTextContent
                    size={salesAndMarketingTableConfig[8]?.className}
                    className=" font-[400] font-inter text-[12px] text-[#6E7079] w-full h-full overflow-y-auto pt-1 pb-1  ps-6"
               >
                    {walkinStatus}
               </TableTextContent>
               <TableTextContent
                    size={salesAndMarketingTableConfig[9]?.className}
                    className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
               >
                    {lastwalkInStatus}
               </TableTextContent>
               <TableTextContent
                    size={salesAndMarketingTableConfig[10]?.className}
                    className=" font-[400] font-inter text-[14px]  text-[#6E7079] ps-6 "
               >
                    <div className="flex flex-row items-center">
                         <SelectComponent
                              className={` w-[140px] h-[30px] bg-[#019BA229]  rounded-[10px]  font-[600] text-[14px]  pe-4 ${selectColorCode_Sales(
                                   leadStatusContent
                              )}`}
                              value={leadStatusContent}
                              optionarr={Lead_Status_Dropdown}
                              updateFunction={(e) => {
                                   if (leadStatusContent.content === e.content)
                                        return;
                                   updateLeadStatus(e);
                              }}
                         >
                              <>{isLoading && <Spinner />}</>
                         </SelectComponent>
                    </div>
               </TableTextContent>

               <TableTextContent
                    size={salesAndMarketingTableConfig[11]?.className}
                    className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
               >
                    {customerId}
               </TableTextContent>
               {/* <TableLinkContent
                    size={salesAndMarketingTableConfig[11]?.className}
                    className=" font-[700] font-inter text-[14px] text-[#019BA2] hover:underline-offset-4 "
                    to={routes.customerSignIn.directLink}
               >
                    <p className=" underline-offset-4">{customerId}</p>
               </TableLinkContent> */}
      {/* <TableTextContent
        size={salesAndMarketingTableConfig[12]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {archName}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[13]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {archNumber}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[14]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {engineerName}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[15]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {engineerNumber}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[16]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {brand}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[17]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {concern}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[18]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {keyPersonName}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[19]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {keyPersonNumber}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[20]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {noOfBathRooms}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[21]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {purpose}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[22]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {reference}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[23]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {leadType}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[24]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {leadSource}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[25]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {contactNo1}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[26]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {contactNo2}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[27]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {contactNo3}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[28]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {contactNo4}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[29]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {leadToCompany}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[30]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {leadSalesPerson}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[31]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {city}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[32]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {area}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[33]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {landmark}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[34]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {archFirmName}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[35]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {plumberName}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[36]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {plumberNumber}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[37]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {siteStage}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[38]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {areaRequirement}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[39]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {brandToPromote}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[40]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {leadPersonRemarks}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[41]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {followUpDate1}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[42]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {followUpDate2}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[43]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {telecallingRemarks}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[44]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {revisitDate1}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[45]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {revisitDate2}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[46]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {revisitDate3}
      </TableTextContent>
      <TableTextContent
        size={salesAndMarketingTableConfig[47]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] ps-6"
      >
        {revisitDate4}
      </TableTextContent>{" "} */}

      {/* <div className=" w-[60px] h-full flex flex-row justify-center items-center">
                    <ButtonComponent
                         onClick={deleteClickHandler}
                         className=" w-[24px] h-[24px] flex flex-rwo justify-center items-center "
                    >
                         <img
                              src={deletething}
                              className=" w-full h-full object-contain"
                              alt="edit"
                         />
                    </ButtonComponent>
               </div> */}
    </div>
  );
};

export default SalesAndMarketingTableInDivRow;

const editLeadStatus = async (id, leadStatus, auth) => {
  let url = new URL(
    BaseURL.mainURl + API_ENDPOINTS.salesAndMarketingEditLead + "/" + id
  );

  url.searchParams.set("userId", auth.id);

  let bodyObj = {
    // closureAt: new Date(),
    lead_status: leadStatus,
  };

  let headers = {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": ["PUT", "POST", "GET", "OPTIONS", "DELETE"],

    redirect: "follow",

    Authorization: auth.token,
  };

  const response = await PostAPI(url, bodyObj, headers, "PUT");

  if (response?.ok) {
    let data = await response.json();

    return true;
  }

  return false;
};
