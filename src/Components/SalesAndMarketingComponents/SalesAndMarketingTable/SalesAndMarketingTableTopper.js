import React from "react";

import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import { FaDownload } from "react-icons/fa6";
import UseJsObj2CSV from "../../../Hooks/UseJsObj2CSV";

const SalesAndMarketingTableTopper = ({ state = "", setState = () => {} }) => {
  const { downloadSalesDashboard } = UseJsObj2CSV();
  return (
    <div className=" min-w-full w-fit h-[45px]  flex flex-row justify-between items-center ps-8 pe-8  gap-[2rem]">
      <h1 className=" text-[#45464E] font-[600] font-inter text-[16px] flex flex-row flex-shrink-0 ">
        Sales List
      </h1>

      <div className=" w-fit h-full flex flex-row items-center  gap-[0.8rem]   ">
        <div className=" w-[200px] flex flex-col items-center relative">
          <input
            value={state}
            type={"text"}
            placeholder={"Search"}
            onChange={(event) => {
              setState(event.target.value);
            }}
            className=" w-full h-[38px] rounded-[6px] ps-5  border border-[#D8E1F2] text-[16px] font-poppins text-[#53545C] placeholder:font-poppins placeholder:text-[16px] placeholder:font-[400] placeholder:text-[#ABAFB1]"
          />
        </div>
        <ButtonComponent
          onClick={downloadSalesDashboard}
          className=" w-fit h-[50px] flex flex-row justify-center items-center gap-[0.25rem]"
        >
          <FaDownload />
        </ButtonComponent>
      </div>
    </div>
  );
};

export default SalesAndMarketingTableTopper;
