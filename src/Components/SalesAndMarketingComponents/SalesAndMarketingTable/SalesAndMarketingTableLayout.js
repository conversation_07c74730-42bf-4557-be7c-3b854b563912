import React, { useState } from "react";
import SalesAndMarketingTableTopper from "./SalesAndMarketingTableTopper";
import SalesAndMarketingTableHeader from "./SalesAndMarketingTableHeader";
import SalesAndMarketingTableListing from "./SalesAndMarketingTableListing";
import SalesAndMarketingTableFilterTab from "./SalesAndMarketingTableFilterTab";

function isSubarrayPresent(str2, str1) {
  // Check if the subArrayString is a substring of fullString
  return str2
    ?.toString()
    ?.toLowerCase()
    ?.includes(str1?.toString()?.toLowerCase());
}

const SalesAndMarketingTableLayout = ({ list = [] }) => {
  const [searchState, setSearchState] = useState("");

  const update = (e) => {
    setSearchState(e);
  };

  list = list?.filter((item) => {
    return isSubarrayPresent(item?.OWNER_NAME, searchState);
  });

  return (
    <>
      <SalesAndMarketingTableTopper state={searchState} setState={update} />
      <SalesAndMarketingTableFilterTab />
      <div className=" w-fit h-fit flex flex-col overflow-x-auto z-[10] ">
        <div className=" w-fit h-fit flex flex-col ">
          <SalesAndMarketingTableHeader />
          <SalesAndMarketingTableListing list={list} />
        </div>
      </div>

      {/* <div className=" flex flex-col flex-shrink-0 ">
        <SalesAndMarketingTableTopper state={searchState} setState={update} />
        <SalesAndMarketingTableFilterTab />
        <div className=" w-full h-fit flex flex-col overflow-x-auto z-[10] ">
          <div className=" w-full h-fit flex flex-col ">
            <SalesAndMarketingTableHeader />
            <SalesAndMarketingTableListing list={list} />
          </div>
        </div>
      </div> */}
    </>
  );
};

export default SalesAndMarketingTableLayout;
