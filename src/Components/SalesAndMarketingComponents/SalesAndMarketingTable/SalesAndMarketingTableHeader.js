import React, { useEffect } from "react";

import { useDispatch, useSelector } from "react-redux";
import { SalesAndMarketingSliceActions } from "../../../Store/SalesAndMarketingSlice/SalesAndMarketingSlice";
import TableHeaderEngineeWrapper from "../../../UI_Enginee/TableHeaderEnginee/TableHeaderEngineeWrapper";
import TableHeaderEnginee, {
  parser_table_filter_data,
  table_header_state_update,
} from "../../../UI_Enginee/TableHeaderEnginee/TableHeaderEnginee";
import UseSalesAndMarketing from "../../../Hooks/UseSalesAndMarketing";

const SalesAndMarketingTableHeader = () => {
  const dispatch = useDispatch();
  const { filter, table } = useSelector((state) => {
    return state.salesAndMarketing;
  });

  const { fetchWithNewFilter, fetch_list } = UseSalesAndMarketing();

  const master = useSelector((state) => {
    return state.master;
  });

  let auth = master?.auth;

  useEffect(() => {
    let state = table_header_state_update(table.header, filter?.state);
    dispatch(SalesAndMarketingSliceActions.updateFilterState(state?.state));
  }, [table?.header]);

  const updateState = (key, value) => {
    let updatedState = {
      ...filter?.state,
      [key]: value,
    };
    dispatch(SalesAndMarketingSliceActions.updateFilterState(updatedState));

    fetch_list(updatedState, null);

    // let filterObj = parser_table_filter_data(table?.header, updatedState);

    // if (filterObj?.isFilter) {
    //   fetchWithNewFilter(filterObj.state);
    // } else {
    //   fetchWithNewFilter({});
    // }
  };

  return (
    <TableHeaderEngineeWrapper className="w-fit h-[52px] border-b-2 flex flex-row z-[0]">
      <div className=" w-[60px] h-full flex flex-row justify-center items-center">
        {/* <CheckBoxComponent value={false} onClick={() => {}} /> */}
      </div>

      <TableHeaderEnginee
        table={table.header}
        state={filter?.state}
        updateState={updateState}
      />
    </TableHeaderEngineeWrapper>
  );
};

export default SalesAndMarketingTableHeader;
