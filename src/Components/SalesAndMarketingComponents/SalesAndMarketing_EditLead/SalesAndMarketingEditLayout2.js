import React, { useEffect, useState } from "react";
import ModelHeaderAndRoute from "../../Utils/UtilsComponents/ModelHeaderAndRoute";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import { useNavigate, useSearchParams } from "react-router-dom";
import FormEnginee from "../../../UI_Enginee/Form/FormEnginee";
import { useDispatch, useSelector } from "react-redux";
import { SalesAndMarketingSliceActions } from "../../../Store/SalesAndMarketingSlice/SalesAndMarketingSlice";
import {
  fetch_sales_edit_form_enginee,
  fetch_sales_lead_edit_form_enginee,
} from "../../../Dashboard_API_calling_and_formatting/sales_API";
import {
  formData_parser,
  formEnginee_edit_Formatee_Structure_and_State,
} from "../../../Dashboard_API_calling_and_formatting/Form_Formate_and_API";
import Spinner from "../../../BasicUIElements/Spinner";
import { API_ENDPOINTS, api_headers, BaseURL } from "../../../API/APIConfig";
import { PostAPI } from "../../../API/PostAPI";
import UseSalesAndMarketing from "../../../Hooks/UseSalesAndMarketing";
import UseRouterAuthentication from "../../../Hooks/UseRouterAuthentication";
import { routes } from "../../../Config/routesConfig";
import UseMarketing from "../../../Hooks/UseMarketing";
import toast from "react-hot-toast";

const SalesAndMarketingEditLayout2 = ({
  back = routes?.salesandmarketing?.directLink,
}) => {
  const [searchParams] = useSearchParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { fetch_list } = UseSalesAndMarketing();
  const marketingFetchList = UseMarketing().fetchList;

  const { edit } = useSelector((state) => {
    return state.salesAndMarketing;
  });

  const { routeAuthentication } = UseRouterAuthentication();
  const { auth, access } = useSelector((state) => {
    return state.master;
  });
  useEffect(() => {
    if (!routeAuthentication("sales", "edit")) {
      navigate(routes?.salesandmarketing?.directLink);
    }
  }, [auth, access]);

  const [state, setState] = useState({});
  const [isContentLoading, setIsContentLoading] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    let id = searchParams?.get("id");

    let fetch = async () => {
      setIsContentLoading(true);

      let result = await fetch_sales_lead_edit_form_enginee(id);
      let ss = formEnginee_edit_Formatee_Structure_and_State(result);
      setState(ss.state);
      dispatch(
        SalesAndMarketingSliceActions.updateEditStructureAndState({
          ...ss,
          id: id,
        })
      );

      setIsContentLoading(false);

      if (ss.structure?.length === 0) {
        return false;
      } else {
        return true;
      }
    };

    if (id !== edit?.id) {
      fetch();
    } else {
      setState(edit.state);
    }
  }, [edit.id]);

  const updateFunctionOfForm = (key, value) => {
    setState((state) => {
      return { ...state, [key]: { ...state[key], value: value } };
    });
  };

  const closeModal = () => {
    navigate(back);
  };

  const clickHandler = async () => {
    let parser_obj = formData_parser(edit.structure, state);

    setIsLoading(true);
    let toast_id = toast.loading("Updating", { position: "top-right" });

    let response = await salesandmarketing_edit(
      parser_obj?.parser_obj,
      searchParams?.get("id")
    );

    if (response) {
      fetch_list();
      marketingFetchList();
      closeModal();
      toast.success("Updated", { id: toast_id });
    } else {
      let responsedata = await response.json();
      toast.error(responsedata?.error, { id: toast_id });
    }
    setIsLoading(false);
  };

  return (
    <>
      {isContentLoading ? (
        <Spinner />
      ) : (
        <div className=" md:w-[424px] w-[340px] max-h-[95vh] h-fit overflow-y-auto bg-[#fff] rounded-[6px] p-4 pb-[4rem] ">
          <ModelHeaderAndRoute
            className="w-full h-fit flex flex-row justify-between ps-4 pe-4 "
            title="Edit The Lead"
            onClick={() => {
              closeModal();
            }}
          />

          <FormEnginee
            form={edit?.structure}
            formStateFunction={updateFunctionOfForm}
            formState={state}
          />

          <div className=" w-full h-fit mt-[1rem]">
            <div className=" w-full h-fit flex flex-row justify-center gap-[1rem]">
              <ButtonComponent
                onClick={() => {
                  closeModal();
                }}
                className=" w-[45%] h-[58px] rounded-[6px] border-[#00BD94] border-2 bg-clip-text text-transparent bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] flex flex-row justify-center items-center"
              >
                Cancel
              </ButtonComponent>
              <ButtonComponent
                onClick={clickHandler}
                isLoading={isLoading}
                className=" w-[45%] h-[58px] rounded-[6px] bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] text-[#fff] flex flex-row justify-center items-center"
              >
                Edit The Lead
              </ButtonComponent>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default SalesAndMarketingEditLayout2;

const salesandmarketing_edit = async (content, id) => {
  let url = new URL(BaseURL?.mainURl + API_ENDPOINTS?.salesLeadEdit + "/" + id);

  let body = content;

  let headers = { ...api_headers };

  let response = await PostAPI(url, body, headers, "PUT");

  if (response?.ok) {
    let data = await response?.json();
    return true;
  }
  return false;
};
