import React, { useEffect, useState } from "react";

import Model<PERSON>eaderAndRoute from "../../Utils/UtilsComponents/ModelHeaderAndRoute";

import { useDispatch, useSelector } from "react-redux";
import { SalesAndMarketingSliceActions } from "../../../Store/SalesAndMarketingSlice/SalesAndMarketingSlice";

import ButtonComponent from "../../../BasicUIElements/ButtonComponent";

import { API_ENDPOINTS, BaseURL, defaultHeaders } from "../../../API/APIConfig";
import { PostAPI } from "../../../API/PostAPI";
import FormEnginee from "../../../UI_Enginee/Form/FormEnginee";

import {
  formData_parser,
  formEnginee_Formatee_Structure_and_State,
} from "../../../Dashboard_API_calling_and_formatting/Form_Formate_and_API";
import { useNavigate } from "react-router-dom";
import { marketing_list_thunk } from "../../../Store/MarketingSlice/MarketingSlices";
import Spinner from "../../../BasicUIElements/Spinner";
import { routes } from "../../../Config/routesConfig";
import { fetch_sales_add_form_enginee } from "../../../Dashboard_API_calling_and_formatting/sales_API";
import UseSalesAndMarketing from "../../../Hooks/UseSalesAndMarketing";
import { vaildator_Form_function } from "../../../UI_Enginee/Form/FormEngineeConfig";
import UseRouterAuthentication from "../../../Hooks/UseRouterAuthentication";
import toast from "react-hot-toast";

const SalesAndMarketingAddLeadLayout = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { fetch_list } = UseSalesAndMarketing();

  const { routeAuthentication } = UseRouterAuthentication();
  const { auth, access } = useSelector((state) => {
    return state.master;
  });

  useEffect(() => {
    if (!routeAuthentication("sales", "write")) {
      navigate(routes?.salesandmarketing?.directLink);
    }
  }, [auth, access]);

  const { add } = useSelector((state) => {
    return state.salesAndMarketing;
  });

  const [state, setState] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  const fetch = async () => {
    let response = await fetch_sales_add_form_enginee();
    if (response?.length === 0) {
      return false;
    }
    let ss = formEnginee_Formatee_Structure_and_State(response);
    setState(ss.state);
    dispatch(SalesAndMarketingSliceActions.updateStructureAndState(ss));
    if (response?.ok) {
      return true;
    }

    return false;
  };

  useEffect(() => {
    // Setting structure and state if not added
    if (add.structure?.length === 0) {
      // dispatch(sales_load_addForm_thunk());
      if (fetch()) {
      } else {
        window.alert("Please check the network and refresh");
      }
    } else {
      setState(add.state);
    }
  }, [add.state]);

  const updateFunctionOfForm = (key, value) => {
    // Updating the state
    let updateState = {
      ...state,
      [key]: { ...state[key], value: value },
    };
    setState(updateState);
  };

  // Submit Handler
  const submitHandler = async () => {
    let vaildate = vaildator_Form_function(add.structure, state);

    dispatch(
      SalesAndMarketingSliceActions?.updateState(vaildate?.validate_Obj)
    );
    setState(vaildate?.validate_Obj);
    if (!vaildate?.isFormValid) {
      toast.error("Fill all mandatory fields to add the sales lead", {
        position: "top-right",
      });
      return;
    }

    let parser_obj = formData_parser(add.structure, state);

    let obj = {
      ...parser_obj.parser_obj,
      assignedBy: auth?.id,
      // lead_status: "New",
    };

    setIsLoading(true);
    let toast_id = toast.loading("Posting", { position: "top-right" });
    let response = await salesAndMarketingAddALeadFunction(obj, auth);

    let responsedata = await response.json();

    if (response?.ok) {
      fetch_list(null, null);
      fetch();
      closeModal();
      toast.success("Added", { id: toast_id });
    } else {
      toast.error(responsedata?.error, { id: toast_id });
    }
    setIsLoading(false);
  };

  // Close Modal
  const closeModal = () => {
    navigate(-1);
  };

  return (
    <>
      {add.structure?.length === 0 ? (
        <Spinner />
      ) : (
        <div className=" md:w-[424px] w-[340px] max-h-[95vh] h-fit overflow-y-auto bg-[#fff] rounded-[6px] p-4 pb-[1rem] ">
          {/* HEADER  */}
          <ModelHeaderAndRoute
            className="w-full h-fit flex flex-row justify-between ps-4 pe-4 "
            title="Add a Lead"
            onClick={() => {
              closeModal();
            }}
          />

          {/* Form Enginee */}
          <FormEnginee
            form={add?.structure}
            formStateFunction={updateFunctionOfForm}
            formState={state}
          />

          {/* BUTTON COMPONENT  */}
          <div className=" w-full h-fit mt-[1rem]">
            <div className=" w-full h-fit flex flex-row justify-center gap-[1rem]">
              <ButtonComponent
                onClick={() => {
                  closeModal();
                }}
                className=" w-[45%] h-[58px] rounded-[6px] border-[#00BD94] border-2 bg-clip-text text-transparent bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] flex flex-row justify-center items-center"
              >
                Cancel
              </ButtonComponent>
              <ButtonComponent
                onClick={submitHandler}
                isLoading={isLoading}
                className=" w-[45%] h-[58px] rounded-[6px] bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] text-[#fff] flex flex-row justify-center items-center"
              >
                Add a Lead
              </ButtonComponent>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default SalesAndMarketingAddLeadLayout;

export const salesAndMarketingAddALeadFunction = async (content, auth) => {
  const url = new URL(BaseURL.mainURl + API_ENDPOINTS?.salesAddLead);

  const bodyObj = content;

  let headers = {
    ...defaultHeaders,
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": ["POST", "GET", "OPTIONS", "DELETE", "PUT"],
    "Access-Control-Allow-Headers": [
      "append",
      "delete",
      "entries",
      "foreach",
      "get",
      "has",
      "keys",
      "set",
      "values",
      "Authorization",
    ],

    redirect: "follow",

    Authorization: auth.token,
  };

  const response = await PostAPI(url, bodyObj, headers);

  return response;
};
