import React, { useEffect } from "react";
import SectionHeaders from "../Utils/SectionHeaders";
import SalesDataBox from "./SalesDataBox";
import SalesAndMarketingTableLayout from "./SalesAndMarketingTable/SalesAndMarketingTableLayout";
import { useSelector } from "react-redux";

import ButtonComponent from "../../BasicUIElements/ButtonComponent";

import { useNavigate } from "react-router-dom";
import { routes } from "../../Config/routesConfig";
import UseSalesAndMarketing, {
  dateObj,
} from "../../Hooks/UseSalesAndMarketing";
import UseRouterAuthentication from "../../Hooks/UseRouterAuthentication";
import DashboardNavbar_template from "../../UI_templates/Dashboard_Templates/DashboardNavbar_template";

import { SalesAndMarketingSliceActions } from "../../Store/SalesAndMarketingSlice/SalesAndMarketingSlice";
import DateFilterTemplateComponent from "../../UI_templates/DateFilterTemplate/DateFilterTemplateComponent";

const SalesAndMarketingLayout = () => {
  const navigate = useNavigate();
  const salesAndMarketing = useSelector((state) => {
    return state.salesAndMarketing;
  });

  const salesAndMarketingList = useSelector((state) => {
    return state.salesAndMarketing.salesAndMarketingList;
  });

  const { isDashboardAccessable, routeAuthentication } =
    UseRouterAuthentication();

  const { auth, access } = useSelector((state) => {
    return state.master;
  });
  const { fetchList, loadTableHeaderData, fetchAnalytics, fetch_list } =
    UseSalesAndMarketing();

  useEffect(() => {
    isDashboardAccessable("sales");
    loadTableHeaderData();
    // fetchList();
    fetchAnalytics();

    fetch_list(null, null);
  }, [auth, access]);

  return (
    <div className=" w-full h-full  flex flex-col ">
      <DashboardNavbar_template
        listLink={routes?.salesandmarketing?.directLink}
        insightsLink={routes?.salesAndMarketingAnalytics?.directLink}
        dashboard="sales"
      />
      <div className=" flex-1 overflow-y-auto">
        <div className=" w-full h-[50px] flex flex-row justify-between ps-8 pe-8 items-end">
          <div></div>
          {routeAuthentication("sales", "write") && (
            <ButtonComponent
              onClick={() => {
                navigate(
                  "?page=" + routes?.salesAndMarketingAddLead?.searchParams
                );
                // dispatch(sales_load_addForm_thunk());\
                // downloadCSV(salesAndMarketingList);
              }}
              className=" w-[185px] h-[36px] bg-mainLinearGradient rounded-[12px] font-inter font-[700] text-[14px] text-[#fff] "
            >
              + Add a New Lead
            </ButtonComponent>
          )}
        </div>
        <div className="  w-full h-fit ps-4 flex flex-row flex-nowrap overflow-y-auto justify-start flex-shrink-0 gap-[1rem] mt-[3rem]">
          <div className=" w-full flex flex-col gap-[1.5rem]">
            <SectionHeaders>Sales Summary</SectionHeaders>
            <SalesDataBox headerData={salesAndMarketing?.headerData} />
          </div>
        </div>

        <div className=" w-full h-[2rem]"></div>

        <DateFilterTemplateComponent
          fetch={fetch_list}
          updateSlice={SalesAndMarketingSliceActions.updateFilterDate}
          dateObj={dateObj}
          customDate="Custom Date"
          date={salesAndMarketing?.filter?.date}
        />

        <SalesAndMarketingTableLayout list={salesAndMarketingList} />
      </div>
    </div>
  );
};

export default SalesAndMarketingLayout;
