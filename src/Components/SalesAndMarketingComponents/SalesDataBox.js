import React, { useState } from "react";
import user from "../../Assest/Utils/UserLogo.png";
import DataStructureBoxHeader from "../Utils/DataStuctureBoxes/DataStructureBoxHeader";
import SelectComponent from "../../BasicUIElements/SelectComponent";
import { useSelector } from "react-redux";
const opt = [
  {
    id: "",
    content: "this week",
  },
  {
    id: "",
    content: "this month",
  },
  {
    id: "",
    content: "this year",
  },
  {
    id: "",
    content: "this month",
  },
];

const SalesDataBox = ({ headerData = {} }) => {
  const { headerDataSales } = useSelector((state) => {
    return state?.salesAndMarketing;
  });
  return (
    <div className=" w-full h-fit flex flex-row flex-nowrap overflow-x-auto md:p-0 ps-8 pe-8 pb-[2rem]">
      <div className=" md:w-[430px] w-[320px] h-[145px] flex flex-col flex-shrink-0 justify-between border-2 p-3 rounded-[10px]">
        <DataStructureBoxHeader logo={user}>
          {/* <SelectComponent
                              value={state}
                              updateFunction={setState}
                              optionarr={opt}
                              className=" w-[200px] h-[35px] text-[#BEC0CA]"
                         /> */}
        </DataStructureBoxHeader>

        <div className=" w-full h-[60px] flex flex-row justify-between ">
          <div className="w-fit h-full flex flex-col justify-between ">
            <h1 className=" font-inter font-[400] text-[#8B8D97] text-[14px] ">
              Leads Vs Closure
            </h1>
            <h5 className=" font-poppins font-[500]  text-[20px] ">
              {headerDataSales?.Leads} {"<>"} {headerDataSales?.Closure}
            </h5>
          </div>
          <div className="w-fit h-full flex flex-col justify-between ">
            <h1 className=" font-inter font-[400] text-[#8B8D97] text-[14px] ">
              Target Vs Achieved
            </h1>
            <h5 className=" font-poppins font-[500]  text-[20px] ">
              {headerDataSales?.Target} {"<>"} {headerDataSales?.Achieved}
            </h5>
          </div>

          <div></div>
        </div>
      </div>
    </div>
  );
};

export default SalesDataBox;
