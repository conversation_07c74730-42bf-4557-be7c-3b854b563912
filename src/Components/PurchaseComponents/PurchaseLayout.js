import React, { useEffect } from "react";
import SectionHeaders from "../Utils/SectionHeaders";
import PurchaseDataBox from "./PurchaseDataBox";
import PurchaseTableLayout from "./PurchaseTable/PurchaseTableLayout";
import { useDispatch, useSelector } from "react-redux";
import {
  purchaseFetchHeaderDataThunk,
  PurchaseSliceActions,
} from "../../Store/PurchaseSlices/PurchaseSlice";
import UseRouterAuthentication from "../../Hooks/UseRouterAuthentication";
import DashboardNavbar_template from "../../UI_templates/Dashboard_Templates/DashboardNavbar_template";
import { routes } from "../../Config/routesConfig";
import UsePurchase, { dateObj } from "./UsePurchase";
import DateFilterTemplateComponent from "../../UI_templates/DateFilterTemplate/DateFilterTemplateComponent";

const PurchaseLayout = () => {
  const dispatch = useDispatch();
  const { isDashboardAccessable } = UseRouterAuthentication();
  const { fetchList, fetchAnalytics, fetch_list } = UsePurchase();

  const { auth, access } = useSelector((state) => {
    return state.master;
  });

  const { date } = useSelector((state) => {
    return state.purchase.filter;
  });

  useEffect(() => {
    isDashboardAccessable("purchase");

    dispatch(purchaseFetchHeaderDataThunk());

    // Fetch Listing
    fetch_list();
    // fetchList();
    fetchAnalytics();
  }, [auth, access]);

  return (
    <div className=" w-full h-full  flex flex-col ">
      <DashboardNavbar_template
        listLink={routes?.purchase?.directLink}
        insightsLink={routes?.purchaseAnalytics?.directLink}
        dashboard="purchase"
      />
      <div className=" flex-1 overflow-y-auto">
        <div className=" w-full h-fit  flex flex-row justify-between items-center ps-8 pe-8">
          <SectionHeaders>Purchase Summary</SectionHeaders>
        </div>

        <div className=" w-full h-fit mt-8 ps-4">
          <PurchaseDataBox />
        </div>
        <div className=" w-full h-[2rem]"></div>

        <DateFilterTemplateComponent
          fetch={fetch_list}
          updateSlice={PurchaseSliceActions.updateFilterDate}
          dateObj={dateObj}
          customDate="Custom Date"
          date={date}
        />

        <PurchaseTableLayout />
      </div>
    </div>
  );
};

export default PurchaseLayout;
