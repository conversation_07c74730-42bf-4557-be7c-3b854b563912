import React from "react";
import PurchaseTableIndivRow from "./PurchaseTableIndivRow";
import { useSelector } from "react-redux";

const PurchaseTableListing = () => {
  const purchaseListing = useSelector((state) => {
    return state.purchase.purchaseList;
  });

  return (
    <div className=" w-fit h-fit min-h-[200px] ">
      {purchaseListing?.map((content) => {
        return <PurchaseTableIndivRow content={content} />;
      })}
    </div>
  );
};

export default PurchaseTableListing;
