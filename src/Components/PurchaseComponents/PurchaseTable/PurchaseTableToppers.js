import React, { useState } from "react";
import SelectComponent from "../../../BasicUIElements/SelectComponent";

import SwitchBoxComponent from "../../../BasicUIElements/SwitchBoxComponent";
import { useDispatch, useSelector } from "react-redux";
import { switchToggleButton_IsPresent } from "../../../Config/TableConfig";
import {
  PurchaseSliceActions,
  purchaseFilterListFetchThunk,
} from "../../../Store/PurchaseSlices/PurchaseSlice";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import { FaDownload } from "react-icons/fa6";
import UseJsObj2CSV from "../../../Hooks/UseJsObj2CSV";

const PurchaseTableToppers = () => {
  const dispatch = useDispatch();
  const { filter } = useSelector((state) => {
    return state.purchase;
  });
  let natureOfOrder = filter.natureOfStock;
  let isTrue_OutOfStock = switchToggleButton_IsPresent(
    "Against Order",
    natureOfOrder
  );

  const changeHandler = (array) => {
    dispatch(PurchaseSliceActions.updateFilterNatureOfStock(array));
    dispatch(
      purchaseFilterListFetchThunk({
        ...filter,
        natureOfStock: array,
      })
    );
  };

  //--------------------------------------
  const { downloadPurchaseDownload } = UseJsObj2CSV();

  return (
    <div className="w-fit  min-w-full h-[45px]  flex flex-row flex-shrink-0 justify-between items-center gap-[2rem] ps-8 pe-8 border-b-2">
      <h1 className=" text-[#45464E] font-[600] font-inter text-[16px]  ">
        Purchase List
      </h1>

      <div className=" w-fit h-full flex flex-row items-center  gap-[0.8rem]   ">
        {/* <div className=" w-fit h-full flex flex-row flex-shrink-0 items-center gap-[8px] ">
          <h2 className=" text-[14px] font-inter font-[400] text-[#2B2F32]">
            Show only Against Order
          </h2>
          <SwitchBoxComponent
            value={isTrue_OutOfStock}
            onClick={() => {
              let array = isTrue_OutOfStock
                ? []
                : [
                    {
                      id: "Against Order",
                      content: "Against Order",
                    },
                  ];

              changeHandler(array);
            }}
          />
        </div> */}

        <div className=" w-fit h-full flex flex-row items-center  gap-[0.8rem]   ">
          <ButtonComponent
            onClick={downloadPurchaseDownload}
            className=" w-fit h-[50px] flex flex-row justify-center items-center gap-[0.25rem]"
          >
            <FaDownload />
          </ButtonComponent>
        </div>
      </div>
    </div>
  );
};

export default PurchaseTableToppers;
