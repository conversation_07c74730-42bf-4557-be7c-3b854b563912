import React from "react";
import { purchaseTableConfig } from "../../../Config/TableConfig";
import { useDispatch, useSelector } from "react-redux";
import {
  PurchaseSliceActions,
  purchaseFilterListFetchThunk,
} from "../../../Store/PurchaseSlices/PurchaseSlice";
import TableHeaderEngineeWrapper from "../../../UI_Enginee/TableHeaderEnginee/TableHeaderEngineeWrapper";
import TableHeaderEnginee, {
  parser_table_filter_data,
} from "../../../UI_Enginee/TableHeaderEnginee/TableHeaderEnginee";
import UsePurchase from "../UsePurchase";

const PurchaseTableHeader = () => {
  const dispatch = useDispatch();
  const { filter } = useSelector((state) => {
    return state.purchase;
  });

  const { fetchListWithNewFilter, fetch_list } = UsePurchase();

  const updateState = (key, value) => {
    let updatedState = {
      ...filter?.state,
      [key]: value,
    };
    dispatch(PurchaseSliceActions.updateFilterState(updatedState));

    // fetchListWithNewFilter(updatedState);
    fetch_list(updatedState, null);
  };

  return (
    <>
      <TableHeaderEngineeWrapper className=" w-fit h-[52px] border-b-2 flex flex-row  flex-shrink-0 pe-8 ">
        <div className=" w-[50px] h-full flex flex-row flex-shrink-0 justify-center items-center"></div>

        <TableHeaderEnginee
          table={purchaseTableConfig}
          state={filter?.state}
          updateState={updateState}
        />
      </TableHeaderEngineeWrapper>
    </>
  );
};

export default PurchaseTableHeader;
