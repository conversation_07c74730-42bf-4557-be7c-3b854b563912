import React from "react";
import PurchaseTableToppers from "./PurchaseTableToppers";
import PurchaseTableHeader from "./PurchaseTableHeader";
import PurchaseTableListing from "./PurchaseTableListing";

const PurchaseTableLayout = () => {
  return (
    <>
      <div className=" flex-col flex-shrink-0 flex">
        <PurchaseTableToppers />
        <div className=" w-full h-fit flex flex-col overflow-auto ">
          <div className=" w-full h-fit  flex flex-col ">
            <PurchaseTableHeader />
            <PurchaseTableListing />
          </div>
        </div>
      </div>
    </>
  );
};

export default PurchaseTableLayout;
