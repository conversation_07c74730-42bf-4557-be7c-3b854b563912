import React from "react";
import { purchaseTableConfig } from "../../../Config/TableConfig";
import TableTextContent from "../../Utils/TableStructures/TableTextContent";
import edit from "../../../Assest/Utils/edit.png";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import UseRouterAuthentication from "../../../Hooks/UseRouterAuthentication";
import { useNavigate } from "react-router-dom";
import { routes } from "../../../Config/routesConfig";

const dateFormatFunction = (dt) => {
  let date = new Date(dt);

  let fullyear = date.getFullYear();
  let month =
    date.getMonth() + 1 < 10
      ? "0" + (date.getMonth() + 1)
      : date.getMonth() + 1;

  let dateNO = date.getDate();

  let format = fullyear + "-" + month + "-" + dateNO;

  return format;
};

const PurchaseTableIndivRow = ({ content = {} }) => {
  const { routeAuthentication } = UseRouterAuthentication();
  const navigate = useNavigate();
  //-------------------------------------------
  let productName = content?.productDetails;
  let batch = content?.batchNo;
  let quantity = content?.quantity;
  let material = content?.material;
  let division = content?.division;
  let sizeText = content?.size;
  let category = content?.category;
  let subCategory = content?.subCategory;
  let brand = content?.brand;
  let grade = {
    text: content?.grade,
    className: "font-[600] font-inter text-[14px] text-[#000]",
  };
  let warehousestatus = {
    text: content?.warehouseStatus,
    className:
      " w-[130px] h-[28px] bg-[#CFD3D433] rounded-[8px] font-inter font-[400] text-[12px] text-[#6E7079] flex flex-rwo justify-center items-center",
  };

  let vehicleNumber = content?.vehicleNumber;
  let natureOfOrders = {
    text: content?.Nature_of_Order,
    className:
      " w-[130px] h-[28px] bg-[#CFD3D433] rounded-[8px] font-inter font-[400] text-[12px] text-[#6E7079] flex flex-rwo justify-center items-center",
  };
  let unLoadingDate = dateFormatFunction(content?.unloadingDate);
  let ETA = dateFormatFunction(content?.ETA);
  let purchaseOrder = content?.purchaseOrder;

  const editClickHandler = () => {
    navigate(
      "?page=" + routes?.purchaseEdit?.searchParams + "&id=" + content?._id
    );
  };

  return (
    <div
      className=" w-fit h-[52px] border-b-2 flex flex-row hover:bg-[#AB19171A] hover:bg-opacity-10 "
      key={content?._id}
    >
      <div className=" w-[50px] h-full"></div>
      <TableTextContent
        size={purchaseTableConfig[0].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {productName}
      </TableTextContent>

      <TableTextContent
        size={purchaseTableConfig[1].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {batch}
      </TableTextContent>
      <TableTextContent
        size={purchaseTableConfig[2].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {quantity}
      </TableTextContent>
      <TableTextContent
        size={purchaseTableConfig[3].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {material}
      </TableTextContent>
      <TableTextContent
        size={purchaseTableConfig[4].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {division}
      </TableTextContent>
      <TableTextContent
        size={purchaseTableConfig[5].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {sizeText}
      </TableTextContent>
      <TableTextContent
        size={purchaseTableConfig[6].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {category}
      </TableTextContent>
      <TableTextContent
        size={purchaseTableConfig[7].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {subCategory}
      </TableTextContent>
      <TableTextContent
        size={purchaseTableConfig[8].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {brand}
      </TableTextContent>
      <TableTextContent
        size={purchaseTableConfig[9].className}
        className={grade.className}
      >
        {grade.text}
      </TableTextContent>
      <TableTextContent
        size={purchaseTableConfig[10].className}
        className=" font-[600] font-inter text-[14px] text-[#000]"
      >
        <p className={warehousestatus.className}>{warehousestatus.text}</p>
      </TableTextContent>
      <TableTextContent
        size={purchaseTableConfig[11].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {vehicleNumber}
      </TableTextContent>
      <TableTextContent
        size={purchaseTableConfig[12].className}
        className=" font-[600] font-inter text-[14px] text-[#000]"
      >
        <p className={natureOfOrders.className}>{natureOfOrders.text}</p>
      </TableTextContent>
      <TableTextContent
        size={purchaseTableConfig[13].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {unLoadingDate}
      </TableTextContent>
      <TableTextContent
        size={purchaseTableConfig[14].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {ETA}
      </TableTextContent>

      {/* <div className=" w-[60px] h-full flex flex-row justify-center items-center">
        {routeAuthentication("serviceComplaints", "edit") && (
          <ButtonComponent
            onClick={editClickHandler}
            className=" w-[24px] h-[24px] flex flex-rwo justify-center items-center "
          >
            <img
              src={edit}
              className=" w-full h-full object-contain"
              alt="edit"
            />
          </ButtonComponent>
        )}
      </div> */}
    </div>
  );
};

export default PurchaseTableIndivRow;
