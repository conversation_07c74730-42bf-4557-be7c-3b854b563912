import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useSearchParams } from "react-router-dom";
import UseRouterAuthentication from "../../../Hooks/UseRouterAuthentication";
import { routes } from "../../../Config/routesConfig";
import ModelHeaderAndRoute from "../../Utils/UtilsComponents/ModelHeaderAndRoute";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import UsePurchase from "../UsePurchase";
import {
  formData_parser,
  formEnginee_edit_Formatee_Structure_and_State,
} from "../../../Dashboard_API_calling_and_formatting/Form_Formate_and_API";
import { PurchaseSliceActions } from "../../../Store/PurchaseSlices/PurchaseSlice";
import FormEnginee from "../../../UI_Enginee/Form/FormEnginee";
import { API_ENDPOINTS, api_headers, BaseURL } from "../../../API/APIConfig";
import { PostAPI } from "../../../API/PostAPI";

const PurchaseEditLayout = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { routeAuthentication } = UseRouterAuthentication();
  const { auth, access } = useSelector((state) => {
    return state.master;
  });

  useEffect(() => {
    if (!routeAuthentication("purchase", "edit")) {
      navigate(routes?.purchase?.directLink);
    }
  }, [auth, access]);

  const { edit } = useSelector((state) => {
    return state.purchase;
  });

  //--------------------------------------------

  const { fetchList, fetchEditFormWithId } = UsePurchase();
  const [searchParams] = useSearchParams();

  // state
  const [state, setState] = useState({});
  const [isContentLoading, setIsContentLoading] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const closeModal = () => {
    navigate(routes?.purchase?.directLink);
  };

  useEffect(() => {
    let id = searchParams?.get("id");

    let fetch = async () => {
      setIsContentLoading(true);

      let result = await fetchEditFormWithId(id);

      if (!result) {
        // window?.confirm(
        //   "Error at fetching data, please check out network and try again"
        // );
        closeModal();
        return;
      }

      let ss = formEnginee_edit_Formatee_Structure_and_State(result);
      setState(ss.state);

      setTimeout(() => {
        dispatch(
          PurchaseSliceActions.updateEditStructureAndState({
            ...ss,
            id: id,
          })
        );
      }, 1);

      setIsContentLoading(false);

      if (ss.structure?.length === 0) {
        return false;
      } else {
        return true;
      }
    };

    fetch();
  }, [edit.id]);

  const updateFunctionOfForm = (key, value) => {
    setState((state) => {
      return { ...state, [key]: { ...state[key], value: value } };
    });
  };

  const clickHandler = async () => {
    let parser_obj = formData_parser(edit.structure, state);

    setIsLoading(true);

    let response = await PurchaseEdit(
      parser_obj?.parser_obj,
      searchParams?.get("id"),
      auth
    );

    if (response) {
      fetchList();
      closeModal();
    }
    setIsLoading(false);
  };

  return (
    <div className=" md:w-[424px] w-[85%] max-h-[95vh] h-fit overflow-y-auto bg-[#fff] rounded-[6px] p-4 pb-[4rem]">
      <ModelHeaderAndRoute
        className="w-full h-fit flex flex-row justify-between ps-4 pe-4 "
        title="Edit The Purchase"
        onClick={() => {
          closeModal();
        }}
      />

      <FormEnginee
        form={edit?.structure}
        formStateFunction={updateFunctionOfForm}
        formState={state}
      />

      <div className=" w-full h-fit mt-[1rem]">
        <div className=" w-full h-fit flex flex-row justify-center gap-[1rem]">
          <ButtonComponent
            onClick={() => {
              navigate(-1);
            }}
            className=" w-[45%] h-[58px] rounded-[6px] border-[#00BD94] border-2 bg-clip-text text-transparent bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] flex flex-row justify-center items-center"
          >
            Cancel
          </ButtonComponent>
          <ButtonComponent
            isLoading={isLoading}
            onClick={clickHandler}
            className=" w-[45%] h-[58px] rounded-[6px] bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] text-[#fff] flex flex-row justify-center items-center"
          >
            Edit Complaint
          </ButtonComponent>
        </div>
      </div>
    </div>
  );
};

export default PurchaseEditLayout;

// Edit API
const PurchaseEdit = async (content, id, auth) => {
  let api = new URL(BaseURL.mainURl + API_ENDPOINTS.purchaseEdit + "/" + id);

  let obj = {
    ...content,
  };

  const header = {
    ...api_headers,
    Authorization: auth.token,
  };

  let response = await PostAPI(api, obj, header, "PUT");

  if (response.ok) {
    return true;
  }

  return false;
};
