import { API_ENDPOINTS, api_headers, BaseURL } from "../../API/APIConfig";
import { GetAPI } from "../../API/GetAPI";
import { useDispatch, useSelector } from "react-redux";
import { PurchaseSliceActions } from "../../Store/PurchaseSlices/PurchaseSlice";
import { parser_table_filter_data } from "../../UI_Enginee/TableHeaderEnginee/TableHeaderEnginee";
import { purchaseTableConfig } from "../../Config/TableConfig";
import { PostAPI } from "../../API/PostAPI";
import { getStartAndEndDate } from "../../Config/DateFilterConfig";

export let dateObj = [
  {
    id: "Last year",
    content: "Last year",
  },
  {
    id: "Last Month",
    content: "Last Month",
  },
  {
    id: "Last Week",
    content: "Last Week",
  },
  {
    id: "Custom Date",
    content: "Custom Date",
  },
  {
    id: "Till now",
    content: "Till now",
  },
];

const UsePurchase = () => {
  const dispatch = useDispatch();
  const master = useSelector((state) => {
    return state.master;
  });

  let auth = master?.auth;

  const { filter, analytics } = useSelector((state) => {
    return state.purchase;
  });

  const fetchList = () => {
    fetch_list();
    return null;
    if (!auth?.id) {
      return;
    }

    let filterObj = parser_table_filter_data(purchaseTableConfig, filter.state);

    if (filterObj?.isFilter) {
      dispatch(fetch_list_with_filter_thunk(filterObj?.state));
    } else {
      dispatch(fetch_list_thunk());
    }
  };

  const fetchListWithNewFilter = (filterObj) => {
    fetch_list(filterObj, null);
    if (!auth?.id) {
      return;
    }

    filterObj = parser_table_filter_data(purchaseTableConfig, filterObj);

    if (filterObj?.isFilter) {
      dispatch(fetch_list_with_filter_thunk(filterObj?.state));
    } else {
      dispatch(fetch_list_thunk());
    }
  };

  // fetch_list
  const fetch_list = (updated_filter_state = null, updated_date = null) => {
    if (!auth?.id) {
      return;
    }

    let filterObj = parser_table_filter_data(purchaseTableConfig, filter.state);
    let filterState = filterObj?.state;
    let isFilter = filterObj?.isFilter;

    let customDate = "Custom Date";
    let startDate = filter?.date?.startDate;
    let endDate = filter?.date?.endDate;
    let filter_options = filter?.date?.filter?.content;

    if (updated_filter_state) {
      let filterState_parser = parser_table_filter_data(
        purchaseTableConfig,
        updated_filter_state
      );
      filterState = filterState_parser?.state;
      isFilter = filterState_parser?.isFilter;
    }

    if (updated_date) {
      startDate = updated_date?.startDate;
      endDate = updated_date?.endDate;
      filter_options = updated_date?.filter?.content;
    }

    // console.log("----------------------------");
    // console.log(filterState, isFilter);
    // console.log(startDate, "---", endDate, "---", filter_options);

    if (isFilter) {
      dispatch(fetch_list_with_filter_thunk(filterState));
    } else {
      dispatch(fetch_list_thunk());
    }
  };

  // Add form builder Fetch
  const fetchAddFormEnginee = async () => {
    let url = new URL(BaseURL?.mainURl + API_ENDPOINTS?.purchaseAddFormBuilder);

    let headers = { ...api_headers };

    let response = await GetAPI(url, headers);

    if (response?.ok) {
      let responseData = await response?.json();

      return responseData?.structure;
    }

    return [];
  };

  // Edit FORM Builder API
  const fetchEditFormWithId = async (id) => {
    let url = new URL(
      BaseURL?.mainURl + API_ENDPOINTS?.purchaseEditFormBuilder
    );

    let headers = { ...api_headers };

    const response = await PostAPI(url, { id: id }, headers);

    if (response?.ok) {
      let responsedata = await response?.json();
      return responsedata?.structure;
    }

    return null;
  };

  const fetchAnalytics = (obj = null) => {
    if (!auth?.id) {
      return;
    }

    let customDate = "Custom Date";
    let startDate = analytics?.startDate;
    let endDate = analytics?.endDate;
    let filter = analytics?.filter?.content;
    let brandVsSizeOptions = analytics?.brandVsSizeOptions;

    if (obj) {
      customDate = "Custom Date";
      startDate = obj?.startDate;
      endDate = obj?.endDate;
      filter = obj?.filter?.content;
      brandVsSizeOptions = obj?.brandVsSizeOptions;
    }

    if (filter === "Last year") {
      let [startD, endD] = getStartAndEndDate(365);
      startDate = new Date(startD);
      endDate = new Date(endD);
    } else if (filter === "Last Month") {
      let [startD, endD] = getStartAndEndDate(30);
      startDate = new Date(startD);
      endDate = new Date(endD);
    } else if (filter === "Last Week") {
      let [startD, endD] = getStartAndEndDate(7);
      startDate = new Date(startD);
      endDate = new Date(endD);
    } else if (filter === "Till now") {
      let [startD, endD] = getStartAndEndDate(3650 + 3650);
      startDate = new Date(startD);
      endDate = new Date(endD);
    } else if (filter === customDate) {
    }

    dispatch(fetch_analytics_thunk(startDate, endDate, brandVsSizeOptions?.id));
  };

  return {
    fetch_list,
    fetchList,
    fetchListWithNewFilter,
    fetchAddFormEnginee,
    fetchEditFormWithId,
    fetchAnalytics,
  };
};

export default UsePurchase;

export const fetch_list_thunk = () => {
  return async (dispatch) => {
    let url = new URL(BaseURL.mainURl + API_ENDPOINTS.purchaseListing);

    let headers = {
      ...api_headers,
    };

    let response = await GetAPI(url, headers);

    if (response?.ok) {
      let responseData = await response?.json();
      let data = responseData?.data.orders;

      dispatch(PurchaseSliceActions.updatePurchaseList(data));
    }
  };
};

export const fetch_list_with_filter_thunk = (obj) => {
  return async (dispatch) => {
    let url = new URL(BaseURL.mainURl + API_ENDPOINTS.purchaseFilterList);

    let headers = {
      ...api_headers,
    };

    let body = obj;

    let response = await PostAPI(url, body, headers);

    if (response?.ok) {
      let responsedata = await response?.json();

      dispatch(
        PurchaseSliceActions?.updatePurchaseList(responsedata?.filteredOrders)
      );
    }
  };
};

//Analytics
function formatDateToCustomString(dateObj) {
  dateObj = new Date(dateObj);
  // Extract year, month, and day
  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, "0"); // Months are zero-indexed
  const day = String(dateObj.getDate()).padStart(2, "0");

  // Format the date string as "YYYY-MM-DD"
  return `${year}-${month}-${day}`;
}

const fetch_analytics_thunk = (startDate, endDate, category = "") => {
  return async (dispatch) => {
    let url = new URL(
      BaseURL?.mainURl + API_ENDPOINTS?.purchaseAnalyticsFilter
    );

    url =
      url.href +
      "?startDate=" +
      formatDateToCustomString(new Date(startDate)) +
      "&tillDate=" +
      formatDateToCustomString(new Date(endDate));

    let headers = { ...api_headers };

    let response = await GetAPI(url, headers);

    //-----------------------------------------------

    let url2 = new URL(
      BaseURL?.mainURl + API_ENDPOINTS?.purchaseAnalyticsBrandVsSize
    );

    url2 =
      url2.href +
      "?startDate=" +
      formatDateToCustomString(new Date(startDate)) +
      "&tillDate=" +
      formatDateToCustomString(new Date(endDate)) +
      "&category=" +
      category;

    let response2 = await GetAPI(url2, headers);

    let brandVsSize = {
      brand: [],
      size: [],
      quantity: [],
    };

    if (response2?.ok) {
      let responsedata = await response2.json();

      let brandObj = {};
      let brandarray = [];

      let sizeObj = {};
      let sizeArray = [];

      let quantity = [];

      for (let item of responsedata) {
        let brandString = item?._id?.brand;
        if (brandString in brandObj) {
        } else {
          brandObj[brandString] = "afd";
          brandarray.push(brandString);
        }

        let sizeString = item?._id?.size;
        if (sizeString in brandObj) {
        } else {
          sizeObj[sizeString] = "afd";
          sizeArray.push(sizeString);
        }
      }

      for (let item of brandarray) {
        let array = [];
        for (let i of sizeArray) {
          array.push(0);
        }
        quantity?.push(array);
      }

      for (let i = 0; i < brandarray?.length; i++) {
        let brandItem = brandarray[i];
        for (let j = 0; j < sizeArray?.length; j++) {
          let sizeItem = sizeArray[j];
          for (let item of responsedata) {
            let brandString = item?._id?.brand;
            let sizeString = item?._id?.size;
            if (brandString === brandItem && sizeString === sizeItem) {
              quantity[i][j] = item?.totalQuantity;
            }
          }
        }
      }

      let newQuantity = [];
      for (let i = 0; i < sizeArray?.length; i++) {
        let array = quantity.map((item) => item[i]);
        newQuantity?.push(array);
      }

      brandVsSize.brand = brandarray;
      brandVsSize.size = sizeArray;
      brandVsSize.quantity = newQuantity;
    }

    //-----------------------------------------------

    if (response?.ok) {
      let responseData = await response?.json();

      let obj = {
        OverallStockPurchased:
          responseData?.OverallStockPurchased?.length > 0
            ? responseData?.OverallStockPurchased[0]?.StockPurchased
            : 0,

        OverallStockSold:
          responseData?.OverallStockSold?.length > 0
            ? responseData?.OverallStockSold[0]?.StockSold
            : 0,

        DelayedCommitments:
          responseData?.DelayedCommitments?.length > 0
            ? responseData?.DelayedCommitments[0]?.count
            : 0,

        AverageStockDeliveryTime: responseData?.AverageStockDeliveryTime,

        OnTimeDeliveryDelayedCommitments: {
          OnTimeDelivery:
            responseData?.OnTimeDelivery?.length > 0
              ? responseData?.OnTimeDelivery[0]?.Delivery
              : 0,

          DelayedCommitments:
            responseData?.DelayedCommitments?.length > 0
              ? responseData?.DelayedCommitments[0]?.count
              : 0,
        },

        NumberofTrucks:
          responseData?.NumberofTrucks?.length > 0
            ? responseData?.NumberofTrucks[0]?.Trucks
            : 0,

        MaterialsUnloaded:
          responseData?.MaterialsUnloaded?.length > 0
            ? responseData?.MaterialsUnloaded[0]?.Unloaded
            : 0,

        PurchaseEntry:
          responseData?.PurchaseEntry?.length > 0
            ? responseData?.PurchaseEntry[0]?.Entry
            : 0,

        PurchaseAgainstOrder:
          responseData?.PurchaseAgainstOrder?.length > 0
            ? responseData?.PurchaseAgainstOrder[0]?.AgainstOrder
            : 0,

        MSLVsOutofStockVsDamaged: {
          Damage: responseData?.MSLVsOutofStockVsDamaged?.map(
            (item) => item?.Damage
          ),
          MSL: responseData?.MSLVsOutofStockVsDamaged?.map((item) => item?.MSL),
          OutofStock: responseData?.MSLVsOutofStockVsDamaged?.map(
            (item) => item?.OutofStock
          ),
          OutofStock: responseData?.MSLVsOutofStockVsDamaged?.map(
            (item) => item?.OutofStock
          ),
          B: responseData?.MSLVsOutofStockVsDamaged?.map((item) => item?.B),
          c: responseData?.MSLVsOutofStockVsDamaged?.map((item) => item?.c),
          unloadingDate: responseData?.MSLVsOutofStockVsDamaged?.map(
            (item) => item?.unloadingDate
          ),
        },

        StockPurchasedVsStockSold: {
          StockPurchased: responseData?.StockPurchasedVsStockSold?.map(
            (item) => item?.StockPurchased
          ),
          StockSold: responseData?.StockPurchasedVsStockSold?.map(
            (item) => item?.StockSold
          ),
          unloadingDate: responseData?.StockPurchasedVsStockSold?.map(
            (item) => item?.unloadingDate
          ),
        },

        topProducts: {
          id: responseData?.topProducts?.map((item) => item?._id),
          totalQuantity: responseData?.topProducts?.map(
            (item) => item?.totalQuantity
          ),
        },

        topBatches: {
          id: responseData?.topBatches?.map((item) => item?._id),
          totalQuantity: responseData?.topBatches?.map(
            (item) => item?.totalQuantity
          ),
        },

        topMaterials: {
          id: responseData?.topMaterials?.map((item) => item?._id),
          totalQuantity: responseData?.topMaterials?.map(
            (item) => item?.totalQuantity
          ),
        },

        topDivisions: {
          id: responseData?.topDivisions?.map((item) => item?._id),
          totalQuantity: responseData?.topDivisions?.map(
            (item) => item?.totalQuantity
          ),
        },

        topSizes: {
          id: responseData?.topSizes?.map((item) => item?._id),
          totalQuantity: responseData?.topSizes?.map(
            (item) => item?.totalQuantity
          ),
        },

        topCategories: {
          id: responseData?.topCategories?.map((item) => item?._id),
          totalQuantity: responseData?.topCategories?.map(
            (item) => item?.totalQuantity
          ),
        },

        topSubCategories: {
          id: responseData?.topSubCategories?.map((item) => item?._id),
          totalQuantity: responseData?.topSubCategories?.map(
            (item) => item?.totalQuantity
          ),
        },

        topBrands: {
          id: responseData?.topBrands?.map((item) => item?._id),
          totalQuantity: responseData?.topBrands?.map(
            (item) => item?.totalQuantity
          ),
        },

        topGrades: {
          id: responseData?.topGrades?.map((item) => item?._id),
          totalQuantity: responseData?.topGrades?.map(
            (item) => item?.totalQuantity
          ),
        },

        topWarehouseStatus: {
          id: responseData?.topWarehouseStatus?.map((item) => item?._id),
          totalQuantity: responseData?.topWarehouseStatus?.map(
            (item) => item?.totalQuantity
          ),
        },

        topNatureOfOrders: {
          id: responseData?.topNatureOfOrders?.map((item) => item?._id),
          totalQuantity: responseData?.topNatureOfOrders?.map(
            (item) => item?.totalQuantity
          ),
        },

        brandVsSize,
      };

      dispatch(PurchaseSliceActions?.updateAnalyticsContent(obj));
    }
  };
};
