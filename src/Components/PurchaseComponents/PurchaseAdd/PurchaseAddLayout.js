import React, { useEffect, useState } from "react";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import ModelHeaderAndRoute from "../../Utils/UtilsComponents/ModelHeaderAndRoute";
import { useNavigate } from "react-router-dom";
import { routes } from "../../../Config/routesConfig";
import { useDispatch, useSelector } from "react-redux";
import FormEnginee from "../../../UI_Enginee/Form/FormEnginee";
import { purchase_form_config } from "../../../Config/TableConfig";
import { PurchaseSliceActions } from "../../../Store/PurchaseSlices/PurchaseSlice";
import {
  formData_parser,
  formEnginee_Formatee_Structure_and_State,
} from "../../../Dashboard_API_calling_and_formatting/Form_Formate_and_API";
import { vaildator_Form_function } from "../../../UI_Enginee/Form/FormEngineeConfig";
import UsePurchase from "../UsePurchase";

const PurchaseAddLayout = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { add } = useSelector((state) => {
    return state.purchase;
  });

  const { auth, access } = useSelector((state) => {
    return state.master;
  });

  const { fetchAddFormEnginee } = UsePurchase();

  //---------------
  const [state, setState] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  const fetch = async () => {
    let response = await fetchAddFormEnginee();
    let ss = formEnginee_Formatee_Structure_and_State(response);
    setState(ss.state);
    dispatch(PurchaseSliceActions.updateStructureAndState(ss));

    if (response?.ok) {
      return true;
    }

    return false;
  };

  useEffect(() => {
    // Setting structure and state if not added
    if (add.structure?.length === 0) {
      // dispatch(sales_load_addForm_thunk());
      if (fetch()) {
      } else {
        window.alert("Please check the network and refresh");
      }
    } else {
      setState(add.state);
    }
  }, [add.state]);

  const updateFunctionOfForm = (key, value) => {
    // Updating the state
    let updateState = {
      ...state,
      [key]: { ...state[key], value: value },
    };
    setState(updateState);
  };
  //---------------

  // Submit Handler
  const submitHandler = async () => {
    let vaildate = vaildator_Form_function(add.structure, state);

    dispatch(PurchaseSliceActions?.updateState(vaildate?.validate_Obj));
    setState(vaildate?.validate_Obj);
    if (!vaildate?.isFormValid) {
      return;
    }

    let parser_obj = formData_parser(add.structure, state);

    let obj = {
      ...parser_obj.parser_obj,
      assignedBy: auth?.id,
      // lead_status: "New",
    };

    setIsLoading(true);
    // let response = await salesAndMarketingAddALeadFunction(obj, auth);
    setIsLoading(false);

    // if (response?.ok) {
    //   fetchList();
    //   fetch();
    //   closeModal();
    // }
  };

  const closeModal = () => {
    navigate(routes?.servicecomplaints?.directLink);
  };
  return (
    <div className=" md:w-[424px] w-[85%] max-h-[95vh] h-fit overflow-y-auto bg-[#fff] rounded-[6px] p-4 pb-[4rem] duration-200 ">
      <ModelHeaderAndRoute
        className="w-full h-fit flex flex-row justify-between ps-4 pe-4 "
        title="Add a Purchase"
        onClick={() => {
          closeModal();
        }}
      />

      <FormEnginee
        form={add.structure}
        formState={state}
        formStateFunction={updateFunctionOfForm}
      />

      <div className=" w-full h-fit mt-[1rem]">
        <div className=" w-full h-fit flex flex-row justify-center gap-[1rem]">
          <ButtonComponent
            onClick={() => {
              closeModal();
            }}
            className=" w-[45%] h-[58px] rounded-[6px] border-[#00BD94] border-2 bg-clip-text text-transparent bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] flex flex-row justify-center items-center"
          >
            Cancel
          </ButtonComponent>
          <ButtonComponent
            isLoading={isLoading}
            onClick={submitHandler}
            className=" w-[45%] h-[58px] rounded-[6px] bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] text-[#fff] flex flex-row justify-center items-center"
          >
            Add Purchase
          </ButtonComponent>
        </div>
      </div>
    </div>
  );
};

export default PurchaseAddLayout;
