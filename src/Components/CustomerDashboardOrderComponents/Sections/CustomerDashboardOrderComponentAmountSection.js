import React from "react";
import DataStructureBoxHeader from "../../Utils/DataStuctureBoxes/DataStructureBoxHeader";
import IndivSoloCustomer from "../../SoloCustomersComponents/Utils/IndivSoloCustomer";
import pie from "../../../Assest/Utils/pie.png";

const CustomerDashboardOrderComponentAmountSection = ({ content = 0 }) => {
  return (
    <div className=" w-[330px] h-[145px] flex flex-col justify-between border-2 p-3 rounded-[10px]">
      <DataStructureBoxHeader logo={pie}></DataStructureBoxHeader>

      <div className=" w-full h-[55px] flex flex-row justify-between ">
        <IndivSoloCustomer heading="Total Orders" stat={"₹" + content} />
        <div></div>
      </div>
    </div>
  );
};

export default CustomerDashboardOrderComponentAmountSection;
