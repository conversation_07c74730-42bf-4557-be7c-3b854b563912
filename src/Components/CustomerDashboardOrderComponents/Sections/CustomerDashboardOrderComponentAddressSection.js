import React from "react";
import DataStructureBoxHeader from "../../Utils/DataStuctureBoxes/DataStructureBoxHeader";
import IndivSoloCustomer from "../../SoloCustomersComponents/Utils/IndivSoloCustomer";
import location from "../../../Assest/Utils/Location.png";

const CustomerDashboardOrderComponentAddressSection = ({ auth = "-" }) => {
  return (
    <div className=" w-[530px] h-[145px] flex flex-col justify-between border-2 p-3 rounded-[10px]">
      <DataStructureBoxHeader logo={location} />
      <div className=" w-full h-[55px] flex flex-row justify-between ">
        <IndivSoloCustomer heading="Phone" stat="-" />
        <IndivSoloCustomer heading="Email" stat={auth?.email} />
        <div></div>
      </div>
    </div>
  );
};

export default CustomerDashboardOrderComponentAddressSection;
