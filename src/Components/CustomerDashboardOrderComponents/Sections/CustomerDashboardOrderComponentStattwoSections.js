import React from "react";
import DataStructureBoxHeader from "../../Utils/DataStuctureBoxes/DataStructureBoxHeader";
import IndivIdualDataFieldLayout from "../../Utils/DataStuctureBoxes/IndivIdualDataFieldLayout";
import logo from "../../../Assest/Utils/shoppingwhite.png";

const CustomerDashboardOrderComponentStattwoSections = ({ content = {} }) => {
  return (
    <div className=" w-[322px] h-[145px] flex flex-col flex-shrink-0 justify-between border-2 p-3 rounded-[10px]">
      <DataStructureBoxHeader logo={logo}></DataStructureBoxHeader>

      <div className=" w-full h-[55px] flex flex-row justify-between ">
        <IndivIdualDataFieldLayout
          heading="Out Stock"
          stat={content?.out_of_stock}
          statColor="red"
        />

        <div></div>
      </div>
    </div>
  );
};

export default CustomerDashboardOrderComponentStattwoSections;
