import React from "react";
import IndivSoloCustomer from "../../SoloCustomersComponents/Utils/IndivSoloCustomer";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import DataStructureBoxImageContainer from "../../Utils/DataStuctureBoxes/Utils/DataStructureBoxImageContainer";
import userimage from "../../../Assest/Utils/UserLogo.png";

const CustomerDashboardOrderComponentProfileSection = ({ auth = {} }) => {
  return (
    <div className=" w-[410px] h-[145px] flex flex-col justify-between border-2 p-2 rounded-[10px]">
      <div className=" w-full h-[125px] flex flex-row justify-between p-2 ">
        <div className=" w-fit h-full flex flex-row gap-[1rem]">
          <DataStructureBoxImageContainer logo={userimage} />
          <div className="flex flex-col gap-[0.1rem]">
            <h1 className=" font-inter font-[400] text-[14px] text-[#05AF9B]">
              {auth?.id}
            </h1>
            <h3 className=" font-inter font-[400] text-[14px] text-[#000]">
              {auth?.username}
            </h3>
            <div className=" flex flex-row font-poppins text-[12px] gap-[0.2rem]  ">
              <p className=" text-[#8B8D97]">Last Order</p>
              {/* <p> 12 Sept 2022</p> */}
            </div>
          </div>
        </div>

        <div>
          <ButtonComponent className=" w-[58px] h-[23px] bg-[#32936F29] rounded-[6px] font-inter font-[400] text-[12px] text-[#519C66]">
            Active
          </ButtonComponent>
        </div>
      </div>
      <div className=" w-full h-[55px] flex flex-row justify-between ">
        <IndivSoloCustomer heading="Phone" stat="-" />
        <IndivSoloCustomer heading="Email" stat={auth?.email} />
        <div></div>
      </div>
    </div>
  );
};

export default CustomerDashboardOrderComponentProfileSection;
