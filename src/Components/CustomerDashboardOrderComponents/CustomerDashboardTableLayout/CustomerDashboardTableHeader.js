import React from "react";
import TableHeaderEngineeWrapper from "../../../UI_Enginee/TableHeaderEnginee/TableHeaderEngineeWrapper";
import { customerDashboardTable } from "../CustomerDashboardOrderComponentsLayout";
import TableHeaderEnginee from "../../../UI_Enginee/TableHeaderEnginee/TableHeaderEnginee";

const CustomerDashboardTableHeader = () => {
  return (
    <TableHeaderEngineeWrapper className="w-fit h-[52px] border-b-2 flex flex-row">
      <div className=" w-[60px] h-full flex flex-row justify-center items-center"></div>
      <TableHeaderEnginee
        table={customerDashboardTable}
        // state={filter?.state}
        // updateState={updateState}
      />
    </TableHeaderEngineeWrapper>
  );
};

export default CustomerDashboardTableHeader;
