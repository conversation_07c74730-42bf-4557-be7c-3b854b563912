import React from "react";
import CustomerDashboardTableRow from "./CustomerDashboardTableRow";
import { useSelector } from "react-redux";

const CustomerDashboardTableListing = () => {
  const { list } = useSelector((state) => {
    return state.customerDashboardOrders;
  });
  return (
    <div className=" w-fit h-fit">
      {list.map((item) => {
        return <CustomerDashboardTableRow content={item} />;
      })}
    </div>
  );
};

export default CustomerDashboardTableListing;
