import React from "react";
import CustomerDashboardTableToppers from "./CustomerDashboardTableToppers";
import CustomerDashboardTableHeader from "./CustomerDashboardTableHeader";
import CustomerDashboardTableListing from "./CustomerDashboardTableListing";

const CustomerDashboardTableLayout = () => {
  return (
    <>
      <div className=" hidden flex-col flex-shrink-0 md:flex mt-[2rem]">
        <CustomerDashboardTableToppers />
        <div className=" w-full h-fit flex flex-col ">
          <CustomerDashboardTableHeader />
          <CustomerDashboardTableListing />
        </div>
      </div>
      <div className=" flex flex-col flex-shrink-0 md:hidden mt-[2rem]">
        <CustomerDashboardTableToppers />
        <div className=" w-full h-fit flex flex-col ">
          <CustomerDashboardTableHeader />
          <CustomerDashboardTableListing />
        </div>
      </div>
    </>
  );
};

export default CustomerDashboardTableLayout;
