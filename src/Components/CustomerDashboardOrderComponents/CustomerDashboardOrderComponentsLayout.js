import React, { useEffect } from "react";
import CustomerDashboardOrderComponentProfileSection from "./Sections/CustomerDashboardOrderComponentProfileSection";
import CustomerDashboardOrderComponentAddressSection from "./Sections/CustomerDashboardOrderComponentAddressSection";
import CustomerDashboardOrderComponentAmountSection from "./Sections/CustomerDashboardOrderComponentAmountSection";
import CustomerDashboardOrderComponentStatoneSections from "./Sections/CustomerDashboardOrderComponentStatoneSections";
import CustomerDashboardOrderComponentStattwoSections from "./Sections/CustomerDashboardOrderComponentStattwoSections";
import { useDispatch, useSelector } from "react-redux";
import { fetch_customer_dashboard_order_data_thunk } from "../../Store/CustomerDashboardSlices/CustomerDashboardOrderSlice";
import CustomerDashboardTableLayout from "./CustomerDashboardTableLayout/CustomerDashboardTableLayout";

// Customer Dashboard table header :- config
export const customerDashboardTable = [
  {
    title: "Bill Id",
    headertype: "display",
    key: "",
    listtype: "display",
    className: " ms-[1rem] w-[180px] h-full",
  },
  {
    title: "Order Id",
    headertype: "display",
    key: "",
    listtype: "display",
    className: " ms-[1rem] w-[180px] h-full",
  },
  {
    title: "Order Date",
    headertype: "display",
    key: "",
    listtype: "display",
    className: " ms-[1rem] w-[180px] h-full",
  },
  {
    title: "Product Details",
    headertype: "display",
    key: "",
    listtype: "display",
    className: " ms-[1rem] w-[180px] h-full ",
  },
  {
    title: "Quantity",
    headertype: "display",
    key: "",
    listtype: "display",
    className: " ms-[1rem] w-[200px] h-full ms-[1rem] ps-[70px]",
  },
  {
    title: "Stock Availability",
    headertype: "display",
    key: "",
    listtype: "display",
    className: " ms-[1rem] w-[180px] h-full",
  },
  {
    title: "Order Total",
    headertype: "display",
    key: "",
    listtype: "display",
    className: " ms-[1rem] w-[150px] h-full",
  },
  {
    title: "Order Status",
    headertype: "display",
    key: "",
    listtype: "display",
    className: " ms-[1rem] w-[180px] h-full",
  },
  {
    title: "Sales Person",
    headertype: "display",
    key: "",
    listtype: "display",
    className: " ms-[1rem] w-[180px] h-full",
  },
  {
    title: "Delivery Date",
    headertype: "display",
    key: "",
    listtype: "display",
    className: " ms-[1rem] w-[180px] h-full",
  },
];

const CustomerDashboardOrderComponentsLayout = () => {
  const dispatch = useDispatch();

  const { auth } = useSelector((state) => {
    return state?.master;
  });

  const { headerData } = useSelector((state) => {
    return state?.customerDashboardOrders;
  });

  useEffect(() => {
    dispatch(fetch_customer_dashboard_order_data_thunk(auth?.id));
  }, [auth]);

  return (
    <div className=" w-full h-full overflow-y-auto">
      <div className=" w-full mt-[2rem] ps-[1rem] overflow-x-auto flex flex-row flex-shrink-0 pb-[1rem] gap-[1rem] md:ps-2 md:pe-2 pe-4">
        <CustomerDashboardOrderComponentProfileSection auth={auth} />
        <CustomerDashboardOrderComponentAddressSection auth={auth} />
        <CustomerDashboardOrderComponentAmountSection
          content={headerData?.orders_total}
        />
      </div>
      <div className=" w-full h-fit overflow-x-auto mt-[2rem] ps-[1rem] flex flex-row flex-shrink-0 pb-[1rem] gap-[1rem] md:ps-2 md:pe-2 pe-4">
        <CustomerDashboardOrderComponentStatoneSections content={headerData} />
        <CustomerDashboardOrderComponentStattwoSections content={headerData} />
      </div>

      <div className=" overflow-auto">
        <CustomerDashboardTableLayout />
      </div>
    </div>
  );
};

export default CustomerDashboardOrderComponentsLayout;
