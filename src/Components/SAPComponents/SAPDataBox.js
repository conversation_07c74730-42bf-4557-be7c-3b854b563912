import React from "react";

const SAPDataBox = ({ headerCount = {} }) => {
  const {
    total = 151,
    open = 50,
    inProgress = 2,
    closed = 92
  } = headerCount;
  
  return (
    <div className="max-w-4xl ml-0 border border-gray-200 bg-white rounded-lg p-6">
      <div className="grid grid-cols-4 gap-2 text-center">
        <div>
          <div className="text-gray-600 font-normal text-base mb-1">Total SAP</div>
          <div className="text-gray-800 text-3xl font-medium">{total}</div>
        </div>
        
        <div>
          <div className="text-gray-600 font-normal text-base mb-1">Open</div>
          <div className="text-gray-800 text-3xl font-medium">{open}</div>
        </div>
        
        <div>
          <div className="text-gray-600 font-normal text-base mb-1">In Progress</div>
          <div className="text-gray-800 text-3xl font-medium">{inProgress}</div>
        </div>
        
        <div>
          <div className="text-gray-600 font-normal text-base mb-1">Closed</div>
          <div className="text-gray-800 text-3xl font-medium">{closed}</div>
        </div>
      </div>
    </div>
  );
};

export default SAPDataBox; 