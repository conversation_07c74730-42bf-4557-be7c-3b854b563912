import React, { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import ModelHeaderAndRoute from "../../Utils/UtilsComponents/ModelHeaderAndRoute";
import UseRouterAuthentication from "../../../Hooks/UseRouterAuthentication";
import { routes } from "../../../Config/routesConfig";
import FormEnginee from "../../../UI_Enginee/Form/FormEnginee";
import { formEnginee_edit_Formatee_Structure_and_State } from "../../../Dashboard_API_calling_and_formatting/Form_Formate_and_API";

// When you create the SAPSlice and related hooks, uncomment these
// import { SAPSliceActions } from "../../../Store/SAPSlice/SAPSlice";
// import UseSAP from "../UseSAP";

const SAPEditLayout = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  
  // Uncomment when you have UseSAP hook
  // const { fetchList } = UseSAP();
  
  const { auth } = useSelector((state) => {
    return state.master;
  });

  const [searchParams] = useSearchParams();
  const [state, setState] = useState({});
  const [isContentLoading, setIsContentLoading] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formStructure, setFormStructure] = useState([
    {
      key: "title",
      label: "Title",
      placeholder: "Enter title",
      type: "text",
      required: true,
      value: "",
      error: "",
    },
    {
      key: "description",
      label: "Description",
      placeholder: "Enter description",
      type: "textarea",
      required: true,
      value: "",
      error: "",
    },
    {
      key: "priority",
      label: "Priority",
      placeholder: "Select priority",
      type: "select",
      options: [
        { label: "High", value: "high" },
        { label: "Medium", value: "medium" },
        { label: "Low", value: "low" },
      ],
      required: true,
      value: "",
      error: "",
    },
    {
      key: "status",
      label: "Status",
      placeholder: "Select status",
      type: "select",
      options: [
        { label: "Pending", value: "pending" },
        { label: "Completed", value: "completed" },
        { label: "Cancelled", value: "cancelled" },
      ],
      required: true,
      value: "",
      error: "",
    },
    {
      key: "deadline",
      label: "Deadline",
      type: "date",
      required: true,
      value: "",
      error: "",
    },
  ]);

  useEffect(() => {
    const id = searchParams?.get("id");
    
    setIsContentLoading(true);
    
    // Mock fetching of data
    // In a real app, this would be an API call
    setTimeout(() => {
      // Simulating a response with data
      const mockResponse = {
        _id: id || '12345',
        title: "Sample SAP Title",
        description: "This is a sample SAP description for testing",
        priority: "medium",
        status: "pending",
        deadline: "2025-05-01",
      };
      
      // Update form structure with values from response
      const updatedFormStructure = formStructure.map(field => ({
        ...field,
        value: mockResponse[field.key] || field.value
      }));
      
      setFormStructure(updatedFormStructure);
      
      // Initialize state from updated structure
      let initialState = {};
      updatedFormStructure.forEach(field => {
        initialState[field.key] = {
          value: field.value || "",
          error: "",
          key: field.key,
          required: field.required,
        };
      });
      setState(initialState);
      
      setIsContentLoading(false);
    }, 1000);
    
    // Real implementation would look like:
    /* 
    const fetchData = async () => {
      try {
        const response = await fetch_sap_by_id(id);
        
        if (response.success) {
          // Update form structure and state with values from response
          const updatedFormStructure = formStructure.map(field => ({
            ...field,
            value: response.data[field.key] || field.value
          }));
          
          setFormStructure(updatedFormStructure);
          
          // Initialize state
          let initialState = {};
          updatedFormStructure.forEach(field => {
            initialState[field.key] = {
              value: field.value || "",
              error: "",
              key: field.key,
              required: field.required,
            };
          });
          setState(initialState);
        } else {
          console.error("Failed to fetch SAP:", response.error);
        }
      } catch (error) {
        console.error("Error fetching SAP:", error);
      } finally {
        setIsContentLoading(false);
      }
    };
    
    fetchData();
    */
  }, []);

  const updateFunctionOfForm = (key, value) => {
    setState((prevState) => {
      return { ...prevState, [key]: { ...prevState[key], value: value } };
    });
  };

  const closeModal = () => {
    navigate("/dashboard/sap");
  };

  const validateForm = () => {
    let isValid = true;
    let newState = { ...state };

    Object.keys(state).forEach((key) => {
      if (state[key].required && !state[key].value) {
        newState[key] = {
          ...newState[key],
          error: "This field is required",
        };
        isValid = false;
      } else {
        newState[key] = {
          ...newState[key],
          error: "",
        };
      }
    });

    setState(newState);
    return isValid;
  };

  const submitHandler = async () => {
    if (!validateForm()) return;

    setIsLoading(true);

    // Mock submission for development
    // Replace with actual API call when backend is ready
    setTimeout(() => {
      setIsLoading(false);
      alert("SAP updated successfully!");
      closeModal();
    }, 1500);

    // Actual implementation would look something like this:
    /* 
    try {
      let formData = {};
      Object.keys(state).forEach((key) => {
        formData[key] = state[key].value;
      });
      
      // Add any other necessary fields like timestamps, user IDs etc.
      
      const id = searchParams?.get("id");
      
      // Call API to update SAP
      const response = await update_sap_api_endpoint(id, formData);
      
      if (response.success) {
        // Update state/UI
        fetchList(); // Refresh the list
        closeModal();
      } else {
        // Handle error
        console.error("Failed to update SAP:", response.error);
      }
    } catch (error) {
      console.error("Error updating SAP:", error);
    } finally {
      setIsLoading(false);
    }
    */
  };

  return (
    <div className="w-[424px] max-h-[95vh] h-fit overflow-y-auto flex flex-col gap-[0rem] bg-[#fff] rounded-[6px] p-4 z-[150]">
      <ModelHeaderAndRoute
        className="w-full h-fit flex flex-row justify-between ps-4 pe-4"
        title="Edit SAP"
        onClick={closeModal}
      />

      {isContentLoading ? (
        <div className="w-full min-h-[300px] flex justify-center items-center">
          <div className="w-[48px] h-[48px] rounded-full border-2 border-gray-300 border-t-mainGreen animate-spin"></div>
        </div>
      ) : (
        <>
          <FormEnginee
            form={formStructure}
            formState={state}
            formStateFunction={updateFunctionOfForm}
          />

          <div className="w-full h-fit mb-[2rem] mt-[2rem]">
            <div className="w-full h-fit flex flex-row justify-center gap-[1rem]">
              <ButtonComponent
                onClick={closeModal}
                className="w-[45%] h-[58px] rounded-[6px] border-[#00BD94] border-2 bg-clip-text text-transparent bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] flex flex-row justify-center items-center"
              >
                Cancel
              </ButtonComponent>
              <ButtonComponent
                onClick={submitHandler}
                isLoading={isLoading}
                className="w-[45%] h-[58px] rounded-[6px] bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] text-[#fff] flex flex-row justify-center items-center"
              >
                Update
              </ButtonComponent>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default SAPEditLayout; 