import React, { useEffect } from "react";
import SectionHeaders from "../Utils/SectionHeaders";
import ButtonComponent from "../../BasicUIElements/ButtonComponent";
import { useNavigate } from "react-router-dom";
import { routes } from "../../Config/routesConfig";
import { useDispatch, useSelector } from "react-redux";
import UseRouterAuthentication from "../../Hooks/UseRouterAuthentication";
import SAPTableLayout from "./SAPTable/SAPTableLayout";
import SAPDataBox from "./SAPDataBox";

// You would need to create these files and functions
// import { SAPSliceActions } from "../../Store/SAPSlice/SAPSlice";
// import UseSAP, { dateObj } from "./UseSAP";
// import DateFilterTemplateComponent from "../../UI_templates/DateFilterTemplate/DateFilterTemplateComponent";

const SAPLayout = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  
  // Uncomment once you create the SAP slice and hook
  // const { fetch_list } = UseSAP();
  // const { filter, headerCount } = useSelector((state) => {
  //   return state.sap;
  // });

  const { auth, access } = useSelector((state) => {
    return state.master;
  });

  const { isDashboardAccessable, routeAuthentication } =
    UseRouterAuthentication();
    
  useEffect(() => {
    isDashboardAccessable("sap");
    // Uncomment once you implement these
    // dispatch(userListFetch(auth));
    // fetch_list();
  }, [auth, access]);

  return (
    <div className="w-full h-full flex flex-col overflow-y-auto">
      <div className="w-full h-[70px] flex flex-row justify-between items-center ps-8 pe-8 mt-[0.5rem]">
        <SectionHeaders>SAP Inventory</SectionHeaders>
        {routeAuthentication("sap", "write") && (
          <ButtonComponent
            onClick={() => {
              navigate("?page=" + "addasap");
            }}
            className="w-[205px] h-[36px] bg-mainLinearGradient rounded-[12px] font-inter font-[700] text-[14px] text-[#fff] hover:shadow-lg transition-all duration-300"
          >
            + Add a New SAP Item
          </ButtonComponent>
        )}
      </div>

      <div className="w-full mt-[2rem] ps-3 mb-[2rem]">
        <SAPDataBox headerCount={{}} />
      </div>
      <div className="w-full h-[2rem]"></div>

      {/* Time filter dropdown - similar to "Till Now" in the Ticketing page */}
      <div className="w-full flex justify-end pr-8 mb-4">
        <div className="w-[200px]">
          <select className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500">
            <option value="tillNow">Till now</option>
            <option value="today">Today</option>
            <option value="yesterday">Yesterday</option>
            <option value="lastWeek">Last Week</option>
            <option value="lastMonth">Last Month</option>
            <option value="custom">Custom Date</option>
          </select>
        </div>
      </div>

      <SAPTableLayout />
    </div>
  );
};

export default SAPLayout; 