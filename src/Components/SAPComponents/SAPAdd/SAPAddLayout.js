import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import ModelHeaderAndRoute from "../../Utils/UtilsComponents/ModelHeaderAndRoute";
import UseRouterAuthentication from "../../../Hooks/UseRouterAuthentication";
import { routes } from "../../../Config/routesConfig";
import FormEnginee from "../../../UI_Enginee/Form/FormEnginee";
import { formEnginee_Formatee_Structure_and_State } from "../../../Dashboard_API_calling_and_formatting/Form_Formate_and_API";

// When you create the SAPSlice and related hooks, uncomment these
// import { SAPSliceActions } from "../../../Store/SAPSlice/SAPSlice";
// import UseSAP from "../UseSAP";

const SAPAddLayout = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  
  // Uncomment when you have UseSAP hook
  // const { fetchList } = UseSAP();
  
  const { routeAuthentication } = UseRouterAuthentication();
  const { auth, access } = useSelector((state) => {
    return state.master;
  });
  
  useEffect(() => {
    if (!routeAuthentication("sap", "write")) {
      navigate("/dashboard");
    }
  }, [auth, access]);

  // For development, we'll use a placeholder state
  // Replace this with actual state from your SAP slice when created
  const [state, setState] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [formStructure, setFormStructure] = useState([
    {
      key: "title",
      label: "Title",
      placeholder: "Enter title",
      type: "text",
      required: true,
      value: "",
      error: "",
    },
    {
      key: "description",
      label: "Description",
      placeholder: "Enter description",
      type: "textarea",
      required: true,
      value: "",
      error: "",
    },
    {
      key: "priority",
      label: "Priority",
      placeholder: "Select priority",
      type: "select",
      options: [
        { label: "High", value: "high" },
        { label: "Medium", value: "medium" },
        { label: "Low", value: "low" },
      ],
      required: true,
      value: "",
      error: "",
    },
    {
      key: "deadline",
      label: "Deadline",
      type: "date",
      required: true,
      value: "",
      error: "",
    },
  ]);

  useEffect(() => {
    // Initialize state from structure
    let initialState = {};
    formStructure.forEach(field => {
      initialState[field.key] = {
        value: field.value || "",
        error: "",
        key: field.key,
        required: field.required,
      };
    });
    setState(initialState);
  }, []);

  const updateFunctionOfForm = (key, value) => {
    // Updating the state
    let updateState = {
      ...state,
      [key]: { ...state[key], value: value },
    };
    setState(updateState);
  };

  const closeModal = () => {
    navigate("/dashboard/sap");
  };

  const validateForm = () => {
    let isValid = true;
    let newState = { ...state };

    Object.keys(state).forEach((key) => {
      if (state[key].required && !state[key].value) {
        newState[key] = {
          ...newState[key],
          error: "This field is required",
        };
        isValid = false;
      } else {
        newState[key] = {
          ...newState[key],
          error: "",
        };
      }
    });

    setState(newState);
    return isValid;
  };

  const submitHandler = async () => {
    if (!validateForm()) return;

    setIsLoading(true);

    // Mock submission for development
    // Replace with actual API call when backend is ready
    setTimeout(() => {
      setIsLoading(false);
      alert("SAP created successfully!");
      closeModal();
    }, 1500);

    // Actual implementation would look something like this:
    // try {
    //   let formData = {};
    //   Object.keys(state).forEach((key) => {
    //     formData[key] = state[key].value;
    //   });
    //
    //   // Add any other necessary fields like timestamps, user IDs etc.
    //   
    //   // Call API to create SAP
    //   const response = await fetch_api_endpoint(formData);
    //   
    //   if (response.success) {
    //     // Update state/UI
    //     fetchList(); // Refresh the list
    //     closeModal();
    //   } else {
    //     // Handle error
    //     console.error("Failed to create SAP:", response.error);
    //   }
    // } catch (error) {
    //   console.error("Error creating SAP:", error);
    // } finally {
    //   setIsLoading(false);
    // }
  };

  return (
    <div className="w-[424px] max-h-[95vh] h-fit overflow-y-auto flex flex-col gap-[0rem] bg-[#fff] rounded-[6px] p-4 z-[150]">
      <ModelHeaderAndRoute
        className="w-full h-fit flex flex-row justify-between ps-4 pe-4"
        title="Create SAP"
        onClick={closeModal}
      />

      <FormEnginee
        form={formStructure}
        formState={state}
        formStateFunction={updateFunctionOfForm}
      />

      <div className="w-full h-fit mb-[2rem] mt-[2rem]">
        <div className="w-full h-fit flex flex-row justify-center gap-[1rem]">
          <ButtonComponent
            onClick={closeModal}
            className="w-[45%] h-[58px] rounded-[6px] border-[#00BD94] border-2 bg-clip-text text-transparent bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] flex flex-row justify-center items-center"
          >
            Cancel
          </ButtonComponent>
          <ButtonComponent
            onClick={submitHandler}
            isLoading={isLoading}
            className="w-[45%] h-[58px] rounded-[6px] bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] text-[#fff] flex flex-row justify-center items-center"
          >
            Create
          </ButtonComponent>
        </div>
      </div>
    </div>
  );
};

export default SAPAddLayout; 