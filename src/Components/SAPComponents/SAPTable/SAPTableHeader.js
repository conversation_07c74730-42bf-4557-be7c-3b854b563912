import React from "react";
import { FaSearch } from "react-icons/fa";

const SAPTableHeader = ({ searchKey, setSearchKey }) => {
  return (
    <div className="w-full h-[58px] bg-[#fff] rounded-[8px] mt-[12px] flex flex-row px-[1rem] py-[0.5rem]">
      <div className="w-[100%] h-full flex flex-row justify-end gap-[4px]">
        <div className="w-[250px] h-full rounded-[8px] border border-[rgba(0,0,0,0.1)] flex flex-row p-[8px] gap-[8px]">
          <div className="h-full aspect-square flex flex-row justify-center items-center">
            <FaSearch />
          </div>
          <input
            value={searchKey}
            onChange={(e) => setSearchKey(e.target.value)}
            placeholder="Search"
            className="flex-1 h-full outline-none border-none bg-transparent"
          />
        </div>
      </div>
    </div>
  );
};

export default SAPTableHeader; 