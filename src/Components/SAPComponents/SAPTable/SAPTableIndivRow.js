import React from "react";
import { useNavigate } from "react-router-dom";
import { routes } from "../../../Config/routesConfig";
import UseRouterAuthentication from "../../../Hooks/UseRouterAuthentication";

const SAPTableIndivRow = ({ content }) => {
  const navigate = useNavigate();
  const { routeAuthentication } = UseRouterAuthentication();

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case "pending":
        return "bg-[#FFA500] text-white";
      case "completed":
        return "bg-[#13DEB9] text-white";
      case "cancelled":
        return "bg-[#FA896B] text-white";
      default:
        return "bg-gray-200 text-gray-700";
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority?.toLowerCase()) {
      case "high":
        return "bg-[#FA896B] text-white";
      case "medium":
        return "bg-[#FFAE1F] text-white";
      case "low":
        return "bg-[#13DEB9] text-white";
      default:
        return "bg-gray-200 text-gray-700";
    }
  };

  return (
    <div className="w-full min-w-[700px] h-[70px] bg-[#fff] rounded-[8px] mt-[12px] flex flex-row justify-between px-[1rem] py-[0.5rem] hover:shadow-lg transition-all duration-300">
      <div className="flex-1 h-full flex flex-col justify-center">
        <span className="font-inter font-[500] text-[16px] text-[#212121]">
          {content?.title || "Title"}
        </span>
        <span className="font-inter font-[400] text-[14px] text-[#212121]/[0.6]">
          {content?.description || "Description"}
        </span>
      </div>

      <div className="w-[120px] h-full flex flex-col justify-center items-center">
        <div
          className={`px-2 py-1 rounded-md ${getStatusColor(
            content?.status
          )} text-sm font-medium`}
        >
          {content?.status || "Status"}
        </div>
      </div>

      <div className="w-[120px] h-full flex flex-col justify-center items-center">
        <div
          className={`px-2 py-1 rounded-md ${getPriorityColor(
            content?.priority
          )} text-sm font-medium`}
        >
          {content?.priority || "Priority"}
        </div>
      </div>

      <div className="w-[120px] h-full flex flex-col justify-center items-center">
        <span className="font-inter font-[400] text-[14px] text-[#212121]">
          {content?.date || "Date"}
        </span>
      </div>

      <div className="w-[120px] h-full flex flex-row justify-center items-center gap-[0.5rem]">
        {routeAuthentication("sap", "write") && (
          <button
            onClick={() => {
              navigate(`?page=editsap&id=${content?._id}`);
            }}
            className="px-3 py-1 rounded-md bg-blue-500 text-white text-sm font-medium"
          >
            Edit
          </button>
        )}
      </div>
    </div>
  );
};

export default SAPTableIndivRow; 