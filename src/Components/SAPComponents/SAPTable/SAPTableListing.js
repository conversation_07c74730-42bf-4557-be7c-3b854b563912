import React from "react";
import SAPTableIndivRow from "./SAPTableIndivRow";

const SAPTableListing = ({ list = [] }) => {
  return (
    <div className="w-fit h-fit min-h-[210px] pb-[4rem]">
      {list.length === 0 ? (
        <div className="w-full h-full flex justify-center items-center text-gray-500 font-inter mt-8">
          No SAP data available
        </div>
      ) : (
        list?.map((item, index) => {
          return <SAPTableIndivRow key={index} content={item} />;
        })
      )}
    </div>
  );
};

export default SAPTableListing; 