import React, { useState, useEffect } from "react";
import SAPTableHeader from "./SAPTableHeader";
import SAPTableListing from "./SAPTableListing";
import SAPBatchModal from "./SAPBatchModal";
import { useSelector } from "react-redux";
import sapData from "../../SAPComponents/sap.json";

const SAPTableLayout = () => {
  const [searchKey, setSearchKey] = useState("");
  const [data, setData] = useState([]);
  const [selectedBatches, setSelectedBatches] = useState(null);
  const [selectedItemName, setSelectedItemName] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(15);
  
  // Load JSON data
  useEffect(() => {
    try {
      // Check if sapData is an array or has a data property that's an array
      let processedData = [];
      
      if (Array.isArray(sapData)) {
        processedData = sapData;
      } else if (sapData && typeof sapData === 'object') {
        // Look for array properties in the object
        const potentialArrays = Object.values(sapData).filter(val => Array.isArray(val));
        if (potentialArrays.length > 0) {
          // Use the first array found
          processedData = potentialArrays[0];
        } else {
          // Convert object to array if no arrays found
          processedData = [sapData];
        }
      }
      
      setData(processedData);
    } catch (error) {
      console.error('Error processing SAP data:', error);
    }
  }, []);

  const filteredList = Array.isArray(data) ? data.filter((item) => {
    const searchLower = searchKey.toLowerCase();
    return (
      (item?.ItemName?.toLowerCase().includes(searchLower) ||
        item?.itemCode?.toLowerCase().includes(searchLower) ||
        item?.U_AVA_Division?.toLowerCase().includes(searchLower) ||
        item?.U_AVA_PrdType?.toLowerCase().includes(searchLower) ||
        item?.U_AVA_Category?.toLowerCase().includes(searchLower) ||
        item?.U_AVA_SubCat?.toLowerCase().includes(searchLower) ||
        item?.FirmCode?.toString().toLowerCase().includes(searchLower)) ??
      false
    );
  }) : [];

  // Get current items for pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredList.slice(indexOfFirstItem, indexOfLastItem);
  
  // Calculate total pages
  const totalPages = Math.ceil(filteredList.length / itemsPerPage);

  // Change page
  const nextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const prevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  // Reset to page 1 when search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchKey]);

  const handleShowBatches = (batches, itemName) => {
    setSelectedBatches(batches);
    setSelectedItemName(itemName);
    setIsModalOpen(true);
  };

  return (
    <div className="w-full flex flex-col gap-[1rem] h-fit ps-3 pe-3 mb-8">
      <h2 className="text-lg font-medium text-gray-700 mb-2 ml-1">SAP Items</h2>
      
      <div className="flex justify-end items-center mb-4">
        <div className="relative">
          <input
            type="text"
            placeholder="Search"
            value={searchKey}
            onChange={(e) => setSearchKey(e.target.value)}
            className="border border-gray-300 rounded-md pl-10 pr-4 py-2 w-64 focus:outline-none focus:ring-2 focus:ring-teal-500"
          />
          <svg 
            className="absolute left-3 top-2.5 h-5 w-5 text-gray-400"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
              clipRule="evenodd"
            />
          </svg>
        </div>
      </div>
      
      <div className="bg-white rounded-lg shadow">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead>
              <tr className="bg-gray-50">
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Name</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Division</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product Type</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SubCat</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Firm Code</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Batch Details</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {!data || data.length === 0 ? (
                <tr>
                  <td colSpan="8" className="px-6 py-4">
                    <div className="w-full min-h-[160px] flex flex-row justify-center items-center">
                      <div className="w-[48px] h-[48px] rounded-full border-2 border-gray-300 border-t-mainGreen animate-spin"></div>
                    </div>
                  </td>
                </tr>
              ) : filteredList.length === 0 ? (
                <tr>
                  <td colSpan="8" className="px-6 py-4 text-center text-gray-500">No SAP data available</td>
                </tr>
              ) : (
                currentItems.map((item, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-blue-600">{item.itemCode}</td>
                    <td className="px-6 py-4 text-sm text-gray-900">{item.ItemName}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.U_AVA_Division}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.U_AVA_PrdType}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.U_AVA_Category}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.U_AVA_SubCat}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.FirmCode}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <button
                        onClick={() => handleShowBatches(item.batches, item.ItemName)}
                        className="px-2 py-1 bg-teal-100 text-teal-800 rounded-md text-xs font-medium hover:bg-teal-200"
                      >
                        {item.batches.length} {item.batches.length === 1 ? 'Batch' : 'Batches'}
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
        
        {/* Pagination Controls */}
        {filteredList.length > 0 && (
          <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-between">
            <div className="text-sm text-gray-500">
              Showing {indexOfFirstItem + 1}-{Math.min(indexOfLastItem, filteredList.length)} of {filteredList.length} items
            </div>
            <div className="flex space-x-2">
              <button
                onClick={prevPage}
                disabled={currentPage === 1}
                className={`px-4 py-2 text-sm font-medium rounded-md ${
                  currentPage === 1
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                }`}
              >
                Previous
              </button>
              <button
                onClick={nextPage}
                disabled={currentPage === totalPages}
                className={`px-4 py-2 text-sm font-medium rounded-md ${
                  currentPage === totalPages
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                }`}
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
      
      <SAPBatchModal 
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        batches={selectedBatches}
        itemName={selectedItemName}
      />
    </div>
  );
};

export default SAPTableLayout; 