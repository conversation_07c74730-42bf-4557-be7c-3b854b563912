import React, { useEffect, useState } from "react";
import { API_ENDPOINTS, api_headers, BaseURL } from "../../../API/APIConfig";
import { GetAPI } from "../../../API/GetAPI";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import { HiXMark } from "react-icons/hi2";

const HistoryItem = ({ item }) => {
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  return (
    <div className="w-full p-4 border-b border-gray-200">
      <div className="flex justify-between items-center mb-3">
        <div className="font-medium text-base">{item.purpose_of_visit || "N/A"}</div>
        <div className="text-sm text-gray-500">{formatDate(item.createdAt)}</div>
      </div>
      <div className="grid grid-cols-2 gap-3 text-sm">
        <div><span className="font-medium">Walk In Type:</span> {item.walk_in_type || "N/A"}</div>
        <div><span className="font-medium">Name:</span> {item.name || "N/A"}</div>
        {item.mobile_phone && <div><span className="font-medium">Mobile:</span> {item.mobile_phone}</div>}
        {item.remarks && (
          <div className="col-span-2">
            <span className="font-medium">Remarks:</span> {item.remarks}
          </div>
        )}
      </div>
    </div>
  );
};

const MarketingWalkinHistoryModal = ({ isOpen, onClose, walkinId, customerName }) => {
  const [historyData, setHistoryData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (isOpen && walkinId) {
      fetchHistoryData(walkinId);
    }
  }, [isOpen, walkinId]);

  const fetchHistoryData = async (id) => {
    try {
      setLoading(true);
      setError(null);
      
      const url = new URL(BaseURL.mainURl + API_ENDPOINTS.marketing_walkin_history + "/" + id);
      const headers = { ...api_headers };
      
      const response = await GetAPI(url, headers);
      
      if (response.ok) {
        const responseData = await response.json();
        setHistoryData(responseData.data || []);
      } else {
        setError("Failed to fetch history data");
      }
    } catch (err) {
      setError("An error occurred while fetching history data");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[5000]">
      <div className="bg-white rounded-lg w-[600px] max-h-[80vh] overflow-hidden flex flex-col">
        <div className="p-4 border-b flex justify-between items-center">
          <h3 className="text-lg font-medium break-words pr-4">Walkin History - {customerName}</h3>
          <ButtonComponent
            onClick={onClose}
            className="w-[30px] h-[30px] rounded-[6px] bg-[#DBF0F2] flex flex-row justify-center items-center"
          >
            <HiXMark size={20} />
          </ButtonComponent>
        </div>
        
        <div className="flex-1 overflow-y-auto">
          {loading ? (
            <div className="p-8 text-center">Loading history data...</div>
          ) : error ? (
            <div className="p-8 text-center text-red-500">{error}</div>
          ) : historyData.length === 0 ? (
            <div className="p-8 text-center">No history records found</div>
          ) : (
            <div className="divide-y divide-gray-200">
              {historyData.map((item) => (
                <HistoryItem key={item._id} item={item} />
              ))}
            </div>
          )}
        </div>
        
        <div className="p-4 border-t">
          <ButtonComponent
            onClick={onClose}
            className="w-full h-[42px] rounded-[6px] bg-mainLinearGradient text-white font-medium flex items-center justify-center"
          >
            Close
          </ButtonComponent>
        </div>
      </div>
    </div>
  );
};

export default MarketingWalkinHistoryModal; 