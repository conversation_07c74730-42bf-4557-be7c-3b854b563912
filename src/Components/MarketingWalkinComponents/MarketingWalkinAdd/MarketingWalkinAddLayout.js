import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import useMarketingWalkIn, {
  fetch_marketing_walkin_add_form_builder_function,
} from "../useMarketingWalkIn";
import UseRouterAuthentication from "../../../Hooks/UseRouterAuthentication";
import { useNavigate } from "react-router-dom";
import {
  formData_parser,
  formEnginee_Formatee_Structure_and_State,
} from "../../../Dashboard_API_calling_and_formatting/Form_Formate_and_API";
import { marketingWalkinSliceAction } from "../../../Store/MarketingSlice/MarketingWalkinSlice";
import toast from "react-hot-toast";
import { API_ENDPOINTS, api_headers, BaseURL } from "../../../API/APIConfig";
import { PostAPI } from "../../../API/PostAPI";
import { vaildator_Form_function } from "../../../UI_Enginee/Form/FormEngineeConfig";
import ModelHeaderAndRoute from "../../Utils/UtilsComponents/ModelHeaderAndRoute";
import { routes } from "../../../Config/routesConfig";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import FormEnginee from "../../../UI_Enginee/Form/FormEnginee";
import CheckBoxComponent from "../../../BasicUIElements/CheckBoxComponent";

const MarketingWalkinAddLayout = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { fetch_list } = useMarketingWalkIn();

  const { routeAuthentication } = UseRouterAuthentication();
  const { auth, access } = useSelector((state) => {
    return state.master;
  });

  const { add } = useSelector((state) => {
    return state.marketingWalkin;
  });

  const [isLoading, setIsLoading] = useState(false);
  const [state, setState] = useState({});
  const [structure, setStructure] = useState([]);
  const [messageSent, setMessageSent] = useState(true);

  useEffect(() => {
    setState({});
    setStructure([]);
    // Setting structure and state if not added
    const fetch = async () => {
      let response = await fetch_marketing_walkin_add_form_builder_function();
      let ss = formEnginee_Formatee_Structure_and_State(response);
      dispatch(marketingWalkinSliceAction.updateStructureAndState(ss));
      // console.log(ss.structure);
      setState(ss.state);
      setStructure(ss.structure);
    };

    // Setting structure and state if not added
    if (structure?.length === 0) {
      // dispatch(sales_load_addForm_thunk());
      fetch();
    } else {
      // setState(add.state);
    }
  }, []);

  // Chained logic is need in this form only. It will be replicated to all other forms in future
  const updateChain_logic = (formdata, struc, sta) => {
    // Check if the current form state matches the chain logic value.
    if (sta[formdata.key].value?.content === formdata?.chain_logic?.value) {
      let index = null;

      // Find the index of the current formdata key in the structure array.
      for (let i = 0; i < struc?.length; i++) {
        if (formdata?.key === struc[i].key) {
          index = i; // Store the index where the key matches.
        }
      }
      // If the key is not found, return null (no updates needed).
      if (index === null) {
        return null;
      }

      // Generate new structure and state for the additional logic to be added.
      let ss = formEnginee_Formatee_Structure_and_State([
        formdata?.chain_logic?.add,
      ]);

      // Create a new structure array by inserting the new logic after the current index.
      let newArray = [
        ...struc.slice(0, index + 1), // Elements before the index
        ...ss.structure, // The new element
        ...struc.slice(index + 1), // Elements after the index
      ];

      // Update the state by merging the existing state with the new state.
      let newState = {
        ...sta,
        ...ss.state,
      };

      // Set the updated state and structure.
      setState(newState);
      setStructure(newArray);

      return null;
    } else {
      // If the condition is not met, remove the previously added logic element.
      let key = formdata?.chain_logic?.add?.key;
      let index = -1;

      // Find the index of the key in the current structure to be removed.
      for (let i = 0; i < struc?.length; i++) {
        if (key === struc[i].key) {
          index = i;
        }
      }

      // If the key is not found, return null (no updates needed).
      if (index === -1) {
        return null;
      }

      // Filter out the element at the specified index to remove it from the structure.
      let newArray = struc.filter((_, i) => i !== index);

      // Update the structure with the filtered array.
      setStructure(newArray);
    }
  };

  const updateFunctionOfForm = (key, value, formData) => {
    setState((state) => {
      return { ...state, [key]: { ...state[key], value: value } };
    });

    if (formData?.isChained) {
      updateChain_logic(formData, structure, {
        ...state,
        [key]: { ...state[key], value: value },
      });
    }
  };

  // Submit Handler
  const submitHandler = async () => {
    let vaildate = vaildator_Form_function(structure, state);

    setState(vaildate?.validate_Obj);

    console.log(vaildate?.validate_Obj);

    if (!vaildate?.isFormValid) {
      toast.error("Fill all mandatory fields to add the marketing lead", {
        position: "top-right",
      });
      return;
    }

    // Time validation for customer_in_time and customer_out_time
    if (state.customer_in_time?.value && state.customer_out_time?.value) {
      const inTime = new Date(state.customer_in_time.value);
      const outTime = new Date(state.customer_out_time.value);
      
      if (outTime < inTime) {
        toast.error("Customer out time must be greater than customer in time", {
          position: "top-right",
        });
        return;
      }
    }

    let parser_obj = formData_parser(structure, state);

    let obj = {
      ...parser_obj.parser_obj,
      assignedBy: auth?.id,
    };

    setIsLoading(true);
    let toast_id = toast.loading("Posting", { position: "top-right" });
    let response = await marketingWalkinAddALeadFunction(obj, auth);

    let responsedata = await response.json();

    console.log(responsedata);

    if (response?.ok) {
      toast.success("Added", { id: toast_id });

      if (messageSent) {
        await sendMessage(responsedata?.data);
      }
    } else {
      // Filter out the specific mobile number error message
      const errorMessage = responsedata?.error;
      if (errorMessage && !errorMessage.includes("Mobile number or already customner visited to the showroom exists")) {
        toast.error(errorMessage, { id: toast_id });
      } else {
        // If it's the mobile number error, just dismiss the loading toast without showing error
        toast.dismiss(toast_id);
      }
    }

    setIsLoading(false);

    if (response?.ok) {
      fetch_list();
      closeModal();
    }
  };

  const sendMessage = async (content) => {
    try {
      let url = new URL(
        BaseURL?.mainURl +
          API_ENDPOINTS?.marketing_waling_sent_message +
          "/" +
          content?._id
      );

      let body = {};

      let header = {
        ...api_headers,
      };

      let response = await PostAPI(url, body, header);

      if (response?.ok) {
        toast.success("Message Sent", { position: "top-right" });
      }
    } catch (e) {}
    return;
  };

  // Close Modal
  const closeModal = () => {
    navigate(routes?.marketingWalkin?.directLink);
    setState({});
    dispatch(marketingWalkinSliceAction.updateState({}));
  };

  return (
    <div className=" md:w-[424px] w-[340px] max-h-[95vh] h-fit overflow-y-auto bg-[#fff] rounded-[6px] p-4 pb-[1rem] ">
      {/* HEADER  */}
      <ModelHeaderAndRoute
        className="w-full h-fit flex flex-row justify-between ps-4 pe-4 "
        title="Add a Walkin Lead"
        onClick={() => {
          closeModal();
        }}
      />

      {/* Form Enginee */}
      <FormEnginee
        form={structure}
        formStateFunction={updateFunctionOfForm}
        formState={state}
      />

      <div className=" w-full h-fit flex flex-row justify-center mt-[1rem] ">
        <div className=" w-[90%] h-[45px] flex flex-row gap-[0.5rem] justify-start">
          <CheckBoxComponent
            value={messageSent}
            onClick={() => {
              setMessageSent((state) => {
                return !state;
              });
            }}
          />
          <p>Send Message Immediately</p>
        </div>
      </div>

      {/* BUTTON COMPONENT  */}
      <div className=" w-full h-fit mt-[1rem]">
        <div className=" w-full h-fit flex flex-row justify-center gap-[1rem]">
          <ButtonComponent
            onClick={() => {
              closeModal();
            }}
            className=" w-[45%] h-[58px] rounded-[6px] border-[#00BD94] border-2 bg-clip-text text-transparent bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] flex flex-row justify-center items-center"
          >
            Cancel
          </ButtonComponent>
          <ButtonComponent
            onClick={submitHandler}
            isLoading={isLoading}
            className=" w-[45%] h-[58px] rounded-[6px] bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] text-[#fff] flex flex-row justify-center items-center"
          >
            Add a Lead
          </ButtonComponent>
        </div>
      </div>
    </div>
  );
};

export default MarketingWalkinAddLayout;

const marketingWalkinAddALeadFunction = async (content, auth) => {
  const url = new URL(BaseURL.mainURl + API_ENDPOINTS?.marketing_walkin_Add);

  const bodyObj = content;

  let headers = {
    ...api_headers,
    Authorization: auth.token,
  };

  const response = await PostAPI(url, bodyObj, headers);

  return response;
};
