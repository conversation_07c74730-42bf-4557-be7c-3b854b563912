import React, { useEffect } from "react";
import { HiXMark } from "react-icons/hi2";
import ButtonComponent from "../../BasicUIElements/ButtonComponent";
import SelectComponent from "../../BasicUIElements/SelectComponent";
import DatePickerComponents from "../Utils/DatePickerComponents";
import useWhatsAppBlast, { dateObj } from "./useWhatsAppBlast";

const WhatsAppBlastModal = ({ isOpen, onClose }) => {
  const { whatsappData, loading, dateFilter, updateDateFilter, fetchWhatsAppBlastData } = useWhatsAppBlast();

  useEffect(() => {
    if (isOpen) {
      fetchWhatsAppBlastData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen]);

  if (!isOpen) return null;

  const handleDateFilterChange = (key, value) => {
    updateDateFilter(key, value);
  };

  const calculateTotal = () => {
    return Object.values(whatsappData).reduce((sum, count) => sum + count, 0);
  };

  return (
    <div className="w-[100vw] h-[100vh] fixed bg-black bg-opacity-50 top-0 left-0 flex flex-row justify-center md:items-center md:pt-0 pt-[2rem] z-[5000]">
      <div className="w-fit h-fit" onClick={(e) => e.stopPropagation()}>
        <div className="w-[450px] h-fit bg-[#fff] rounded-[8px] p-5">
          {/* Header */}
          <div className="w-full h-fit flex flex-row justify-between items-center mb-4">
            <h1 className="font-poppins font-[600] text-[16px] text-[#333]">
              WhatsApp Blast Analytics
            </h1>
            <ButtonComponent
              onClick={onClose}
              className="w-[20px] h-[20px] rounded-[4px] bg-gray-100 hover:bg-gray-200 flex flex-row justify-center items-center transition-colors"
            >
              <HiXMark size={"14"} />
            </ButtonComponent>
          </div>

          {/* Date Filter */}
          <div className="mb-4">
            <h3 className="text-xs font-medium text-gray-700 mb-2">Date Filter</h3>
            <div className="w-fit">
              <SelectComponent
                optionarr={dateObj}
                value={dateFilter.filter}
                className="w-[120px] h-[32px] text-xs"
                updateFunction={(e) => {
                  handleDateFilterChange("filter", e);
                }}
              />

              <div
                className={`mt-2 flex flex-row gap-[0.5rem] ${
                  "Custom Date" === dateFilter.filter.content ? "flex" : "hidden"
                }`}
              >
                <DatePickerComponents
                  title="Start Date"
                  className="relative border w-[100px] h-[32px] rounded-[4px] bg-white flex flex-row justify-center text-xs"
                  value={dateFilter.startDate}
                  isLogoPresent={false}
                  onChange={(e) => {
                    handleDateFilterChange("startDate", new Date(e.target.value));
                  }}
                />
                <DatePickerComponents
                  title="End Date"
                  className="relative border w-[100px] h-[32px] rounded-[4px] bg-white flex flex-row justify-center text-xs"
                  value={dateFilter.endDate}
                  isLogoPresent={false}
                  onChange={(e) => {
                    handleDateFilterChange("endDate", new Date(e.target.value));
                  }}
                />
              </div>
            </div>
          </div>

          {/* Message Counts */}
          <div className="mb-4">
            <h3 className="text-xs font-medium text-gray-700 mb-3">Message Counts</h3>

            {loading ? (
              <div className="w-full h-[100px] flex flex-row justify-center items-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
              </div>
            ) : (
              <div className="space-y-1">
                {/* Welcome Message and Survey */}
                <div className="flex justify-between items-center py-1">
                  <span className="text-gray-700 text-sm">Welcome Message</span>
                  <span className="text-blue-600 font-bold text-sm">{whatsappData.Welcome_message || 0}</span>
                </div>
                <div className="flex justify-between items-center py-1">
                  <span className="text-gray-700 text-sm">Survey</span>
                  <span className="text-blue-600 font-bold text-sm">{whatsappData.Survey || 0}</span>
                </div>

                {/* B1-B6 in grid format */}
                <div className="grid grid-cols-2 gap-x-8 gap-y-1 mt-2">
                  <div className="flex justify-between items-center py-1">
                    <span className="text-gray-700 text-sm">B1</span>
                    <span className="text-blue-600 font-bold text-sm">{whatsappData.B1 || 0}</span>
                  </div>
                  <div className="flex justify-between items-center py-1">
                    <span className="text-gray-700 text-sm">B2</span>
                    <span className="text-blue-600 font-bold text-sm">{whatsappData.B2 || 0}</span>
                  </div>
                  <div className="flex justify-between items-center py-1">
                    <span className="text-gray-700 text-sm">B3</span>
                    <span className="text-blue-600 font-bold text-sm">{whatsappData.B3 || 0}</span>
                  </div>
                  <div className="flex justify-between items-center py-1">
                    <span className="text-gray-700 text-sm">B4</span>
                    <span className="text-blue-600 font-bold text-sm">{whatsappData.B4 || 0}</span>
                  </div>
                  <div className="flex justify-between items-center py-1">
                    <span className="text-gray-700 text-sm">B5</span>
                    <span className="text-blue-600 font-bold text-sm">{whatsappData.B5 || 0}</span>
                  </div>
                  <div className="flex justify-between items-center py-1">
                    <span className="text-gray-700 text-sm">B6</span>
                    <span className="text-blue-600 font-bold text-sm">{whatsappData.B6 || 0}</span>
                  </div>
                </div>

                {/* Total Messages */}
                <div className="bg-blue-50 rounded-md p-2 mt-3">
                  <div className="flex justify-between items-center">
                    <span className="text-blue-700 font-medium text-sm">Total Messages</span>
                    <span className="text-blue-600 font-bold text-lg">{calculateTotal()}</span>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Close Button */}
          <div className="flex justify-end">
            <ButtonComponent
              onClick={onClose}
              className="px-4 py-1 bg-gray-500 hover:bg-gray-600 text-white rounded text-xs font-medium transition-colors"
            >
              Close
            </ButtonComponent>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WhatsAppBlastModal;
