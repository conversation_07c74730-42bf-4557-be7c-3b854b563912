import { useState } from "react";
import { API_ENDPOINTS, api_headers, BaseURL } from "../../API/APIConfig";
import { GetAPI } from "../../API/GetAPI";
import { getStartAndEndDate } from "../../Config/DateFilterConfig";

export const dateObj = [
  {
    id: "Last year",
    content: "Last year",
  },
  {
    id: "Last Month",
    content: "Last Month",
  },
  {
    id: "Last Week",
    content: "Last Week",
  },
  {
    id: "Custom Date",
    content: "Custom Date",
  },
  {
    id: "Till now",
    content: "Till now",
  },
];

function formatDateToYYYYMMDD(dateObj) {
  if (!(dateObj instanceof Date)) {
    return;
  }
  dateObj = new Date(dateObj);
  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, "0");
  const day = String(dateObj.getDate()).padStart(2, "0");

  return `${year}-${month}-${day}`;
}

const useWhatsAppBlast = () => {
  const [whatsappData, setWhatsappData] = useState({
    Welcome_message: 0,
    Survey: 0,
    B1: 0,
    B2: 0,
    B3: 0,
    B4: 0,
    B5: 0,
    B6: 0,
  });
  const [loading, setLoading] = useState(false);
  const [dateFilter, setDateFilter] = useState({
    filter: { id: "Last Week", content: "Last Week" },
    startDate: new Date(),
    endDate: new Date(),
  });

  const fetchWhatsAppBlastData = async (dateParams = null) => {
    setLoading(true);
    
    try {
      let startDate = dateFilter.startDate;
      let endDate = dateFilter.endDate;
      let filter_options = dateFilter.filter?.content;

      // Use provided date params if available
      if (dateParams) {
        startDate = dateParams.startDate || startDate;
        endDate = dateParams.endDate || endDate;
        filter_options = dateParams.filter?.content || filter_options;
      }

      // Calculate dates based on filter
      if (filter_options === "Last year") {
        let [startD, endD] = getStartAndEndDate(365);
        startDate = new Date(startD);
        endDate = new Date(endD);
      } else if (filter_options === "Last Month") {
        let [startD, endD] = getStartAndEndDate(30);
        startDate = new Date(startD);
        endDate = new Date(endD);
      } else if (filter_options === "Last Week") {
        let [startD, endD] = getStartAndEndDate(7);
        startDate = new Date(startD);
        endDate = new Date(endD);
      } else if (filter_options === "Till now") {
        let [startD, endD] = getStartAndEndDate(3650 + 3650);
        startDate = new Date(startD);
        endDate = new Date(endD);
      }

      let url = new URL(
        BaseURL?.mainURl + API_ENDPOINTS?.marketing_walkin_whatsapp_blast
      );

      url.searchParams.set("startDate", formatDateToYYYYMMDD(startDate));
      url.searchParams.set("endDate", formatDateToYYYYMMDD(endDate));

      let headers = { ...api_headers };

      let response = await GetAPI(url, headers);

      if (response?.ok) {
        let data = await response?.json();
        console.log("WhatsApp Blast Data:", data);
        
        if (data?.count) {
          setWhatsappData(data.count);
        }
      } else {
        console.error("Failed to fetch WhatsApp blast data");
      }
    } catch (error) {
      console.error("Error fetching WhatsApp blast data:", error);
    } finally {
      setLoading(false);
    }
  };

  const updateDateFilter = (key, value) => {
    const newDateFilter = {
      ...dateFilter,
      [key]: value,
    };
    setDateFilter(newDateFilter);
    fetchWhatsAppBlastData(newDateFilter);
  };

  return {
    whatsappData,
    loading,
    dateFilter,
    updateDateFilter,
    fetchWhatsAppBlastData,
  };
};

export default useWhatsAppBlast;
