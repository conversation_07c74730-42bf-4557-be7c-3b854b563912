import React, { useEffect, useState } from "react";
import DashboardNavbar_template from "../../UI_templates/Dashboard_Templates/DashboardNavbar_template";
import { routes } from "../../Config/routesConfig";
import ButtonComponent from "../../BasicUIElements/ButtonComponent";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import SectionHeaders from "../Utils/SectionHeaders";

import DateFilterTemplateComponent from "../../UI_templates/DateFilterTemplate/DateFilterTemplateComponent";
import MarketingWalkinDataBox from "./MarketingWalkinDataBox";
import MarketingWalkinTableLayout from "./MarketingWalkinTable/MarketingWalkinTableLayout";
import useMarketingWalkIn, { dateObj } from "./useMarketingWalkIn";
import { marketingWalkinSliceAction } from "../../Store/MarketingSlice/MarketingWalkinSlice";
import WhatsAppBlastModal from "./WhatsAppBlastModal";

const MarketingWalkinLayout = () => {
  const { list, headerList, filter } = useSelector((state) => {
    return state.marketingWalkin;
  });

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [isWhatsAppModalOpen, setIsWhatsAppModalOpen] = useState(false);

  const { fetch_list, loadTableHeaderData } = useMarketingWalkIn();

  const { auth, access } = useSelector((state) => {
    return state.master;
  });

  useEffect(() => {
    fetch_list();
  }, [auth, access]);
  return (
    <div className=" w-full h-full flex flex-col">
      <DashboardNavbar_template
        listLink={routes?.marketing?.directLink}
        insightsLink={routes?.marketingAnalytics?.directLink}
        dashboard="marketing"
        subpages={[
          { title: "Walk In", path: routes?.marketingWalkin.directLink },
        ]}
      />

      <div className="  w-full h-fit ps-4 flex md:flex-row-reverse flex-col  pe-4  flex-nowrap overflow-y-auto justify-between flex-shrink-0 gap-[1rem] mt-[3rem]">
        <div className="flex flex-col md:flex-row gap-[1rem]">
          <ButtonComponent
            onClick={() => {
              setIsWhatsAppModalOpen(true);
            }}
            className=" w-[150px] h-[36px] bg-[#25D366] hover:bg-[#1DA851] flex-shrink-0 rounded-[12px] font-inter font-[700] text-[14px] text-[#fff] transition-colors"
          >
            WhatsApp Blast
          </ButtonComponent>
          <ButtonComponent
            onClick={() => {
              navigate("?page=" + routes?.marketingWalkinAdd?.searchParams);
            }}
            className=" w-[215px] h-[36px] bg-mainLinearGradient flex-shrink-0 rounded-[12px] font-inter font-[700] text-[14px] text-[#fff] "
          >
            + Add a New Walk In Lead
          </ButtonComponent>
        </div>

        <div className=" w-fit flex flex-col gap-[1.5rem] ">
          <SectionHeaders>Marketing WalkIn Summary</SectionHeaders>
          <MarketingWalkinDataBox headerData={headerList} />
        </div>
      </div>

      <div className=" flex-1 overflow-y-auto">
        <DateFilterTemplateComponent
          fetch={fetch_list}
          updateSlice={marketingWalkinSliceAction.updateFilterDate}
          dateObj={dateObj}
          customDate="Custom Date"
          date={filter?.date}
        />
        <MarketingWalkinTableLayout list={list} />
      </div>

      {/* WhatsApp Blast Modal */}
      <WhatsAppBlastModal
        isOpen={isWhatsAppModalOpen}
        onClose={() => setIsWhatsAppModalOpen(false)}
      />
    </div>
  );
};

export default MarketingWalkinLayout;
