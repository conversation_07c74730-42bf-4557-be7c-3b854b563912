import React from "react";
import DataStructureBoxHeader from "../Utils/DataStuctureBoxes/DataStructureBoxHeader";
import HeaderDataEnginee from "../../UI_Enginee/HeaderData/HeaderDataEnginee";

const MarketingWalkinDataBox = ({ headerData = {} }) => {
  let eachPerLine = 6; // Updated to accommodate 6 items: TOTAL, NEW LEAD, QUOTE SENT, IN FOLLOW UP, DROPPED, ORDER CLOSED
  let widthPerTitle = 200;
  return (
    <div className=" w-full h-fit flex flex-row flex-shrink-0  pb-[1rem] md:pe-3 md:ps-0 ps-4 pe-4 ">
      <div
        className={` w-[${
          widthPerTitle * eachPerLine
        }px] h-fit min-h-[100px] min-w-[500px] flex flex-col flex-shrink-0 justify-between border-2 p-3 rounded-[10px]`}
      >
        <DataStructureBoxHeader logo={""}></DataStructureBoxHeader>
        <HeaderDataEnginee headerData={headerData} eachPerLine={6} />
      </div>
    </div>
  );
};
export default MarketingWalkinDataBox;
