import React from "react";
import UseJsObj2CSV from "../../../Hooks/UseJsObj2CSV";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import { FaDownload } from "react-icons/fa6";
import { IoClose } from "react-icons/io5";

const MarketingWalkinTableTopper = ({
  state = "",
  setState = () => {},
  totalResults = 0,
  filteredResults = 0,
  isSearching = false,
  isProcessing = false
}) => {
  const { downloadMarketingWalkinDashboard } = UseJsObj2CSV();
  return (
    <div className=" w-full h-fit flex flex-row justify-between ps-4 pe-6">
      <div className="flex flex-col">
        <div>Marketing WalkIn List</div>
        {isSearching && (
          <div className="text-sm text-gray-600 mt-1">
            {isProcessing ? (
              <span className="text-blue-600">Searching...</span>
            ) : (
              <>
                Showing {filteredResults} of {totalResults} results
                {filteredResults === 0 && state.trim() !== "" && (
                  <span className="text-orange-600 ml-2">No matches found</span>
                )}
              </>
            )}
          </div>
        )}
      </div>

      <div className=" w-fit h-full flex flex-row items-center  gap-[0.8rem]   ">
        <div className=" w-[250px] flex flex-col items-center relative">
          <input
            value={state}
            type={"text"}
            placeholder={"Search"}
            onChange={(event) => {
              setState(event.target.value);
            }}
            className=" w-full h-[38px] rounded-[6px] ps-5 pe-10 border border-[#D8E1F2] text-[16px] font-poppins text-[#53545C] placeholder:font-poppins placeholder:text-[16px] placeholder:font-[400] placeholder:text-[#ABAFB1]"
          />
          {state && (
            <button
              onClick={() => setState("")}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
              title="Clear search"
            >
              <IoClose size={18} />
            </button>
          )}
        </div>
        <ButtonComponent
          onClick={downloadMarketingWalkinDashboard}
          className=" w-fit h-[50px] flex flex-row justify-center items-center gap-[0.25rem]"
        >
          <FaDownload />
        </ButtonComponent>
      </div>
    </div>
  );
};

export default MarketingWalkinTableTopper;
