import React, { useState, useMemo, useCallback } from "react";
import MarketingWalkinTableTopper from "./MarketingWalkinTableTopper";
import MarketingWalkinTableFilterTab from "./MarketingWalkinTableFilterTab";
import MarketingWalkinTableHeader from "./MarketingWalkinTableHeader";
import MarketingWalkinTableListingRow from "./MarketingWalkinTableListingRow";
import { useDebounce } from "../../../Hooks/useDebounce";

function isSubarrayPresent(str2, str1) {
  // Check if the subArrayString is a substring of fullString
  return str2
    ?.toString()
    ?.toLowerCase()
    ?.includes(str1?.toString()?.toLowerCase());
}

// Enhanced search function that searches across multiple fields
function searchInMultipleFields(item, searchTerm) {
  if (!searchTerm || searchTerm.trim() === "") return true;

  const searchFields = [
    item?.name,
    item?.mobile_phone,
    item?.current_location,
    item?.lead_status,
    item?.assignedBy?.name || item?.assignedBy,
    item?.salutation,
    item?.purpose_of_visit,
    item?.walk_in_type,
    item?.category,
    item?.remarks,
    item?.date_of_birth,
    item?.customer_in_time,
    item?.customer_out_time,
    item?.status_of_building,
    // Add more searchable fields as needed
  ];

  const searchLower = searchTerm.toLowerCase().trim();

  // Handle multiple search terms (space-separated)
  const searchTerms = searchLower.split(/\s+/).filter(term => term.length > 0);

  return searchTerms.every(term =>
    searchFields.some(field => {
      if (!field) return false;
      const fieldStr = field.toString().toLowerCase();
      return fieldStr.includes(term);
    })
  );
}

const MarketingWalkinTableLayout = ({ list = [] }) => {
  const [searchState, setSearchState] = useState("");

  // Debounce search input to improve performance
  const debouncedSearchTerm = useDebounce(searchState, 300);

  // Check if search is in progress (user is typing but debounce hasn't completed)
  const isSearching = searchState !== debouncedSearchTerm;

  const update = useCallback((e) => {
    setSearchState(e);
  }, []);

  // Memoize filtered list to prevent unnecessary recalculations
  const filteredList = useMemo(() => {
    const trimmedSearch = debouncedSearchTerm?.trim() || "";

    // Don't filter if search term is empty or too short (less than 2 characters)
    if (!trimmedSearch || trimmedSearch.length < 2) {
      return list;
    }

    return list?.filter((item) => {
      return searchInMultipleFields(item, trimmedSearch);
    });
  }, [list, debouncedSearchTerm]);
  return (
    <div className="w-full h-full flex flex-col">
      <MarketingWalkinTableTopper
        state={searchState}
        setState={update}
        totalResults={list?.length || 0}
        filteredResults={filteredList?.length || 0}
        isSearching={debouncedSearchTerm && debouncedSearchTerm.trim().length >= 2}
        isProcessing={isSearching}
      />
      <MarketingWalkinTableFilterTab />
      <div className="w-full h-full flex flex-col overflow-hidden">
        <div id="marketingwalkintable" className="w-full h-full flex flex-col overflow-auto">
          <div className="w-full h-fit flex flex-col sticky top-0 z-[1000]">
            <MarketingWalkinTableHeader />
          </div>
          <div className="w-full h-fit flex flex-col">
            <MarketingWalkinTableListingRow list={filteredList} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default MarketingWalkinTableLayout;
