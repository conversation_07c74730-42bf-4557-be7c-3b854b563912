import React, { useState } from "react";
import MarketingTableIndivRow from "../../MarketingComponents/MarketingTable/MarketingTableIndivRow";
import MarketingWalkinTableIndivRow from "./MarketingWalkinTableIndivRow";

const MarketingWalkinTableListingRow = ({ list = [] }) => {
  return (
    <div className=" w-fit h-fit min-h-[250px] mb-[5rem] ">
      {list.map((state) => {
        return <MarketingWalkinTableIndivRow content={state} />;
      })}
    </div>
  );
};

export default MarketingWalkinTableListingRow;
