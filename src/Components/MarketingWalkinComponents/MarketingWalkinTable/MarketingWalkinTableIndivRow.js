import React, { useState } from "react";
import { useAsyncError, useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import UseRouterAuthentication from "../../../Hooks/UseRouterAuthentication";
import UseUserData from "../../../Hooks/UseUserData";
import { routes } from "../../../Config/routesConfig";
import { marketingWalkinTableConfig } from "../../../Config/MarketingWalkinConfig";
import TableTextContent from "../../Utils/TableStructures/TableTextContent";
import TableTextAreaTextModelDisplay from "../../../UI_Enginee/TableListEnginee/TableTextAreaTextModelDisplay";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import edit from "../../../Assest/Utils/edit.png";
import { returnUserNameWIthID_tickets } from "../../../Config/TicketingConfig";
import TableList_array_template from "../../../UI_templates/TableList_array_template";
import { API_ENDPOINTS, api_headers, BaseURL } from "../../../API/APIConfig";
import { PostAPI } from "../../../API/PostAPI";
import MarketingWalkinHistoryModal from "../MarketingWalkinHistory/MarketingWalkinHistoryModal";

const datetimeFormatFunction = (dt) => {
  let date = new Date(dt);

  let fullyear = date.getFullYear();
  let month =
    date.getMonth() + 1 < 10
      ? "0" + (date.getMonth() + 1)
      : date.getMonth() + 1;

  let dateNO = date.getDate();

  const hours = String(date?.getHours()).padStart(2, "0");
  const minutes = String(date?.getMinutes()).padStart(2, "0");

  let format =
    hours + " : " + minutes + " --- " + fullyear + "/" + month + "/" + dateNO;

  return format;
};

const dateFormatFunction = (dt) => {
  let date = new Date(dt);

  let fullyear = date.getFullYear();
  let month =
    date.getMonth() + 1 < 10
      ? "0" + (date.getMonth() + 1)
      : date.getMonth() + 1;

  let dateNO = date.getDate();

  let format = fullyear + "-" + month + "-" + dateNO;

  return format;
};

const getAssigneeString = (array, userlist) => {
  let arrayf = [];
  for (let i = 0; i < array?.length; i++) {
    arrayf.push(returnUserNameWIthID_tickets(array[i], userlist));
  }

  return arrayf;
};

const MarketingWalkinTableIndivRow = ({ content }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { routeAuthentication } = UseRouterAuthentication();

  const { getUsernameWithid, userList } = UseUserData();

  const [editLoading, setEditLoading] = useState(false);
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);

  let saleperson = getAssigneeString(
    content?.name_of_the_sales_person,
    userList
  );

  const editClickHandler = async () => {
    navigate(
      "?page=" +
        routes?.marketingWalkinEdit?.searchParams +
        "&id=" +
        content?._id
    );
    setEditLoading(false);
  };

  const deleteClickHandler = () => {};

  const [is_message_sent, set_is_message_sent] = useState(false);
  const [message_loading, set_message_loading] = useState(false);

  let is_message_sent_boolean = is_message_sent || content?.walkin_message_sent;

  const sendMessage = async () => {
    try {
      let url = new URL(
        BaseURL?.mainURl +
          API_ENDPOINTS?.marketing_waling_sent_message +
          "/" +
          content?._id
      );

      let body = {};

      let header = {
        ...api_headers,
      };

      set_message_loading(true);
      let response = await PostAPI(url, body, header);

      if (response?.ok) {
        set_is_message_sent(true);
      }

      set_message_loading(false);
    } catch (e) {}
  };

  return (
    <div
      className=" w-fit h-[52px] border-b-2 flex flex-row hover:bg-[#F0FEFF]  "
      key={content?._id}
    >
      <div className=" w-[60px] h-full flex flex-row justify-center items-center">
        <ButtonComponent
          onClick={editClickHandler}
          isLoading={editLoading}
          className=" w-[24px] h-[24px] flex flex-rwo justify-center items-center "
        >
          <img
            src={edit}
            className=" w-full h-full object-contain"
            alt="edit"
          />
        </ButtonComponent>
      </div>

      <TableTextContent
        size={marketingWalkinTableConfig[0]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {getUsernameWithid(content?.assignedBy)}
      </TableTextContent>
      <TableTextContent
        size={marketingWalkinTableConfig[1]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.salutation}
      </TableTextContent>
      <TableTextContent
        size={marketingWalkinTableConfig[2]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.name}
      </TableTextContent>
      <TableTextContent
        size={marketingWalkinTableConfig[3]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {dateFormatFunction(content?.date_of_birth)}
      </TableTextContent>
      <TableTextContent
        size={marketingWalkinTableConfig[4]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.mobile_phone}
      </TableTextContent>
      <TableTextAreaTextModelDisplay
        title="Current Location"
        size={marketingWalkinTableConfig[5]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.current_location}
      </TableTextAreaTextModelDisplay>

      <TableTextContent
        title="Lead"
        size={marketingWalkinTableConfig[6]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.lead_status}
      </TableTextContent>
      <div size={marketingWalkinTableConfig[7]?.className}>
        {is_message_sent_boolean ? (
          <div className=" w-fit  h-full flex flex-row pe-8 justify-center items-center">
            <ButtonComponent className=" w-[150px] h-[24px] text-white rounded-[20px] bg-mainLinearGradient flex flex-rwo justify-center items-center ">
              Message Sent
            </ButtonComponent>
          </div>
        ) : (
          <div className=" w-fit  h-full flex flex-row pe-8 justify-center items-center">
            <ButtonComponent
              onClick={sendMessage}
              isLoading={message_loading}
              className=" w-[150px] h-[24px] rounded-[20px] bg-mainLinearGradient flex flex-rwo justify-center items-center "
            >
              Send Message
            </ButtonComponent>
          </div>
        )}
      </div>
      <TableTextContent
        size={marketingWalkinTableConfig[8]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.architect_details}
      </TableTextContent>
      <TableTextContent
        size={marketingWalkinTableConfig[9]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.engineer_details}
      </TableTextContent>
      <TableTextContent
        size={marketingWalkinTableConfig[10]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.building_area}
      </TableTextContent>
      <TableTextContent
        size={marketingWalkinTableConfig[11]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.no_of_bathrooms}
      </TableTextContent>
      <TableTextContent
        size={marketingWalkinTableConfig[12]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.category}
      </TableTextContent>
      <TableTextContent
        size={marketingWalkinTableConfig[13]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.status_of_building}
      </TableTextContent>
      <TableList_array_template
        className={marketingWalkinTableConfig[14]?.className}
        list={content?.customer_visited_following_showroom}
      ></TableList_array_template>
      <TableTextContent
        size={marketingWalkinTableConfig[15]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.Category}
      </TableTextContent>
      <TableList_array_template
        list={saleperson}
        className={marketingWalkinTableConfig[16]?.className}
      />
      <TableTextContent
        size={marketingWalkinTableConfig[17]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {datetimeFormatFunction(content?.customer_in_time)}
      </TableTextContent>
      <TableTextContent
        size={marketingWalkinTableConfig[18]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {datetimeFormatFunction(content?.customer_out_time)}
      </TableTextContent>

      <TableTextAreaTextModelDisplay
        title="Remarks"
        size={marketingWalkinTableConfig[19]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.remarks}
      </TableTextAreaTextModelDisplay>

      <TableTextContent
        size={marketingWalkinTableConfig[20]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.how_do_you_know_about_our_showroom}
      </TableTextContent>

      <TableTextContent
        size={marketingWalkinTableConfig[21]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.unit}
      </TableTextContent>

      <TableTextContent
        size={marketingWalkinTableConfig[22]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.walk_in_type}
      </TableTextContent>

      <TableTextContent
        size={marketingWalkinTableConfig[23]?.className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079] pe-3 truncate"
      >
        {content?.purpose_of_visit}
      </TableTextContent>

      <div size={marketingWalkinTableConfig[24]?.className}>
        <div className=" w-fit h-full flex flex-row pe-8 justify-center items-center">
          <ButtonComponent
            onClick={() => setIsHistoryModalOpen(true)}
            className=" w-[150px] h-[24px] text-white rounded-[20px] bg-mainLinearGradient flex flex-row justify-center items-center "
          >
            View History
          </ButtonComponent>
        </div>
      </div>

      <MarketingWalkinHistoryModal 
        isOpen={isHistoryModalOpen}
        onClose={() => setIsHistoryModalOpen(false)}
        walkinId={content?.id || content?._id}
        customerName={content?.name}
      />
    </div>
  );
};
export default MarketingWalkinTableIndivRow;
