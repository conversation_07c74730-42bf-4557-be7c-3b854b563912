import React, { useEffect } from "react";
import TableHeaderEnginee, {
  table_header_state_update,
} from "../../../UI_Enginee/TableHeaderEnginee/TableHeaderEnginee";

import { useDispatch, useSelector } from "react-redux";
import { marketingWalkinSliceAction } from "../../../Store/MarketingSlice/MarketingWalkinSlice";
import useMarketingWalkIn from "../useMarketingWalkIn";

const MarketingWalkinTableHeader = () => {
  const dispatch = useDispatch();

  const { filter, table } = useSelector((state) => {
    return state.marketingWalkin;
  });

  const { fetch_list } = useMarketingWalkIn();

  // State update - only run once on mount
  useEffect(() => {
    if (table?.header && filter?.state) {
      let state = table_header_state_update(table?.header, filter?.state);
      dispatch(marketingWalkinSliceAction.updateFilterState(state?.state));
    }
  }, []); // Empty dependency array to run only once on mount

  const updateState = (key, value) => {
    let updatedState = {
      ...filter?.state,
      [key]: value,
    };
    dispatch(marketingWalkinSliceAction.updateFilterState(updatedState));

    fetch_list(updatedState, null);
  };

  return (
    <div className="w-full h-fit flex flex-col bg-white">
      <div className="w-fit h-[52px] border-b-2 flex flex-row">
        <div className="w-[60px] h-full flex flex-row justify-center items-center"></div>
        <TableHeaderEnginee
          table={table?.header}
          state={filter?.state}
          updateState={updateState}
        />
      </div>
    </div>
  );
};

export default MarketingWalkinTableHeader;
