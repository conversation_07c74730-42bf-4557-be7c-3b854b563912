import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import UseMarketing from "../../../Hooks/UseMarketing";
import { marketingSliceActions } from "../../../Store/MarketingSlice/MarketingSlices";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import useMarketingWalkIn from "../useMarketingWalkIn";
import { marketingWalkinSliceAction } from "../../../Store/MarketingSlice/MarketingWalkinSlice";

const Selected = ({ text = "All", onClick }) => {
  return (
    <ButtonComponent
      onClick={onClick}
      className=" w-fit h-[48px] border-b-[4px] border-[#019BA2] ps-6 pe-6 font-poppins text-[16px] text-[#019BA2] font-[400]"
    >
      {text}
    </ButtonComponent>
  );
};

const Upselected = ({ text = "", onClick }) => {
  return (
    <ButtonComponent
      onClick={onClick}
      className=" w-fit h-[48px] border-b-[4px]  border-opacity-0 ps-6 pe-6 font-poppins text-[16px]  font-[400]"
    >
      {text}
    </ButtonComponent>
  );
};

// Helper Function
const isAll = (content = []) => {
  // If no status and more than one status are selected, then all should light up
  if (content?.length <= 0) {
    return true;
  }
  return false;
};

const isCompleted = (content = [], id) => {
  // If only completed status is picked,
  if (content?.length == 1 && content[0]?.id === id) {
    return true;
  }
  return false;
};

const MarketingWalkinTableFilterTab = () => {
  const dispatch = useDispatch();

  const { filter } = useSelector((state) => {
    return state.marketingWalkin;
  });
  const { fetch_list } = useMarketingWalkIn();

  const updateState = (key, value) => {
    let updatedState = {
      ...filter?.state,
      [key]: value,
    };

    fetch_list(updatedState, null);
    dispatch(marketingWalkinSliceAction.updateFilterState(updatedState));
  };

  const removeAllFilters = () => {
    // Clear all filters by setting an empty state
    fetch_list({}, null);
    dispatch(marketingWalkinSliceAction.updateFilterState({}));
  };

  let array = [
    "NEW LEAD",
    "QUOTE SENT",
    "IN FOLLOW UP",
    "DROPPED",
    "ORDER CLOSED",
  ];
  return (
    <div className="min-w-full  w-full overflow-auto flex flex-row justify-between ps-6 pe-6 ">
      <div className=" w-fit flex flex-row ">
        {isAll(filter?.state?.lead_status) ? (
          <Selected onClick={() => {}} text="All" />
        ) : (
          <Upselected
            onClick={() => {
              updateState("lead_status", []);
            }}
            text="All"
          />
        )}

        {array?.map((item) => {
          return (
            <>
              {isCompleted(filter?.state.lead_status, item) ? (
                <Selected onClick={() => {}} text={item} />
              ) : (
                <Upselected
                  onClick={() => {
                    updateState("lead_status", [{ id: item, content: item }]);
                  }}
                  text={item}
                />
              )}
            </>
          );
        })}
      </div>
      <div className="flex items-center">
        <ButtonComponent
          onClick={removeAllFilters}
          className="w-fit h-[36px] bg-[#019BA2] text-white px-4 rounded-md font-poppins text-[14px] hover:bg-[#018a90]"
        >
          Remove All Filters
        </ButtonComponent>
      </div>
    </div>
  );
};

export default MarketingWalkinTableFilterTab;
