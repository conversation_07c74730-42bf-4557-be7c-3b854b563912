import React, { useEffect } from "react";
import SideNavBar from "./SideNavBar";
import { useDispatch, useSelector } from "react-redux";
import UseRouterAuthentication from "../../Hooks/UseRouterAuthentication";
import TopNavBarMobileScreen from "./TopNavBarMobileScreen";

const DashBoardTemplate = ({ children }) => {
  const master = useSelector((state) => {
    return state.master;
  });
  const dispatch = useDispatch();

  const {checkAuthentication} = UseRouterAuthentication();

  useEffect(() => {
    let auth = master?.auth;
    checkAuthentication("Auth");
  }, []);

  return (
    <>
      <div className=" w-full  h-screen md:flex hidden flex-row ">
        <SideNavBar />
        <div className=" flex-1 overflow-hidden ">{children}</div>
      </div>

      <div className=" w-full h-screen md:hidden flex-shrink-0 flex flex-col gap-[0.5rem]">
        <TopNavBarMobileScreen />
        <div className=" flex-1 ">{children}</div>
      </div>
    </>
  );
};

export default DashBoardTemplate;
