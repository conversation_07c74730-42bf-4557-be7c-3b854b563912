import React from "react";

// UI Components
import ButtonComponent from "../../BasicUIElements/ButtonComponent";

// Slice Actions
import { MasterSliceActions } from "../../Store/MasterSlice/MasterSlice";

// Hooks and Custom Hook
import { NavLink, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import UseRouterAuthentication from "../../Hooks/UseRouterAuthentication";

// Helper Function
import { findActive } from "./Utils/IndivSideNavBar";

// Config
import { navBarObjConfig } from "../../Config/NavBarConfig";

// Icons and Svg
import CustomerNavSvg from "../../Assest/SVG/CustomerNavSvg";
import PurchaseNavBarSvg from "../../Assest/SVG/PurchaseNavBarSvg";
import SalesAndMarketingNavBar from "../../Assest/SVG/SalesAndMarketingNavBar";
import ServiceComplaintsSvg from "../../Assest/SVG/ServiceComplaintsSvg";
import OrderNavSvg from "../../Assest/SVG/OrderNavSvg";
import { FaXmark } from "react-icons/fa6";
import { RxHamburgerMenu } from "react-icons/rx";

import { GiMeshNetwork } from "react-icons/gi";
import { IoTicketSharp } from "react-icons/io5";
import { FaTasks } from "react-icons/fa";
import { SiGooglemarketingplatform } from "react-icons/si";
import { FaUserCircle } from "react-icons/fa";
import { FaSitemap } from "react-icons/fa";

const SvgElementForNavBar = ({ isActive = true, name = "" }) => {
  return (
    <>
      {navBarObjConfig.users?.name === name && (
        <FaUserCircle size={32} color={"#44A1A0"} />
      )}

      {navBarObjConfig.purchase?.name === name && (
        <PurchaseNavBarSvg color={"#44A1A0"} />
      )}
      {navBarObjConfig?.salesAndMarketing?.name === name && (
        <SalesAndMarketingNavBar isActive={isActive} />
      )}

      {navBarObjConfig?.marketing?.name === name && (
        <SiGooglemarketingplatform size={32} color={"#44A1A0"} />
      )}

      {navBarObjConfig.orderDetails?.name === name && (
        <OrderNavSvg color={"#44A1A0"} />
      )}
      {navBarObjConfig.customers.name === name && (
        <CustomerNavSvg color={"#44A1A0"} />
      )}

      {navBarObjConfig.networking.name === name && (
        <GiMeshNetwork size={32} color={"#44A1A0"} />
      )}

      {navBarObjConfig.serviceComplaints.name === name && (
        <ServiceComplaintsSvg color={"#44A1A0"} />
      )}

      {navBarObjConfig.ticketing?.name === name && (
        <IoTicketSharp size={32} color={"#44A1A0"} />
      )}

      {navBarObjConfig.taskmanagement?.name === name && (
        <FaTasks size={28} color={"#44A1A0"} />
      )}

      {navBarObjConfig.sap?.name === name && (
        <FaSitemap size={28} color={"#44A1A0"} />
      )}
    </>
  );
};

const HeadingText = ({ location }) => {
  return (
    <>
      {findActive(location.pathname, navBarObjConfig.users?.link) && (
        <h1 className="  font-poppins font-[800] text-[14px] text-[#fff]">
          {navBarObjConfig.users?.name}
        </h1>
      )}
      {findActive(location.pathname, navBarObjConfig.purchase?.link) && (
        <h1 className="  font-poppins font-[800] text-[14px] text-[#fff]">
          {navBarObjConfig.purchase.name}
        </h1>
      )}
      {findActive(
        location.pathname,
        navBarObjConfig.salesAndMarketing?.link
      ) && (
        <h1 className="  font-poppins font-[800] text-[14px] text-[#fff]">
          {navBarObjConfig.salesAndMarketing.name}
        </h1>
      )}
      {findActive(location.pathname, navBarObjConfig.marketing?.link) && (
        <h1 className="  font-poppins font-[800] text-[14px] text-[#fff]">
          {navBarObjConfig.marketing.name}
        </h1>
      )}
      {findActive(location.pathname, navBarObjConfig.orderDetails?.link) && (
        <h1 className="  font-poppins font-[800] text-[14px] text-[#fff]">
          {navBarObjConfig.orderDetails.name}
        </h1>
      )}
      {findActive(location.pathname, navBarObjConfig.customers?.link) && (
        <h1 className="  font-poppins font-[800] text-[14px] text-[#fff]">
          {navBarObjConfig.customers?.name}
        </h1>
      )}
      {findActive(
        location.pathname,
        navBarObjConfig.serviceComplaints?.link
      ) && (
        <h1 className="  font-poppins font-[800] text-[14px] text-[#fff]">
          {navBarObjConfig.serviceComplaints?.name}
        </h1>
      )}
      {findActive(location.pathname, navBarObjConfig.ticketing?.link) && (
        <h1 className="  font-poppins font-[800] text-[14px] text-[#fff]">
          {navBarObjConfig.ticketing?.name}
        </h1>
      )}
      {findActive(location.pathname, navBarObjConfig.taskmanagement?.link) && (
        <h1 className="  font-poppins font-[800] text-[14px] text-[#fff]">
          {navBarObjConfig.taskmanagement?.name}
        </h1>
      )}
      {findActive(location.pathname, navBarObjConfig.sap?.link) && (
        <h1 className="  font-poppins font-[800] text-[14px] text-[#fff]">
          {navBarObjConfig.sap?.name}
        </h1>
      )}
    </>
  );
};

const IndivNavListTopNavBar = ({ children, content }) => {
  const dispatch = useDispatch();
  const location = useLocation();

  let isActive = findActive(location.pathname, content?.link);
  return (
    <NavLink
      to={content?.link}
      onClick={() => {
        dispatch(MasterSliceActions.modifyIsMobileScreenOpen(false));
      }}
      className={`w-full min-h-[60px]  ps-[7vw] flex flex-row justify-start items-center gap-[1rem] ${
        isActive ? "bg-[#F1F7FE] " : ""
      }`}
    >
      <>
        <div className=" w-fit h-full justify-center flex flex-col">
          <div className=" w-[45px] h-[45px]  flex flex-row justify-center items-center ">
            <SvgElementForNavBar name={content?.name} />
          </div>
        </div>
        <h1 className="font-[400] font-poppins text-[14px] text-[#019BA2]">
          {content?.name}
        </h1>
      </>
    </NavLink>
  );
};

const TotalListComponent = () => {
  const { routeAuthentication } = UseRouterAuthentication();
  return (
    <div className=" w-full h-screen fixed bg-[#fff] left-0  dropdown duration-300 flex flex-col overflow-auto gap-[1rem] pt-[90px] pb-[20px] z-[10000] ">
      {routeAuthentication("user", "read") && (
        <IndivNavListTopNavBar
          content={navBarObjConfig.users}
        ></IndivNavListTopNavBar>
      )}

      {routeAuthentication("purchase", "read") && (
        <IndivNavListTopNavBar
          content={navBarObjConfig.purchase}
        ></IndivNavListTopNavBar>
      )}

      {routeAuthentication("sales", "read") && (
        <IndivNavListTopNavBar
          content={navBarObjConfig.salesAndMarketing}
        ></IndivNavListTopNavBar>
      )}

      {routeAuthentication("marketing", "read") && (
        <IndivNavListTopNavBar
          content={navBarObjConfig.marketing}
        ></IndivNavListTopNavBar>
      )}

      {routeAuthentication("orders", "read") && (
        <IndivNavListTopNavBar
          content={navBarObjConfig.orderDetails}
        ></IndivNavListTopNavBar>
      )}

      {routeAuthentication("customers", "read") && (
        <IndivNavListTopNavBar
          content={navBarObjConfig.customers}
        ></IndivNavListTopNavBar>
      )}

      {routeAuthentication("networking", "read") && (
        <IndivNavListTopNavBar
          content={navBarObjConfig?.networking}
        ></IndivNavListTopNavBar>
      )}

      {routeAuthentication("serviceComplaints", "read") && (
        <IndivNavListTopNavBar
          content={navBarObjConfig.serviceComplaints}
        ></IndivNavListTopNavBar>
      )}

      {routeAuthentication("tickets", "read") && (
        <IndivNavListTopNavBar
          content={navBarObjConfig.ticketing}
        ></IndivNavListTopNavBar>
      )}

      {routeAuthentication("taskManagement", "read") && (
        <IndivNavListTopNavBar
          content={navBarObjConfig.taskmanagement}
        ></IndivNavListTopNavBar>
      )}

      {routeAuthentication("sap", "read") && (
        <IndivNavListTopNavBar
          content={navBarObjConfig.sap}
        ></IndivNavListTopNavBar>
      )}
    </div>
  );
};

const TopNavBarMobileScreen = () => {
  const dispatch = useDispatch();

  const ui = useSelector((state) => {
    return state.master.ui;
  });

  const location = useLocation();

  return (
    <div className=" w-full h-fit flex flex-row gap-[4vw] bg-mainLinearGradient flex-shrink-0  z-[20] relative ">
      <div className=" w-full h-[75px]"></div>
      <div className=" w-full h-[75px] flex flex-row gap-[4vw] bg-mainLinearGradient flex-shrink-0 ps-6 pe-6 z-[10001] fixed">
        <div className=" w-fit h-full flex flex-col justify-center">
          <ButtonComponent
            className=" w-[40px] h-[40px] duration-300 "
            onClick={() => {
              dispatch(
                MasterSliceActions.modifyIsMobileScreenOpen(
                  !ui.isMobileScreenOpen
                )
              );
            }}
          >
            {ui.isMobileScreenOpen ? (
              <FaXmark size={30} color={"grey"} />
            ) : (
              <RxHamburgerMenu size={30} color={"white"} />
            )}
          </ButtonComponent>
        </div>
        <div className=" w-fit h-full flex flex-col justify-center">
          <HeadingText location={location} />
        </div>
      </div>

      {ui.isMobileScreenOpen && <TotalListComponent />}
    </div>
  );
};

export default TopNavBarMobileScreen;
