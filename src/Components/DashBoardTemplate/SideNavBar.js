import React from "react";
import DharanLogoSideNav from "./Utils/DharanLogoSideNav";
import IndivSideNavBar from "./Utils/IndivSideNavBar";
import ButtonComponent from "../../BasicUIElements/ButtonComponent";
import { ExitConfig, navBarObjConfig } from "../../Config/NavBarConfig";
import { MdArrowBackIos } from "react-icons/md";
import UseRouterAuthentication from "../../Hooks/UseRouterAuthentication";
import CustomerNavSvg from "../../Assest/SVG/CustomerNavSvg";
import { useDispatch, useSelector } from "react-redux";
import {
  MasterSliceActions,
  Master_Roles,
} from "../../Store/MasterSlice/MasterSlice";

const SideNavBar = () => {
  const { ui, auth } = useSelector((state) => {
    return state.master;
  });

  const { isExtended } = ui;
  const dispatch = useDispatch();

  const { routeAuthentication } = UseRouterAuthentication();

  return (
    <>
      <div
        className={` relative  ${
          isExtended ? "w-[254px]" : "w-[88px]"
        } h-full bg-mainLinearGradient pt-[10px] flex flex-col justify-between duration-300 `}
      >
        <ButtonComponent
          onClick={() => {
            dispatch(MasterSliceActions.modifyIsExtended(!isExtended));
          }}
          className={` absolute w-[30px] h-[30px] rounded-[50%]  ${
            !isExtended ? " rotate-180" : " rotate-0"
          } duration-300 bg-mainLinearGradient bg-opacity-10 flex flex-row justify-center items-center right-[-15px] z-[1500] top-[5rem]`}
        >
          <MdArrowBackIos color="#2422207A" />
        </ButtonComponent>

        <div className=" w-full flex flex-col overflow-y-auto">
          <DharanLogoSideNav />

          <div className=" w-full h-fit flex flex-col items-center gap-[0.5rem] mt-[1rem]">
            {routeAuthentication("user", "read") && (
              <IndivSideNavBar
                isExtended={isExtended}
                content={navBarObjConfig.users}
              >
                <CustomerNavSvg />
              </IndivSideNavBar>
            )}

            {routeAuthentication("purchase", "read") && (
              <IndivSideNavBar
                isExtended={isExtended}
                content={navBarObjConfig.purchase}
              ></IndivSideNavBar>
            )}

            {routeAuthentication("sales", "read") && (
              <IndivSideNavBar
                isExtended={isExtended}
                content={navBarObjConfig.salesAndMarketing}
              ></IndivSideNavBar>
            )}

            {routeAuthentication("marketing", "read") && (
              <IndivSideNavBar
                isExtended={isExtended}
                content={navBarObjConfig?.marketing}
              ></IndivSideNavBar>
            )}

            {routeAuthentication("orders", "read") && (
              <IndivSideNavBar
                isExtended={isExtended}
                content={navBarObjConfig.orderDetails}
              ></IndivSideNavBar>
            )}

            {routeAuthentication("customers", "read") && (
              <IndivSideNavBar
                isExtended={isExtended}
                content={navBarObjConfig.customers}
              ></IndivSideNavBar>
            )}

            {routeAuthentication("networking", "read") && (
              <IndivSideNavBar
                isExtended={isExtended}
                content={navBarObjConfig?.networking}
              ></IndivSideNavBar>
            )}

            {routeAuthentication("serviceComplaints", "read") && (
              <IndivSideNavBar
                isExtended={isExtended}
                content={navBarObjConfig.serviceComplaints}
              ></IndivSideNavBar>
            )}

            {routeAuthentication("tickets", "read") && (
              <IndivSideNavBar
                isExtended={isExtended}
                content={navBarObjConfig.ticketing}
              ></IndivSideNavBar>
            )}

            {routeAuthentication("taskManagement", "read") && (
              <IndivSideNavBar
                isExtended={isExtended}
                content={navBarObjConfig.taskmanagement}
              ></IndivSideNavBar>
            )}

            {routeAuthentication("sap", "read") && (
              <IndivSideNavBar
                isExtended={isExtended}
                content={navBarObjConfig.sap}
              ></IndivSideNavBar>
            )}
          </div>
        </div>
        {/* <div>
                         <IndivSideNavBar content={ExitConfig} />
                    </div> */}
      </div>
    </>
  );
};

export default SideNavBar;
