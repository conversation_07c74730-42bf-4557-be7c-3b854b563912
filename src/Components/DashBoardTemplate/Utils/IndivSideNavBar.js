import React, { useMemo } from "react";

// Hooks
import { NavLink, useLocation } from "react-router-dom";

// Configs
import { navBarObjConfig } from "../../../Config/NavBarConfig";

// Side nav bar -> SVGs
import CustomerNavSvg from "../../../Assest/SVG/CustomerNavSvg";
import PurchaseNavBarSvg from "../../../Assest/SVG/PurchaseNavBarSvg";
import SalesAndMarketingNavBar from "../../../Assest/SVG/SalesAndMarketingNavBar";
import OrderNavSvg from "../../../Assest/SVG/OrderNavSvg";
import ServiceComplaintsSvg from "../../../Assest/SVG/ServiceComplaintsSvg";

import { GiMeshNetwork } from "react-icons/gi";
import { IoTicketSharp } from "react-icons/io5";
import { FaTasks } from "react-icons/fa";
import { SiGooglemarketingplatform } from "react-icons/si";
import { FaUserCircle } from "react-icons/fa";
import { FaSitemap } from "react-icons/fa";

// Find whether the currently active link is on this dashboard
// Example : current = "/dashboard/salesandmarketing/insights" navlink = "/dashboard/salesandmarketing" -- True
// logic : Is navlink string a sub string of current string
export const findActive = (current = "", navlink = "") => {
  let string = "";
  for (let i = 0; i < current.length; i++) {
    string = string + current[i];
    if (string === navlink) {
      return true;
    }
  }
  return false;
};

// Svg handler for each route / dashboard
const SvgElementForNavBar = ({ isActive = false, name = "" }) => {
  let activeColor = "#44A1A0";
  let inActiveColor = "white";
  return (
    <>
      {/* User Management Dashboard SVG */}
      {navBarObjConfig.users?.name === name && (
        <FaUserCircle size={32} color={isActive ? activeColor : inActiveColor} />
      )}

      {/* Purchase Dashboard SVG */}
      {navBarObjConfig.purchase?.name === name && (
        <PurchaseNavBarSvg color={isActive ? activeColor : inActiveColor} />
      )}

      {/* Sales dashboard SVG */}
      {navBarObjConfig.salesAndMarketing.name === name && (
        <SalesAndMarketingNavBar isActive={isActive} />
      )}

      {/* Marketing dashboard SVG */}
      {navBarObjConfig.marketing.name === name && (
        <SiGooglemarketingplatform
          size={32}
          color={isActive ? activeColor : inActiveColor}
        />
      )}

      {/* Order Management dashboard SVG */}
      {navBarObjConfig.orderDetails?.name === name && (
        <OrderNavSvg color={isActive ? activeColor : inActiveColor} />
      )}

      {/* Customer Dashboard SVG */}
      {navBarObjConfig.customers.name === name && (
        <CustomerNavSvg color={isActive ? activeColor : inActiveColor} />
      )}

      {/* Networking Dashboard */}
      {navBarObjConfig.networking.name === name && (
        <GiMeshNetwork
          size={32}
          color={isActive ? activeColor : inActiveColor}
        />
      )}

      {/* Service Complaints dashboard SVG */}
      {navBarObjConfig.serviceComplaints.name === name && (
        <ServiceComplaintsSvg color={isActive ? activeColor : inActiveColor} />
      )}

      {/* Tickets Dashboard SVG */}
      {navBarObjConfig.ticketing.name === name && (
        <IoTicketSharp
          size={32}
          color={isActive ? activeColor : inActiveColor}
        />
      )}

      {/* Task Management dashboard SVG */}
      {navBarObjConfig.taskmanagement.name === name && (
        <FaTasks
          size={28}
          color={isActive ? activeColor : inActiveColor}
        />
      )}

      {/* SAP Dashboard SVG */}
      {navBarObjConfig.sap.name === name && (
        <FaSitemap
          size={28}
          color={isActive ? activeColor : inActiveColor}
        />
      )}
    </>
  );
};

const IndivSideNavBar = ({ content = {}, children, isExtended = false }) => {
  const location = useLocation();

  let isActive = useMemo(() => {
    return findActive(location.pathname, content?.link);
  }, [location.pathname]);

  return (
    <>
      {isExtended ? (
        <div className=" w-full h-fit flex flex-row justify-start ps-2">
          <NavLink
            to={content?.link}
            className={` w-[90%] h-fit flex flex-row items-center ps-2 pe-2 pt-1 pb-1 ${
              isActive ? "bg-[#BDE0DD]" : ""
            } rounded-[10px]`}
          >
            <div className=" cursor-pointer w-[56px] h-[56px] flex flex-row justify-center items-center  rounded-[12px]">
              <div className="">
                <SvgElementForNavBar isActive={isActive} name={content?.name} />
              </div>
            </div>
            <div
              className={`font-[700] font-lato text-[18px] ${
                isActive ? "text-[#44A1A0]" : " text-[white]"
              }`}
            >
              {content?.name}
            </div>
          </NavLink>
        </div>
      ) : (
        <div className=" w-full h-fit flex flex-row justify-center">
          <NavLink
            to={content?.link}
            className={` cursor-pointer w-[56px] h-[56px] flex flex-row justify-center ${
              isActive ? "bg-[#BDE0DD]" : ""
            } items-center  rounded-[12px]`}
          >
            <div className="">
              <SvgElementForNavBar isActive={isActive} name={content?.name} />
            </div>
          </NavLink>
        </div>
      )}
    </>
  );
};

export default IndivSideNavBar;
