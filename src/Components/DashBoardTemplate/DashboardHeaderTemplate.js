import React, { useState } from "react";
import SelectComponent from "../../BasicUIElements/SelectComponent";
import notificaton from "../../Assest/Utils/Notification.png";
import profile from "../../Assest/Logo/profile.png";
import ProfileComponent from "../../BasicUIElements/ProfileComponent";

const opt = [
     {
          id: "",
          content: "Dharan Natural Stone park",
     },
     {
          id: "",
          content: "Dharan Analytics",
     },
     {
          id: "",
          content: "Dharan Analytics",
     },
     {
          id: "",
          content: "Dharan Analytics",
     },
];

const DashboardHeaderTemplate = ({ heading = "" }) => {
 

     return (
          <div className=" w-full h-[60px]  flex-row justify-between ps-4 pe-6 border-b-2 md:flex hidden flex-shrink-0">
               <div className=" w-fit h-full text-dashboardHeaderTextColor font-[500] text-[20px] font-poppins flex flex-col justify-center">
                    {heading}
               </div>

               <div className=" w-fit h-full flex flex-row gap-[20px]">
                    {/* <div className=" w-fit  h-full flex flex-col justify-center ">
                         <SelectComponent
                              className=" w-[240px] h-[35px] rounded-[6px]  bg-[#F0F6FE] "
                              textClassName="text-[14px]  font-inter font-[400]"
                              value={state}
                              updateFunction={setState}
                              optionarr={opt}
                         />
                    </div>

                    <div className=" w-fit h-full flex flex-col justify-center">
                         <div className=" w-[24px] h-[24px] ">
                              <img
                                   src={notificaton}
                                   alt={"Notification"}
                                   className=" w-full h-full object-contain"
                              />
                         </div>
                    </div>

                    <div className=" w-fit h-full flex flex-col justify-center">
                         <ProfileComponent
                              className=" w-[32px] h-[32px] rounded-[8px] cursor-pointer"
                              content={{ image: profile, name: "profile" }}
                         />
                    </div> */}
               </div>
          </div>
     );
};

export default DashboardHeaderTemplate;
