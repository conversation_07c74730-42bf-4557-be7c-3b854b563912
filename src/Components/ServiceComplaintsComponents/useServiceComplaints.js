import React from "react";
import { API_ENDPOINTS, api_headers, BaseURL } from "../../API/APIConfig";
import { useDispatch, useSelector } from "react-redux";
import { GetAPI } from "../../API/GetAPI";
import { ServiceComplaintsSliceActions } from "../../Store/ServiceComplaintsSlice/ServiceComplaintSlice";
import { parser_table_filter_data } from "../../UI_Enginee/TableHeaderEnginee/TableHeaderEnginee";
import { serviceComplaintsTableConfig } from "../../Config/ServiceComplaintsTableConfig";
import { PostAPI } from "../../API/PostAPI";

export let dateObj = [
  {
    id: "Last year",
    content: "Last year",
  },
  {
    id: "Last Month",
    content: "Last Month",
  },
  {
    id: "Last Week",
    content: "Last Week",
  },
  {
    id: "Custom Date",
    content: "Custom Date",
  },
  {
    id: "Till now",
    content: "Till now",
  },
];

const useServiceComplaints = () => {
  const dispatch = useDispatch();

  const { auth } = useSelector((state) => {
    return state?.master;
  });

  const { filter } = useSelector((state) => {
    return state?.serviceComplaints;
  });

  // fetch_list
  const fetch_list = (updated_filter_state = null, updated_date = null) => {
    if (!auth?.id) {
      return;
    }

    let filterObj = parser_table_filter_data(
      serviceComplaintsTableConfig,
      filter.state
    );
    let filterState = filterObj?.state;
    let isFilter = filterObj?.isFilter;

    let customDate = "Custom Date";
    let startDate = filter?.date?.startDate;
    let endDate = filter?.date?.endDate;
    let filter_options = filter?.date?.filter?.content;

    if (updated_filter_state) {
      let filterState_parser = parser_table_filter_data(
        serviceComplaintsTableConfig,
        updated_filter_state
      );
      filterState = filterState_parser?.state;
      isFilter = filterState_parser?.isFilter;
    }

    if (updated_date) {
      startDate = updated_date?.startDate;
      endDate = updated_date?.endDate;
      filter_options = updated_date?.filter?.content;
    }

    // console.log("----------------------------");
    // console.log(filterState, isFilter);
    // console.log(startDate, "---", endDate, "---", filter_options);

    if (isFilter) {
      dispatch(serviceComplaints_filter_list_thunk(filterState, auth));
    } else {
      dispatch(serviceComplaints_list_fetch_thunk(auth));
    }
    // Header Count
    dispatch(load_header_data_thunk(auth));
  };

  const fetchList = () => {
    if (!auth?.id) {
      return;
    }

    fetch_list();

    // let filterObj = parser_table_filter_data(
    //   serviceComplaintsTableConfig,
    //   filter.state
    // );

    // if (filterObj?.isFilter) {
    //   dispatch(serviceComplaints_filter_list_thunk(filterObj?.state, auth));
    // } else {
    //   dispatch(serviceComplaints_list_fetch_thunk(auth));
    // }

    // // Header Count
    // dispatch(load_header_data_thunk(auth));
  };

  const fetchListWithNewFilter = (filterObj) => {
    if (!auth?.id) {
      return;
    }

    window.alert(
      "Bug in the service complaints dashboard filter. Please connect with developer "
    );

    // dispatch(serviceComplaints_filter_list_thunk(filterObj, auth));
  };

  return { fetch_list, fetchList, fetchListWithNewFilter };
};

export default useServiceComplaints;

const serviceComplaints_filter_list_thunk = (filter, auth) => {
  return async (dispatch) => {
    let url = new URL(
      BaseURL?.mainURl + API_ENDPOINTS?.serviceComplaintListingWithFilter
    );

    url.searchParams.set("userId", auth?.id);

    let headers = { ...api_headers, userId: auth?.id };

    let body = filter;

    let response = await PostAPI(url, body, headers);

    if (response?.ok) {
      let responsedata = await response?.json();

      if (responsedata?.length > 0) {
        if (responsedata[0] === "No Data Found") {
          responsedata = [];
        }
      }

      let array = responsedata?.data;
      if (responsedata?.data?.raisedBy) {
        array = responsedata?.data?.raisedBy?.reverse();
      }else{
        array = array.reverse()
      }

      dispatch(
        ServiceComplaintsSliceActions?.updateServiceComplaintsList(array)
      );
    }
  };
};

const serviceComplaints_list_fetch_thunk = (auth) => {
  return async (dispatch) => {
    let url = new URL(BaseURL.mainURl + API_ENDPOINTS.serviceComplaintListing);

    url.searchParams.set("userId", auth.id);

    let headers = { ...api_headers };

    let response = await GetAPI(url, headers);

    if (response?.ok) {
      let responsedata = await response?.json();

      if (responsedata?.code) {
        // Error
        return [];
      }

      let array = responsedata;

      if (
        responsedata?.raisedBy?.length >= 0 ||
        responsedata?.raisedTo?.length >= 0
      ) {
        array = [...responsedata?.raisedBy, ...responsedata?.raisedTo];
      }

      dispatch(
        ServiceComplaintsSliceActions?.updateServiceComplaintsList(
          array?.reverse()
        )
      );
    }
  };
};

// Load Header Data

const load_header_data_thunk = (auth) => {
  return async (dispatch) => {
    let url = new URL(
      BaseURL?.mainURl + API_ENDPOINTS?.serviceComplaintsHeaderCount
    );

    url.searchParams.set("userId", auth?.id);

    let headers = { ...api_headers };

    let response = await GetAPI(url, headers);

    if (response?.ok) {
      let responseData = await response?.json();

      let data = [
        { title: "Total", count: responseData?.totalCount },
        { title: "New", count: responseData?.statusCounts?.new },
        { title: "In Progress", count: responseData?.statusCounts?.inProgress },
        { title: "Resolved", count: responseData?.statusCounts?.resolved },
      ];

      dispatch(ServiceComplaintsSliceActions?.updateHeaderCount(data));
    }
  };
};
