import React, { useEffect, useState } from "react";
import ModelHeaderAndRoute from "../../Utils/UtilsComponents/ModelHeaderAndRoute";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";

import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import UseRouterAuthentication from "../../../Hooks/UseRouterAuthentication";
import { routes } from "../../../Config/routesConfig";
import {
  formData_parser,
  formEnginee_edit_Formatee_Structure_and_State,
} from "../../../Dashboard_API_calling_and_formatting/Form_Formate_and_API";
import { API_ENDPOINTS, api_headers, BaseURL } from "../../../API/APIConfig";
import { PostAPI } from "../../../API/PostAPI";
import { ServiceComplaintsSliceActions } from "../../../Store/ServiceComplaintsSlice/ServiceComplaintSlice";
import FormEnginee from "../../../UI_Enginee/Form/FormEnginee";
import useServiceComplaints from "../useServiceComplaints";
import toast from "react-hot-toast";

const ServiceComplaintsEditLayout = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { routeAuthentication } = UseRouterAuthentication();
  const { auth, access } = useSelector((state) => {
    return state.master;
  });

  useEffect(() => {
    if (!routeAuthentication("serviceComplaints", "edit")) {
      navigate(routes?.servicecomplaints?.directLink);
    }
  }, [auth, access]);

  //---------------------------------------------------------------------------

  const { fetch_list } = useServiceComplaints();
  const [searchParams] = useSearchParams();

  const { edit } = useSelector((state) => {
    return state?.serviceComplaints;
  });

  // state
  const [state, setState] = useState({});
  const [isContentLoading, setIsContentLoading] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    let id = searchParams?.get("id");

    let fetch = async () => {
      setIsContentLoading(true);

      let result = await fetch_serviceComplaints_edit_form_with_id(id);

      if (!result) {
        window?.confirm(
          "Error at fetching data, please check out network and try again"
        );
      }

      let ss = formEnginee_edit_Formatee_Structure_and_State(result);
      setState(ss.state);
      dispatch(
        ServiceComplaintsSliceActions.updateEditStructureAndState({
          ...ss,
          id: id,
        })
      );

      setIsContentLoading(false);

      if (ss.structure?.length === 0) {
        return false;
      } else {
        return true;
      }
    };

    fetch();
  }, [edit.id]);

  const updateFunctionOfForm = (key, value) => {
    setState((state) => {
      return { ...state, [key]: { ...state[key], value: value } };
    });
  };

  const closeModal = () => {
    navigate(routes?.servicecomplaints?.directLink);
  };

  const clickHandler = async () => {
    let parser_obj = formData_parser(edit.structure, state);

    setIsLoading(true);

    let obj = parser_obj?.parser_obj;

    if (obj?.complaint_status === "Resolved") {
      obj = {
        ...obj,
        closed_at: new Date(),
      };
    }

    let toast_id = toast.loading("Updating", { position: "top-right" });

    let response = await ServiceComplaintsEdit(
      obj,
      searchParams?.get("id"),
      auth
    );

    if (response?.ok) {
      fetch_list();
      closeModal();
      toast.success("Updated", { id: toast_id });
    } else {
      let responsedata = await response.json();
      toast.error(responsedata?.error, { id: toast_id });
    }
    setIsLoading(false);
  };

  return (
    <div className=" md:w-[424px] w-[340px] max-h-[95vh] h-fit overflow-y-auto bg-[#fff] rounded-[6px] p-4 pb-[4rem]">
      <ModelHeaderAndRoute
        className="w-full h-fit flex flex-row justify-between ps-4 pe-4 "
        title="Edit The Complaint"
        onClick={() => {
          closeModal();
        }}
      />

      <FormEnginee
        form={edit?.structure}
        formStateFunction={updateFunctionOfForm}
        formState={state}
      />

      <div className=" w-full h-fit mt-[1rem]">
        <div className=" w-full h-fit flex flex-row justify-center gap-[1rem]">
          <ButtonComponent
            onClick={() => {
              navigate(-1);
            }}
            className=" w-[45%] h-[58px] rounded-[6px] border-[#00BD94] border-2 bg-clip-text text-transparent bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] flex flex-row justify-center items-center"
          >
            Cancel
          </ButtonComponent>
          <ButtonComponent
            isLoading={isLoading}
            onClick={clickHandler}
            className=" w-[45%] h-[58px] rounded-[6px] bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] text-[#fff] flex flex-row justify-center items-center"
          >
            Edit Complaint
          </ButtonComponent>
        </div>
      </div>
    </div>
  );
};

export default ServiceComplaintsEditLayout;

// Edit FORM Builder API
const fetch_serviceComplaints_edit_form_with_id = async (id) => {
  let url = new URL(
    BaseURL?.mainURl + API_ENDPOINTS?.serviceComplaintsEditFormBuilder
  );

  let headers = { ...api_headers };

  const response = await PostAPI(url, { id: id }, headers);

  if (response?.ok) {
    let responsedata = await response?.json();
    return responsedata?.structure;
  }

  return null;
};

// Edit API
const ServiceComplaintsEdit = async (content, id, auth) => {
  let api = new URL(
    BaseURL.mainURl + API_ENDPOINTS.serviceComplaintEdit + "/" + id
  );
  let obj = {
    ...content,
    closedAt: new Date(),
  };
  const header = {
    ...api_headers,
    Authorization: auth.token,
  };
  let response = await PostAPI(api, obj, header, "PUT");

  return response;
};
