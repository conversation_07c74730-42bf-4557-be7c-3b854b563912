import React, { useEffect, useState } from "react";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import { useNavigate } from "react-router-dom";
import ModelHeaderAndRoute from "../../Utils/UtilsComponents/ModelHeaderAndRoute";

import { useDispatch, useSelector } from "react-redux";
import {
  ServiceComplaintsSliceActions,
  serviceComplaint_add_form,
} from "../../../Store/ServiceComplaintsSlice/ServiceComplaintSlice";

import { API_ENDPOINTS, BaseURL, defaultHeaders } from "../../../API/APIConfig";
import { PostAPI } from "../../../API/PostAPI";
import FormEnginee from "../../../UI_Enginee/Form/FormEnginee";
import { formData_parser } from "../../../Dashboard_API_calling_and_formatting/Form_Formate_and_API";
import UseRouterAuthentication from "../../../Hooks/UseRouterAuthentication";
import { routes } from "../../../Config/routesConfig";
import useServiceComplaints from "../useServiceComplaints";
import toast from "react-hot-toast";
import { vaildator_Form_function } from "../../../UI_Enginee/Form/FormEngineeConfig";

const ServiceComplaintAddNewLayout = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { add } = useSelector((state) => {
    return state.serviceComplaints;
  });
  const { fetch_list } = useServiceComplaints();
  const { routeAuthentication } = UseRouterAuthentication();
  const { auth, access } = useSelector((state) => {
    return state.master;
  });

  useEffect(() => {
    if (!routeAuthentication("serviceComplaints", "write")) {
      navigate(routes?.servicecomplaints?.directLink);
    }
  }, [auth, access]);

  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (add?.structure?.length === 0) {
      dispatch(serviceComplaint_add_form());
    }
  }, []);

  const updateFunctionOfForm = (key, value) => {
    let updateState = {
      ...add.state,
      [key]: { ...add.state[key], value: value },
    };
    dispatch(ServiceComplaintsSliceActions.updateState(updateState));
  };

  const submitHandler = async () => {
    if (isLoading) {
      return;
    }
    let vaildate = vaildator_Form_function(add.structure, add.state);
    dispatch(ServiceComplaintsSliceActions.updateState(vaildate?.validate_Obj));

    if (!vaildate?.isFormValid) {
      toast.error("Fill up all fields ", {
        position: "top-right",
      });
      return;
    }

    let parser_obj = formData_parser(add.structure, vaildate?.validate_Obj);

    let obj = {
      ...parser_obj.parser_obj,
      complaint_date: dateFormatFunction(new Date()),
      raised_by: auth?.id,
    };

    setIsLoading(true);
    let toast_id = toast.loading("Posting", { position: "top-right" });
    let response = await addComplaintFunction(obj, auth);
    setIsLoading(false);

    if (response?.ok) {
      toast.success("Added", { id: toast_id });
      fetch_list();
      dispatch(
        ServiceComplaintsSliceActions?.updateAddStructureAndState({
          state: {},
          structure: [],
        })
      );
      closeModal();
    } else {
      let responsedata = await response?.json();
      toast.error(responsedata?.error, { id: toast_id });
    }
  };

  const closeModal = () => {
    navigate(routes?.servicecomplaints?.directLink);
  };

  return (
    <div className=" md:w-[424px] w-[340px] max-h-[95vh] h-fit overflow-y-auto bg-[#fff] rounded-[6px] p-4 pb-[4rem] ">
      <ModelHeaderAndRoute
        className="w-full h-fit flex flex-row justify-between ps-4 pe-4 "
        title="Add a Complaint"
        onClick={() => {
          closeModal();
        }}
      />

      <FormEnginee
        form={add?.structure}
        formState={add.state}
        formStateFunction={updateFunctionOfForm}
      />

      <div className=" w-full h-fit mt-[1rem]">
        <div className=" w-full h-fit flex flex-row justify-center gap-[1rem]">
          <ButtonComponent
            onClick={() => {
              closeModal();
            }}
            className=" w-[45%] h-[58px] rounded-[6px] border-[#00BD94] border-2 bg-clip-text text-transparent bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] flex flex-row justify-center items-center"
          >
            Cancel
          </ButtonComponent>
          <ButtonComponent
            isLoading={isLoading}
            onClick={submitHandler}
            className=" w-[45%] h-[58px] rounded-[6px] bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] text-[#fff] flex flex-row justify-center items-center"
          >
            Add Complaint
          </ButtonComponent>
        </div>
      </div>
    </div>
  );
};

export default ServiceComplaintAddNewLayout;

const dateFormatFunction = (dt) => {
  let date = new Date(dt);

  let fullyear = date.getFullYear();
  let month =
    date.getMonth() + 1 < 10
      ? "0" + (date.getMonth() + 1)
      : date.getMonth() + 1;

  let dateNO = date.getDate();

  let format = fullyear + "-" + month + "-" + dateNO;

  return format;
};

export const addComplaintFunction = async (body, auth) => {
  let url = new URL(
    BaseURL.mainURl + API_ENDPOINTS.serviceComplaintAddComplaint
  );

  url.searchParams.set("userId", auth.id);

  const bodyObj = body;

  let headers = {
    ...defaultHeaders,
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": ["POST", "GET", "OPTIONS", "DELETE", "PUT"],
    "Access-Control-Allow-Headers": [
      "append",
      "delete",
      "entries",
      "foreach",
      "get",
      "has",
      "keys",
      "set",
      "values",
      "Authorization",
    ],

    redirect: "follow",

    Authorization: auth?.token,
  };

  const response = await PostAPI(url, bodyObj, headers);

  return response;
};
