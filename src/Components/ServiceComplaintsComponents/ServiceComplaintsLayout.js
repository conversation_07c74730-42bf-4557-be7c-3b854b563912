import React, { useEffect } from "react";
import ServiceComplaintsDataBox from "./ServiceComplaintsDataBox";
import ServiceComplaintsTableLayout from "./ServiceComplaintsTable/ServiceComplaintsTableLayout";
import ButtonComponent from "../../BasicUIElements/ButtonComponent";
import { useNavigate } from "react-router-dom";
import { routes } from "../../Config/routesConfig";
import { useDispatch, useSelector } from "react-redux";
import {
  serviceComplaint_add_form,
  ServiceComplaintsSliceActions,
  serviceComplaintWithFilterAPIThunk,
} from "../../Store/ServiceComplaintsSlice/ServiceComplaintSlice";
import UseRouterAuthentication from "../../Hooks/UseRouterAuthentication";
import useServiceComplaints, { dateObj } from "./useServiceComplaints";
import DateFilterTemplateComponent from "../../UI_templates/DateFilterTemplate/DateFilterTemplateComponent";

const ServiceComplaintsLayout = () => {
  const navigate = useNavigate();

  const { fetchList, fetch_list } = useServiceComplaints();
  const { isDashboardAccessable, routeAuthentication } =
    UseRouterAuthentication();
  const { auth, access } = useSelector((state) => {
    return state.master;
  });
  const { headerCount, filter } = useSelector((state) => {
    return state.serviceComplaints;
  });

  useEffect(() => {
    isDashboardAccessable("serviceComplaints");
    //dispatch(serviceComplaintWithFilterAPIThunk(filter, auth));
    // fetchList();
    fetch_list();
  }, [auth, access]);
  return (
    <div className=" w-full h-full overflow-y-auto">
      <div className=" w-full h-[50px] flex flex-row justify-between ps-8 pe-8 items-end">
        <div></div>
        {routeAuthentication("serviceComplaints", "write") && (
          <ButtonComponent
            onClick={() => {
              navigate(
                "?page=" + routes?.servicecomplaintsAddNew?.searchParams
              );
            }}
            className=" w-[205px] h-[36px] bg-mainLinearGradient rounded-[12px] font-inter font-[700] text-[14px] text-[#fff] "
          >
            + Add a New Complaint
          </ButtonComponent>
        )}
      </div>
      <div className=" w-full ps-4">
        <ServiceComplaintsDataBox headerCount={headerCount} />
      </div>

      <DateFilterTemplateComponent
        fetch={fetch_list}
        updateSlice={ServiceComplaintsSliceActions.updateFilterDate}
        dateObj={dateObj}
        customDate="Custom Date"
        date={filter?.date}
      />
      <ServiceComplaintsTableLayout />
    </div>
  );
};

export default ServiceComplaintsLayout;
