import React from "react";
import DataStructureBoxHeader from "../Utils/DataStuctureBoxes/DataStructureBoxHeader";
import HeaderDataEnginee from "../../UI_Enginee/HeaderData/HeaderDataEnginee";

const ServiceComplaintsDataBox = ({ headerCount = {} }) => {
  let eachPerLine = 5;
  let widthPerTitle = 150;
  return (
    <div className=" w-full h-fit flex flex-row flex-shrink-0 overflow-x-auto md:ps-0 md:pe-0 ps-4 pe-4 pb-[1rem]">
      <div
        className={` w-[${
          widthPerTitle * eachPerLine
        }px] h-fit flex flex-col flex-shrink-0 justify-between border-2 p-3 rounded-[10px]`}
      >
        <DataStructureBoxHeader></DataStructureBoxHeader>

        <HeaderDataEnginee headerData={headerCount} eachPerLine={eachPerLine} />
      </div>
    </div>
  );
};

export default ServiceComplaintsDataBox;
