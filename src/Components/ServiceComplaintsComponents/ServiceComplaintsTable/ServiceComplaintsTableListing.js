import React from "react";
import ServiceComplaintsTableIndivRow from "./ServiceComplaintsTableIndivRow";
import { useSelector } from "react-redux";

const ServiceComplaintsTableListing = ({ list = [] }) => {
  return (
    <div className={` w-fit h-fit min-h-[170px] `}>
      {list.map((item) => {
        return <ServiceComplaintsTableIndivRow content={item} />;
      })}
    </div>
  );
};

export default ServiceComplaintsTableListing;
