import React, { useState } from "react";

import ServiceComplaintsTableHeaders from "./ServiceComplaintsTableHeaders";
import ServiceComplaintsTableListing from "./ServiceComplaintsTableListing";
import ServiceComplaintsTableToppers from "./ServiceComplaintsTableToppers";
import ServiceComplaintsTableFilterTab from "./ServiceComplaintsTableFilterTab";
import { useSelector } from "react-redux";

function isSubarrayPresent(str2, str1) {
  // Check if the subArrayString is a substring of fullString
  return str2
    ?.toString()
    ?.toLowerCase()
    ?.includes(str1?.toString()?.toLowerCase());
}

const ServiceComplaintsTableLayout = () => {
  let list = useSelector((state) => {
    return state.serviceComplaints?.serviceComplaintsList;
  });
  const [searchState, setSearchState] = useState("");

  const update = (e) => {
    setSearchState(e);
  };

  list = list?.filter((item) => {
    return isSubarrayPresent(item?.customer_id, searchState);
  });
  return (
    <>
      <ServiceComplaintsTableToppers state={searchState} setState={update} />
      <ServiceComplaintsTableFilterTab />
      <div className=" w-fit h-fit flex flex-col overflow-auto  ">
        <div className=" w-fit h-fit flex flex-col ">
          <ServiceComplaintsTableHeaders />
          <ServiceComplaintsTableListing list={list} />
        </div>
      </div>

      {/* <div className="  flex-col flex-shrink-0 md:hidden flex ">
        <div className=" w-full h-fit flex flex-col overflow-auto  ">
          <ServiceComplaintsTableToppers
            state={searchState}
            setState={update}
          />
          <ServiceComplaintsTableFilterTab />
          <div className=" w-full h-fit flex flex-col ">
            <ServiceComplaintsTableHeaders />
            <ServiceComplaintsTableListing list={list} />
          </div>
        </div>
      </div> */}
    </>
  );
};

export default ServiceComplaintsTableLayout;
