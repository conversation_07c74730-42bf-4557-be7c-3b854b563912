import React, { useState } from "react";
import {
  complaintStatusDropDown_ServiceComplaitns,
  findContentObj,
  selectColorCode_ServiceComplaint,
  serviceComplaintsTableConfig,
} from "../../../Config/ServiceComplaintsTableConfig";
import TableTextContent from "../../Utils/TableStructures/TableTextContent";
import { routes } from "../../../Config/routesConfig";
import TableLinkContent from "../../Utils/TableStructures/TableLinkContent";
import edit from "../../../Assest/Utils/edit.png";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import Spinner from "../../../BasicUIElements/Spinner";
import SelectComponent from "../../../BasicUIElements/SelectComponent";
import { API_ENDPOINTS, BaseURL, defaultHeaders } from "../../../API/APIConfig";
import { PostAPI } from "../../../API/PostAPI";
import { serviceComplaintWithFilterAPIThunk } from "../../../Store/ServiceComplaintsSlice/ServiceComplaintSlice";
import UseRouterAuthentication from "../../../Hooks/UseRouterAuthentication";
import UseUserData from "../../../Hooks/UseUserData";
import useServiceComplaints from "../useServiceComplaints";
import TableList_array_template from "../../../UI_templates/TableList_array_template";

const ServiceComplaintsTableIndivRow = ({ content = {} }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { getUsernameWithid } = UseUserData();
  const { fetch_list } = useServiceComplaints();

  const { routeAuthentication } = UseRouterAuthentication();

  //-------------------------------------------------------------------

  const complaintsDate = content?.complaint_date;
  const typesOfRequest = content?.type_of_request;
  const customerId = content?.customer_id;
  // const OrderID = content?.orderId;
  // const orderDate = content?.orderDate;
  let productDetails = content?.product_details;
  // const batchNo = content?.batchno;
  // const quantityText = content?.quantity;
  const complaintsText = content?.complaint;
  const attendingPerson = getUsernameWithid(content?.attending_person);
  const complaintsStatus = findContentObj(
    content?.complaint_status,
    complaintStatusDropDown_ServiceComplaitns
  );

  const timeTaken =
    content?.complaint_status === "Resolved"
      ? content.time_taken
        ? content?.time_taken?.toFixed(0) + " Days"
        : "-"
      : "-";

  const editClickHandler = () => {
    navigate(
      "?page=" +
        routes?.servicecomplaintsEdit?.searchParams +
        "&id=" +
        content?._id
    );
  };
  const deleteClickHandler = () => {
    navigate(routes.deleteOrderManagement.relativeLink);
  };

  //----------------------------------------------------------------------------------------------------------------

  const [isLoading, setIsLoading] = useState(false);
  const auth = useSelector((state) => {
    return state.master.auth;
  });

  const updateServiceComplaintsEdit = async (e) => {
    setIsLoading(true);

    const response = await serviceComplaint_Edit(
      {
        id: content?._id,
        complaintsStatus: e.content,
      },
      auth.token
    );

    if (response) {
      fetch_list();
    }

    setIsLoading(false);
  };

  //---------------------------------------------------------------------------------------------------------------

  return (
    <div
      className=" w-fit h-[52px] border-b-2 flex flex-row hover:bg-[#F0FEFF]  "
      key={content?._id}
    >
      <div className=" w-[60px] h-full flex flex-row justify-center items-center">
        {routeAuthentication("serviceComplaints", "edit") && (
          <ButtonComponent
            onClick={editClickHandler}
            className=" w-[24px] h-[24px] flex flex-rwo justify-center items-center "
          >
            <img
              src={edit}
              className=" w-full h-full object-contain"
              alt="edit"
            />
          </ButtonComponent>
        )}
      </div>
      <TableTextContent
        size={serviceComplaintsTableConfig[0].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {complaintsDate}
      </TableTextContent>
      <TableTextContent
        size={serviceComplaintsTableConfig[1].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        {typesOfRequest}
      </TableTextContent>

      <TableLinkContent
        size={serviceComplaintsTableConfig[2].className}
        className=" font-[700] font-inter text-[14px] text-[#019BA2] hover:underline-offset-4"
        to={routes.customer.directLink + "/" + customerId}
      >
        {customerId}
      </TableLinkContent>

      <TableTextContent
        size={serviceComplaintsTableConfig[3].className}
        className=" font-[400] font-inter text-[12px] text-[#6E7079] w-full h-full overflow-y-auto pt-1 pb-1 "
      >
        {complaintsText}
      </TableTextContent>
      <TableTextContent
        size={serviceComplaintsTableConfig[4].className}
        className=" font-[400] font-inter text-[12px] text-[#6E7079]  ps-4 "
      >
        {attendingPerson}
      </TableTextContent>
      <TableList_array_template
        list={content?.Brand}
        className={serviceComplaintsTableConfig[5]?.className}
      />
      <TableTextContent
        size={serviceComplaintsTableConfig[6].className}
        className=" font-[400] font-inter text-[14px] text-[#6E7079]"
      >
        <div className="flex flex-row items-center">
          <SelectComponent
            className={` w-[140px] h-[30px] bg-[#019BA229] rounded-[10px]  font-[600] text-[14px]  pe-4 ${selectColorCode_ServiceComplaint(
              complaintsStatus
            )}`}
            value={complaintsStatus}
            optionarr={complaintStatusDropDown_ServiceComplaitns}
            updateFunction={(e) => {
              if (complaintsStatus.content === e.content) return;
              updateServiceComplaintsEdit(e);
            }}
          >
            <>{isLoading && <Spinner />}</>
          </SelectComponent>
        </div>
      </TableTextContent>
      <TableTextContent
        size={serviceComplaintsTableConfig[7].className}
        className=" font-[400] font-inter text-[12px] text-[#6E7079]  ps-4 "
      >
        {timeTaken}
      </TableTextContent>
    </div>
  );
};

export default ServiceComplaintsTableIndivRow;

export const serviceComplaint_Edit = async (content, token) => {
  let obj = {};

  let id = content?.id;

  let complaintsStatus = content?.complaintsStatus;

  if (complaintsStatus) {
    obj = {
      ...obj,
      complaint_status: complaintsStatus,
    };

    if (complaintsStatus === "Resolved") {
      obj = { ...obj, closed_at: new Date() };
    }
  }

  let url = new URL(
    BaseURL.mainURl + API_ENDPOINTS.serviceComplaintEdit + "/" + id
  );

  let headers = {
    ...defaultHeaders,
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": ["POST", "GET", "OPTIONS", "DELETE", "PUT"],
    "Access-Control-Allow-Headers": [
      "append",
      "delete",
      "entries",
      "foreach",
      "get",
      "has",
      "keys",
      "set",
      "values",
      "Authorization",
    ],

    redirect: "follow",

    Authorization: token,
  };

  const response = await PostAPI(url, obj, headers, "PUT");

  if (response?.ok) {
    let data = await response.json();

    return true;
  }

  return false;
};
