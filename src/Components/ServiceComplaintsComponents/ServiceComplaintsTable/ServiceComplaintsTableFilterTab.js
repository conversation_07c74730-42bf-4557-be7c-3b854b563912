import React from "react";
import { useDispatch, useSelector } from "react-redux";
import useServiceComplaints from "../useServiceComplaints";
import { ServiceComplaintsSliceActions } from "../../../Store/ServiceComplaintsSlice/ServiceComplaintSlice";
import { serviceComplaintsTableConfig } from "../../../Config/ServiceComplaintsTableConfig";
import { parser_table_filter_data } from "../../../UI_Enginee/TableHeaderEnginee/TableHeaderEnginee";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";

const Selected = ({ text = "All", onClick }) => {
  return (
    <ButtonComponent
      onClick={onClick}
      className=" w-fit h-[48px] border-b-[4px] border-[#019BA2] flex flex-row justify-center flex-shrink-0 ps-6 pe-6 font-poppins text-[16px] text-[#019BA2] font-[400]"
    >
      {text}
    </ButtonComponent>
  );
};

const Upselected = ({ text = "", onClick }) => {
  return (
    <ButtonComponent
      onClick={onClick}
      className=" w-fit h-[48px] border-b-[4px] flex flex-row justify-center flex-shrink-0  border-opacity-0 ps-6 pe-6 font-poppins text-[16px]  font-[400]"
    >
      {text}
    </ButtonComponent>
  );
};

// Helper Function
const isAll = (content = []) => {
  // If no status and more than one status are selected, then all should light up
  if (content?.length <= 0) {
    return true;
  }
  return false;
};

const isCompleted = (content = [], id) => {
  // If only completed status is picked,
  if (content?.length == 1 && content[0]?.id === id) {
    return true;
  }
  return false;
};

const ServiceComplaintsTableFilterTab = () => {
  const dispatch = useDispatch();
  const { filter } = useSelector((state) => {
    return state.serviceComplaints;
  });

  const { fetchListWithNewFilter, fetch_list } = useServiceComplaints();

  const updateState = (key, value) => {
    let updatedState = {
      ...filter?.state,
      [key]: value,
    };
    dispatch(ServiceComplaintsSliceActions.updateFilterState(updatedState));

    fetch_list(updatedState, null);
    // let filterObj = parser_table_filter_data(
    //   serviceComplaintsTableConfig,
    //   updatedState
    // );

    // if (filterObj?.isFilter) {
    //   fetchListWithNewFilter(filterObj.state);
    // } else {
    //   fetchListWithNewFilter({});
    // }
  };
  return (
    <div className="min-w-full  w-full overflow-auto flex flex-row justify-between ps-6 pe-6 ">
      <div className=" w-fit flex flex-row flex-shrink-0 ">
        {isAll(filter?.state?.complaint_status) ? (
          <Selected onClick={() => {}} text="All" />
        ) : (
          <Upselected
            onClick={() => {
              updateState("complaint_status", []);
            }}
            text="All"
          />
        )}

        {isCompleted(filter?.state.complaint_status, "New") ? (
          <Selected onClick={() => {}} text="New" />
        ) : (
          <Upselected
            onClick={() => {
              updateState("complaint_status", [{ id: "New", content: "New" }]);
            }}
            text="New"
          />
        )}
        {isCompleted(filter?.state.complaint_status, "In Progress") ? (
          <Selected onClick={() => {}} text="In Progress" />
        ) : (
          <Upselected
            onClick={() => {
              updateState("complaint_status", [
                { id: "In Progress", content: "In Progress" },
              ]);
            }}
            text="In Progress"
          />
        )}
        {isCompleted(filter?.state.complaint_status, "Resolved") ? (
          <Selected onClick={() => {}} text="Resolved" />
        ) : (
          <Upselected
            onClick={() => {
              updateState("complaint_status", [
                { id: "Resolved", content: "Resolved" },
              ]);
            }}
            text="Resolved"
          />
        )}
      </div>
    </div>
  );
};

export default ServiceComplaintsTableFilterTab;
