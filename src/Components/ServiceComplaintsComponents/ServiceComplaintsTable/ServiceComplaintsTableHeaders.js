import React from "react";

import { serviceComplaintsTableConfig } from "../../../Config/ServiceComplaintsTableConfig";
import { useDispatch, useSelector } from "react-redux";
import { ServiceComplaintsSliceActions } from "../../../Store/ServiceComplaintsSlice/ServiceComplaintSlice";
import TableHeaderEnginee, {
  parser_table_filter_data,
} from "../../../UI_Enginee/TableHeaderEnginee/TableHeaderEnginee";
import TableHeaderEngineeWrapper from "../../../UI_Enginee/TableHeaderEnginee/TableHeaderEngineeWrapper";
import useServiceComplaints from "../useServiceComplaints";

const ServiceComplaintsTableHeaders = () => {
  const dispatch = useDispatch();
  const { filter } = useSelector((state) => {
    return state.serviceComplaints;
  });

  const { auth, access } = useSelector((state) => {
    return state.master;
  });

  const { fetchListWithNewFilter, fetch_list } = useServiceComplaints();

  const updateState = (key, value) => {
    let updatedState = {
      ...filter?.state,
      [key]: value,
    };
    dispatch(ServiceComplaintsSliceActions.updateFilterState(updatedState));

    fetch_list(updatedState, null);

    // let filterObj = parser_table_filter_data(
    //   serviceComplaintsTableConfig,
    //   updatedState
    // );

    // if (filterObj?.isFilter) {
    //   fetchListWithNewFilter(filterObj.state);
    // } else {
    //   fetchListWithNewFilter({});
    // }
  };

  return (
    <TableHeaderEngineeWrapper className=" w-fit h-[52px] border-b-2 flex flex-row  pe-8 ">
      <div className=" w-[60px] h-full flex flex-row justify-center items-center"></div>

      <TableHeaderEnginee
        table={serviceComplaintsTableConfig}
        state={filter?.state}
        updateState={updateState}
      />
    </TableHeaderEngineeWrapper>
  );
};

export default ServiceComplaintsTableHeaders;
